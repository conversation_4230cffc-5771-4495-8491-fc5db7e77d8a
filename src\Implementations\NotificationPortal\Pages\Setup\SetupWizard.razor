@page "/setup"
@layout EmptyLayout
@inject IApiService ApiService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<PageTitle>Setup - NotificationService</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="setup-container">
    <div class="setup-card glass-effect animate-fade-in">
        <div class="setup-header text-center mb-6">
            <div class="setup-logo mb-4">
                <div class="logo-container gradient-primary">
                    <MudIcon Icon="@Icons.Material.Filled.Notifications" Size="Size.Large" Class="logo-icon" />
                </div>
            </div>
            <MudText Typo="Typo.h3" Class="setup-title text-gradient mb-2">NotificationService</MudText>
            <MudText Typo="Typo.h5" Class="setup-subtitle mb-2">Setup Wizard</MudText>
            <MudText Typo="Typo.body1" Class="mud-text-secondary">
                Configure your notification service for first use
            </MudText>

            @if (_currentStep != SetupStep.CheckingStatus)
            {
                <div class="setup-progress mt-4">
                    <MudProgressLinear Value="@GetProgressPercentage()"
                                       Color="Color.Primary"
                                       Size="Size.Medium"
                                       Class="progress-bar" />
                    <MudText Typo="Typo.caption" Class="mt-2 mud-text-secondary">
                        Step @((int)_currentStep) of 3
                    </MudText>
                </div>
            }
        </div>

        @if (_currentStep == SetupStep.CheckingStatus)
        {
            <div class="setup-step checking-status text-center">
                <div class="status-spinner mb-4">
                    <MudProgressCircular Size="Size.Large"
                                         Indeterminate="true"
                                         Color="Color.Primary"
                                         Class="animate-scale-in" />
                </div>
                <MudText Typo="Typo.h6" Class="mb-2">Checking system status...</MudText>
                <MudText Typo="Typo.body2" Class="mud-text-secondary">
                    Please wait while we verify the server configuration
                </MudText>
                <div class="status-dots mt-4">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
            </div>
        }
        else if (_currentStep == SetupStep.ServerConnection)
        {
            <div class="setup-step server-connection animate-slide-up">
                <MudCard Elevation="0" Class="connection-card">
                    <MudCardHeader Class="card-header-modern">
                        <CardHeaderContent>
                            <div class="d-flex align-center">
                                <div class="step-icon-container mr-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Link" Size="Size.Medium" />
                                </div>
                                <div>
                                    <MudText Typo="Typo.h6" Class="step-title">Connect to Server</MudText>
                                    <MudText Typo="Typo.caption" Class="mud-text-secondary">
                                        Establish connection to your NotificationService instance
                                    </MudText>
                                </div>
                            </div>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent Class="card-content-modern">
                        <MudAlert Severity="Severity.Info"
                                  Class="modern-alert mb-4"
                                  Icon="@Icons.Material.Filled.Info">
                            <div class="alert-content">
                                <MudText Typo="Typo.body2" Class="mb-1">
                                    <strong>System Not Configured</strong>
                                </MudText>
                                <MudText Typo="Typo.caption">
                                    Please connect to your NotificationService server to continue.
                                </MudText>
                            </div>
                        </MudAlert>

                        <MudForm @ref="_serverForm" @bind-IsValid="@_isServerFormValid" Class="modern-form">
                            <div class="form-group mb-4">
                                <MudTextField @bind-Value="_serverUrl"
                                              Label="Server URL"
                                              Variant="Variant.Outlined"
                                              Required="true"
                                              RequiredError="Server URL is required"
                                              FullWidth="true"
                                              Placeholder="http://localhost:5120"
                                              Adornment="Adornment.Start"
                                              AdornmentIcon="@Icons.Material.Filled.Computer"
                                              Class="modern-input" />
                                <MudText Typo="Typo.caption" Class="mud-text-secondary mt-1">
                                    Enter the URL where your NotificationService API is running
                                </MudText>
                            </div>

                        <div class="d-flex justify-end gap-2">
                            <MudButton Variant="Variant.Outlined"
                                       OnClick="TestConnection"
                                       Disabled="@(!_isServerFormValid || _isLoading)"
                                       StartIcon="@Icons.Material.Filled.NetworkCheck">
                                Test Connection
                            </MudButton>
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="ValidateAndConnect"
                                       Disabled="@(!_isServerFormValid || _isLoading)"
                                       StartIcon="@Icons.Material.Filled.CheckCircle">
                                @if (_isLoading)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                }
                                Validate & Connect
                            </MudButton>
                        </div>
                    </MudForm>
                </MudCardContent>
            </MudCard>

            @if (_connectionResult != null)
            {
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2" />
                                Connection Result
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_connectionResult.Success)
                        {
                            <MudAlert Severity="Severity.Success" Class="mb-4">
                                Successfully connected to NotificationService!
                            </MudAlert>
                            
                            <div class="connection-info">
                                <MudText Typo="Typo.subtitle2" Class="mb-2">Server Information:</MudText>
                                <MudSimpleTable Dense="true" Hover="true">
                                    <tbody>
                                        <tr>
                                            <td><strong>Version:</strong></td>
                                            <td>@_connectionResult.Version</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <MudChip Size="Size.Small" Color="Color.Success">
                                                    @_connectionResult.Status
                                                </MudChip>
                                            </td>
                                        </tr>
                                        @if (!string.IsNullOrEmpty(_connectionResult.RootTenantName))
                                        {
                                            <tr>
                                                <td><strong>Root Tenant:</strong></td>
                                                <td>@_connectionResult.RootTenantName</td>
                                            </tr>
                                        }
                                        <tr>
                                            <td><strong>Components:</strong></td>
                                            <td>
                                                @foreach (var component in _connectionResult.ComponentStatus)
                                                {
                                                    <MudChip Size="Size.Small" 
                                                             Color="@(component.Value ? Color.Success : Color.Error)"
                                                             Class="mr-1 mb-1">
                                                        @component.Key
                                                    </MudChip>
                                                }
                                            </td>
                                        </tr>
                                    </tbody>
                                </MudSimpleTable>
                            </div>

                            <div class="d-flex justify-end mt-4">
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Primary"
                                           OnClick="ProceedToLogin"
                                           StartIcon="@Icons.Material.Filled.Login">
                                    Proceed to Login
                                </MudButton>
                            </div>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Error" Class="mb-4">
                                Failed to connect to server: @_connectionResult.Error
                            </MudAlert>
                            
                            <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                Please check the server URL and ensure the NotificationService is running.
                            </MudText>
                        }
                    </MudCardContent>
                </MudCard>
            }
        }
        else if (_currentStep == SetupStep.SystemInitialization)
        {
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
                            Initialize System
                        </MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudAlert Severity="Severity.Warning" Class="mb-4">
                        The server is running but not yet initialized. Complete the setup to begin using the system.
                    </MudAlert>

                    <MudForm @ref="_initForm" @bind-IsValid="@_isInitFormValid">
                        <MudTextField @bind-Value="_rootTenantName"
                                      Label="Organization Name"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="Organization name is required"
                                      Class="mb-4"
                                      FullWidth="true"
                                      Placeholder="My Organization"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Business" />

                        <MudTextField @bind-Value="_adminEmail"
                                      Label="Administrator Email"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="Administrator email is required"
                                      Class="mb-4"
                                      FullWidth="true"
                                      InputType="InputType.Email"
                                      Placeholder="<EMAIL>"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Email" />

                        <MudTextField @bind-Value="_adminPassword"
                                      Label="Administrator Password"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="Administrator password is required"
                                      Class="mb-4"
                                      FullWidth="true"
                                      InputType="@(_showPassword ? InputType.Text : InputType.Password)"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@(_showPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                      OnAdornmentClick="TogglePasswordVisibility" />

                        <MudTextField @bind-Value="_confirmPassword"
                                      Label="Confirm Password"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="Password confirmation is required"
                                      Class="mb-4"
                                      FullWidth="true"
                                      InputType="@(_showPassword ? InputType.Text : InputType.Password)"
                                      Validation="@(new Func<string, string?>(ValidatePasswordConfirmation))" />

                        <div class="d-flex justify-end">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="InitializeSystem"
                                       Disabled="@(!_isInitFormValid || _isLoading)"
                                       StartIcon="@Icons.Material.Filled.RocketLaunch">
                                @if (_isLoading)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                    Initializing...
                                }
                                else
                                {
                                    Initialize System
                                }
                            </MudButton>
                        </div>
                    </MudForm>
                </MudCardContent>
            </MudCard>
        }

        <!-- Footer -->
        <div class="setup-footer mt-6 text-center">
            <MudText Typo="Typo.caption" Class="mud-text-secondary">
                NotificationService v2.0.0 - Modern Plugin-Based Notification Platform
            </MudText>
            <div class="footer-links mt-2">
                <MudLink Href="https://docs.notificationservice.com" Target="_blank" Class="footer-link">
                    Documentation
                </MudLink>
                <span class="mx-2">•</span>
                <MudLink Href="https://github.com/notificationservice" Target="_blank" Class="footer-link">
                    GitHub
                </MudLink>
                <span class="mx-2">•</span>
                <MudLink Href="https://support.notificationservice.com" Target="_blank" Class="footer-link">
                    Support
                </MudLink>
            </div>
        </div>
    </div>
</MudContainer>

@code {
    private enum SetupStep
    {
        CheckingStatus,
        ServerConnection,
        SystemInitialization
    }

    private SetupStep _currentStep = SetupStep.CheckingStatus;
    private bool _isLoading = false;
    private bool _showPassword = false;

    // Server connection form
    private MudForm? _serverForm;
    private bool _isServerFormValid;
    private string _serverUrl = "http://localhost:5120";
    private ConnectionResult? _connectionResult;

    // System initialization form
    private MudForm? _initForm;
    private bool _isInitFormValid;
    private string _rootTenantName = "";
    private string _adminEmail = "";
    private string _adminPassword = "";
    private string _confirmPassword = "";

    protected override async Task OnInitializedAsync()
    {
        await CheckSystemStatus();
    }

    private async Task CheckSystemStatus()
    {
        _currentStep = SetupStep.CheckingStatus;
        _isLoading = true;

        try
        {
            // Try to check setup status from default URL
            var response = await ApiService.GetAsync<dynamic>("/health/setup-status");
            
            if (response != null)
            {
                var isInitialized = response.GetProperty("isInitialized").GetBoolean();
                
                if (isInitialized)
                {
                    // System is already initialized, redirect to login
                    Navigation.NavigateTo("/login");
                }
                else
                {
                    // System exists but not initialized
                    _currentStep = SetupStep.SystemInitialization;
                }
            }
        }
        catch
        {
            // Server not reachable or not configured
            _currentStep = SetupStep.ServerConnection;
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task TestConnection()
    {
        _isLoading = true;
        try
        {
            // Test basic connectivity
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10);
            
            var response = await httpClient.GetAsync($"{_serverUrl}/health/live");
            
            if (response.IsSuccessStatusCode)
            {
                Snackbar.Add("Connection successful!", Severity.Success);
            }
            else
            {
                Snackbar.Add($"Server responded with status: {response.StatusCode}", Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Connection failed: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task ValidateAndConnect()
    {
        _isLoading = true;
        try
        {
            // Update API service base URL
            // In a real implementation, this would be done through configuration
            
            var response = await ApiService.GetAsync<dynamic>("/health/setup-status");
            
            if (response != null)
            {
                _connectionResult = new ConnectionResult
                {
                    Success = true,
                    Version = response.GetProperty("version").GetString() ?? "Unknown",
                    Status = "Connected",
                    RootTenantName = response.TryGetProperty("rootTenantName", out var tenant) ? tenant.GetString() : null,
                    ComponentStatus = new Dictionary<string, bool>()
                };

                if (response.TryGetProperty("componentStatus", out var components))
                {
                    foreach (var component in components.EnumerateObject())
                    {
                        _connectionResult.ComponentStatus[component.Name] = component.Value.GetBoolean();
                    }
                }

                var isInitialized = response.GetProperty("isInitialized").GetBoolean();
                
                if (isInitialized)
                {
                    // System is initialized, can proceed to login
                    await Task.Delay(2000); // Show success message briefly
                    Navigation.NavigateTo("/login");
                }
                else
                {
                    // Need to initialize system
                    _currentStep = SetupStep.SystemInitialization;
                }
            }
        }
        catch (Exception ex)
        {
            _connectionResult = new ConnectionResult
            {
                Success = false,
                Error = ex.Message
            };
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task InitializeSystem()
    {
        _isLoading = true;
        try
        {
            var initRequest = new
            {
                RootTenantName = _rootTenantName,
                AdminEmail = _adminEmail,
                AdminPassword = _adminPassword,
                Configuration = new Dictionary<string, object>()
            };

            var response = await ApiService.PostAsync<dynamic>("/api/setup/initialize", initRequest);
            
            if (response != null)
            {
                Snackbar.Add("System initialized successfully!", Severity.Success);
                await Task.Delay(2000);
                Navigation.NavigateTo($"/login?email={Uri.EscapeDataString(_adminEmail)}");
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Initialization failed: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task ProceedToLogin()
    {
        Navigation.NavigateTo("/login");
    }

    private void TogglePasswordVisibility()
    {
        _showPassword = !_showPassword;
    }

    private string? ValidatePasswordConfirmation(string confirmPassword)
    {
        if (confirmPassword != _adminPassword)
        {
            return "Passwords do not match";
        }
        return null;
    }

    private double GetProgressPercentage()
    {
        return _currentStep switch
        {
            SetupStep.CheckingStatus => 0,
            SetupStep.ServerConnection => 33,
            SetupStep.SystemInitialization => 66,
            _ => 100
        };
    }

    private class ConnectionResult
    {
        public bool Success { get; set; }
        public string Version { get; set; } = "";
        public string Status { get; set; } = "";
        public string? RootTenantName { get; set; }
        public string? Error { get; set; }
        public Dictionary<string, bool> ComponentStatus { get; set; } = new();
    }
}

<style>
    .setup-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-attachment: fixed;
        padding: var(--spacing-lg);
        position: relative;
    }

    .setup-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .setup-card {
        max-width: 600px;
        width: 100%;
        padding: var(--spacing-3xl);
        border-radius: var(--radius-2xl);
        position: relative;
        z-index: 1;
    }

    .setup-header {
        position: relative;
    }

    .setup-logo {
        display: flex;
        justify-content: center;
    }

    .logo-container {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-xl);
        position: relative;
    }

    .logo-container::before {
        content: '';
        position: absolute;
        inset: -2px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--mud-palette-primary), var(--mud-palette-secondary));
        z-index: -1;
    }

    .logo-icon {
        color: white;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    }

    .setup-title {
        font-weight: var(--font-weight-extrabold);
        letter-spacing: -0.02em;
    }

    .setup-subtitle {
        font-weight: var(--font-weight-semibold);
        color: var(--mud-palette-text-secondary);
    }

    .setup-progress {
        max-width: 300px;
        margin: 0 auto;
    }

    .progress-bar {
        border-radius: var(--radius-md);
        height: 8px;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .setup-step {
        margin-bottom: var(--spacing-xl);
    }

    .checking-status {
        padding: var(--spacing-3xl) var(--spacing-lg);
    }

    .status-spinner {
        position: relative;
    }

    .status-dots {
        display: flex;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: var(--mud-palette-primary);
        animation: pulse 1.5s infinite;
    }

    .dot:nth-child(2) {
        animation-delay: 0.5s;
    }

    .dot:nth-child(3) {
        animation-delay: 1s;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 0.3;
            transform: scale(1);
        }
        50% {
            opacity: 1;
            transform: scale(1.2);
        }
    }

    .connection-card {
        border: 1px solid var(--mud-palette-divider-light);
        border-radius: var(--radius-xl);
        overflow: hidden;
    }

    .card-header-modern {
        background: linear-gradient(135deg, var(--mud-palette-background-grey), var(--mud-palette-surface));
        border-bottom: 1px solid var(--mud-palette-divider-light);
        padding: var(--spacing-lg);
    }

    .step-icon-container {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-lg);
        background: var(--mud-palette-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: var(--shadow-md);
    }

    .step-title {
        font-weight: var(--font-weight-semibold);
        margin-bottom: var(--spacing-xs);
    }

    .card-content-modern {
        padding: var(--spacing-xl);
    }

    .modern-alert {
        border-radius: var(--radius-lg);
        border: none;
        box-shadow: var(--shadow-sm);
    }

    .alert-content {
        padding: var(--spacing-sm) 0;
    }

    .modern-form {
        margin-top: var(--spacing-lg);
    }

    .form-group {
        position: relative;
    }

    .modern-input {
        transition: all var(--transition-normal);
    }

    .modern-input:focus-within {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .connection-info .mud-simple-table {
        background: transparent;
        border-radius: var(--radius-lg);
        overflow: hidden;
    }

    .connection-info .mud-simple-table tbody tr:hover {
        background-color: var(--mud-palette-background-grey);
    }

    .setup-footer {
        border-top: 1px solid var(--mud-palette-divider-light);
        padding-top: var(--spacing-lg);
        margin-top: var(--spacing-2xl);
    }

    .footer-links {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .footer-link {
        color: var(--mud-palette-text-secondary);
        text-decoration: none;
        font-size: 0.875rem;
        transition: color var(--transition-fast);
    }

    .footer-link:hover {
        color: var(--mud-palette-primary);
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .setup-container {
            padding: var(--spacing-md);
            align-items: flex-start;
            padding-top: var(--spacing-xl);
        }

        .setup-card {
            padding: var(--spacing-xl);
        }

        .logo-container {
            width: 64px;
            height: 64px;
        }

        .card-content-modern {
            padding: var(--spacing-lg);
        }

        .footer-links {
            flex-direction: column;
            gap: var(--spacing-sm);
        }
    }

    @media (max-width: 480px) {
        .setup-card {
            padding: var(--spacing-lg);
        }

        .setup-title {
            font-size: 1.75rem;
        }

        .setup-subtitle {
            font-size: 1.125rem;
        }
    }

    /* Dark theme adjustments */
    .mud-theme-dark .setup-card {
        background-color: rgba(30, 41, 59, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mud-theme-dark .card-header-modern {
        background: linear-gradient(135deg, rgba(51, 65, 85, 0.8), rgba(30, 41, 59, 0.8));
    }

    .mud-theme-dark .connection-card {
        border-color: rgba(255, 255, 255, 0.1);
    }

    .mud-theme-dark .progress-bar {
        background-color: rgba(0, 0, 0, 0.2);
    }

    /* Accessibility */
    @media (prefers-reduced-motion: reduce) {
        .animate-fade-in,
        .animate-slide-up,
        .animate-scale-in {
            animation: none;
        }

        .dot {
            animation: none;
            opacity: 0.6;
        }

        .modern-input:focus-within {
            transform: none;
        }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
        .setup-card {
            border: 2px solid var(--mud-palette-text-primary);
        }

        .step-icon-container {
            border: 2px solid var(--mud-palette-text-primary);
        }
    }
</style>
