using Microsoft.Extensions.Logging;
using PluginCore.Base;
using PluginCore.Interfaces;
using PluginCore.Models;
using PluginCore.Utilities;
using System.Reflection;
using System.Text.Json;

namespace PluginCore.Services;

/// <summary>
/// Default implementation of the plugin manager.
/// </summary>
public class PluginManager : IPluginManager
{
    private readonly ILogger<PluginManager> _logger;
    private readonly Dictionary<string, LoadedPlugin> _loadedPlugins = new();
    private readonly Dictionary<Type, List<object>> _pluginsByType = new();
    private readonly object _lock = new();

    public PluginManager(ILogger<PluginManager> logger)
    {
        _logger = logger;
    }

    public async Task<OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!Directory.Exists(pluginDirectory))
            {
                return GatewayUtilities.CreateOperationResult(false, $"Plugin directory does not exist: {pluginDirectory}");
            }

            var pluginFiles = Directory.GetFiles(pluginDirectory, "*.dll", SearchOption.AllDirectories);
            var loadedCount = 0;
            var errors = new List<string>();

            foreach (var pluginFile in pluginFiles)
            {
                try
                {
                    var result = await LoadSinglePluginAsync(pluginFile, cancellationToken);
                    if (result.IsSuccess)
                    {
                        loadedCount++;
                    }
                    else
                    {
                        errors.Add($"{Path.GetFileName(pluginFile)}: {result.Message}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to load plugin from {PluginFile}", pluginFile);
                    errors.Add($"{Path.GetFileName(pluginFile)}: {ex.Message}");
                }
            }

            var message = $"Loaded {loadedCount} plugins successfully";
            if (errors.Any())
            {
                message += $". {errors.Count} failed to load.";
            }

            _logger.LogInformation(message);
            return GatewayUtilities.CreateOperationResult(true, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load plugins from directory {PluginDirectory}", pluginDirectory);
            return GatewayUtilities.CreateOperationResult(false, "Failed to load plugins", ex);
        }
    }

    public async Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(pluginPath))
            {
                return GatewayUtilities.CreateOperationResult(false, $"Plugin file does not exist: {pluginPath}");
            }

            return await LoadSinglePluginAsync(pluginPath, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load plugin from {PluginPath}", pluginPath);
            return GatewayUtilities.CreateOperationResult(false, "Failed to load plugin", ex);
        }
    }

    public async Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!Directory.Exists(pluginDirectory))
            {
                return GatewayUtilities.CreateOperationResult(false, $"Plugin directory does not exist: {pluginDirectory}");
            }

            // Search for plugin file by name
            var pluginFiles = Directory.GetFiles(pluginDirectory, "*.dll", SearchOption.AllDirectories)
                .Where(file => Path.GetFileNameWithoutExtension(file).Contains(pluginName, StringComparison.OrdinalIgnoreCase))
                .ToList();

            if (!pluginFiles.Any())
            {
                return GatewayUtilities.CreateOperationResult(false, $"No plugin files found matching name: {pluginName}");
            }

            if (pluginFiles.Count > 1)
            {
                _logger.LogWarning("Multiple plugin files found for {PluginName}, using first match: {PluginFile}",
                    pluginName, pluginFiles.First());
            }

            return await LoadSinglePluginAsync(pluginFiles.First(), cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load plugin by name {PluginName}", pluginName);
            return GatewayUtilities.CreateOperationResult(false, "Failed to load plugin", ex);
        }
    }

    public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType
    {
        lock (_lock)
        {
            if (_pluginsByType.TryGetValue(typeof(T), out var plugins))
            {
                return plugins.Cast<T>().ToList().AsReadOnly();
            }
            return new List<T>().AsReadOnly();
        }
    }

    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType
    {
        lock (_lock)
        {
            if (_loadedPlugins.TryGetValue(pluginName, out var loadedPlugin) && 
                loadedPlugin.Instance is T plugin)
            {
                return plugin;
            }
            return null;
        }
    }

    public IReadOnlyList<PluginManifest> GetPluginManifests()
    {
        lock (_lock)
        {
            return _loadedPlugins.Values
                .Select(p => p.Manifest)
                .ToList()
                .AsReadOnly();
        }
    }

    public PluginManifest? GetPluginManifest(string pluginName)
    {
        lock (_lock)
        {
            return _loadedPlugins.TryGetValue(pluginName, out var plugin) ? plugin.Manifest : null;
        }
    }

    public async Task<OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (!_loadedPlugins.TryGetValue(pluginName, out var loadedPlugin))
            {
                return GatewayUtilities.CreateOperationResult(false, $"Plugin '{pluginName}' not found");
            }

            // Remove from type collections
            var pluginType = loadedPlugin.Instance.GetType();
            var interfaces = pluginType.GetInterfaces().Where(i => typeof(IPluginType).IsAssignableFrom(i));
            
            foreach (var interfaceType in interfaces)
            {
                if (_pluginsByType.TryGetValue(interfaceType, out var plugins))
                {
                    plugins.Remove(loadedPlugin.Instance);
                    if (!plugins.Any())
                    {
                        _pluginsByType.Remove(interfaceType);
                    }
                }
            }

            // Dispose if disposable
            if (loadedPlugin.Instance is IDisposable disposable)
            {
                disposable.Dispose();
            }

            _loadedPlugins.Remove(pluginName);
            
            _logger.LogInformation("Plugin '{PluginName}' unloaded successfully", pluginName);
            return GatewayUtilities.CreateOperationResult(true, $"Plugin '{pluginName}' unloaded successfully");
        }
    }

    public async Task<OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (!_loadedPlugins.TryGetValue(pluginName, out var loadedPlugin))
            {
                return GatewayUtilities.CreateOperationResult(false, $"Plugin '{pluginName}' not found");
            }

            var pluginPath = loadedPlugin.AssemblyPath;
            
            // Unload first
            var unloadResult = UnloadPluginAsync(pluginName, cancellationToken).Result;
            if (!unloadResult.IsSuccess)
            {
                return unloadResult;
            }

            // Reload
            return LoadSinglePluginAsync(pluginPath, cancellationToken).Result;
        }
    }

    public async Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default)
    {
        var statuses = new List<PluginStatus>();
        Dictionary<string, LoadedPlugin> pluginsCopy;

        lock (_lock)
        {
            pluginsCopy = new Dictionary<string, LoadedPlugin>(_loadedPlugins);
        }

        foreach (var (name, loadedPlugin) in pluginsCopy)
        {
            try
            {
                var isHealthy = true;
                var status = "Loaded";
                string? errorMessage = null;

                // For now, just assume plugins are healthy if they're loaded
                // In the future, we can add a health check interface
                status = "Healthy";

                statuses.Add(new PluginStatus(
                    Name: name,
                    Version: loadedPlugin.Manifest.Version,
                    Type: loadedPlugin.Manifest.Type,
                    IsLoaded: true,
                    IsHealthy: isHealthy,
                    Status: status,
                    LastChecked: DateTimeOffset.UtcNow,
                    ErrorMessage: errorMessage
                ));
            }
            catch (Exception ex)
            {
                statuses.Add(new PluginStatus(
                    Name: name,
                    Version: loadedPlugin.Manifest.Version,
                    Type: loadedPlugin.Manifest.Type,
                    IsLoaded: true,
                    IsHealthy: false,
                    Status: "Error",
                    LastChecked: DateTimeOffset.UtcNow,
                    ErrorMessage: ex.Message
                ));
            }
        }

        return statuses.AsReadOnly();
    }

    public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(pluginPath))
            {
                return new ValidationResult { IsValid = false, ErrorMessage = "Plugin file does not exist" };
            }

            // Try to load assembly
            var assembly = Assembly.LoadFrom(pluginPath);
            
            // Look for plugin types
            var pluginTypes = assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && typeof(IPluginType).IsAssignableFrom(t))
                .ToList();

            if (!pluginTypes.Any())
            {
                return new ValidationResult { IsValid = false, ErrorMessage = "No plugin types found in assembly" };
            }

            // Validate each plugin type
            foreach (var pluginType in pluginTypes)
            {
                // Check if it has a parameterless constructor or constructor with ILogger
                var constructors = pluginType.GetConstructors();
                var hasValidConstructor = constructors.Any(c => 
                    c.GetParameters().Length == 0 || 
                    (c.GetParameters().Length == 1 && c.GetParameters()[0].ParameterType.Name.Contains("ILogger")));

                if (!hasValidConstructor)
                {
                    return new ValidationResult 
                    { 
                        IsValid = false, 
                        ErrorMessage = $"Plugin type {pluginType.Name} does not have a valid constructor" 
                    };
                }
            }

            return new ValidationResult { IsValid = true };
        }
        catch (Exception ex)
        {
            return new ValidationResult { IsValid = false, ErrorMessage = ex.Message };
        }
    }

    private async Task<OperationResult> LoadSinglePluginAsync(string pluginPath, CancellationToken cancellationToken)
    {
        try
        {
            // Validate first
            var validation = await ValidatePluginAsync(pluginPath, cancellationToken);
            if (!validation.IsValid)
            {
                return GatewayUtilities.CreateOperationResult(false, validation.ErrorMessage ?? "Plugin validation failed");
            }

            var assembly = Assembly.LoadFrom(pluginPath);
            var pluginTypes = assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && typeof(IPluginType).IsAssignableFrom(t))
                .ToList();

            foreach (var pluginType in pluginTypes)
            {
                try
                {
                    // Create instance
                    var instance = CreatePluginInstance(pluginType);
                    if (instance == null)
                    {
                        continue;
                    }

                    // Get manifest
                    var manifest = await GetPluginManifestAsync(instance);
                    if (manifest == null)
                    {
                        _logger.LogWarning("Plugin {PluginType} does not provide a manifest", pluginType.Name);
                        continue;
                    }

                    // Store plugin
                    var loadedPlugin = new LoadedPlugin(instance, manifest, pluginPath);
                    
                    lock (_lock)
                    {
                        _loadedPlugins[manifest.Name] = loadedPlugin;
                        
                        // Add to type collections
                        var interfaces = pluginType.GetInterfaces().Where(i => typeof(IPluginType).IsAssignableFrom(i));
                        foreach (var interfaceType in interfaces)
                        {
                            if (!_pluginsByType.ContainsKey(interfaceType))
                            {
                                _pluginsByType[interfaceType] = new List<object>();
                            }
                            _pluginsByType[interfaceType].Add(instance);
                        }
                    }

                    _logger.LogInformation("Loaded plugin: {PluginName} v{Version} ({Type})", 
                        manifest.Name, manifest.Version, manifest.Type);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to load plugin type {PluginType}", pluginType.Name);
                }
            }

            return GatewayUtilities.CreateOperationResult(true, "Plugin loaded successfully");
        }
        catch (Exception ex)
        {
            return GatewayUtilities.CreateOperationResult(false, "Failed to load plugin", ex);
        }
    }

    private object? CreatePluginInstance(Type pluginType)
    {
        try
        {
            // Try constructor with ILogger parameter first
            var loggerConstructor = pluginType.GetConstructors()
                .FirstOrDefault(c => c.GetParameters().Length == 1 && 
                                   c.GetParameters()[0].ParameterType.Name.Contains("ILogger"));

            if (loggerConstructor != null)
            {
                var loggerType = typeof(ILogger<>).MakeGenericType(pluginType);
                var logger = _logger; // Use the plugin manager's logger for now
                return Activator.CreateInstance(pluginType, logger);
            }

            // Try parameterless constructor
            var parameterlessConstructor = pluginType.GetConstructors()
                .FirstOrDefault(c => c.GetParameters().Length == 0);

            if (parameterlessConstructor != null)
            {
                return Activator.CreateInstance(pluginType);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create instance of plugin type {PluginType}", pluginType.Name);
            return null;
        }
    }

    private async Task<PluginManifest?> GetPluginManifestAsync(object pluginInstance)
    {
        try
        {
            // Try to get manifest from admin interface
            if (pluginInstance is IGatewayAdminPluginType adminPlugin)
            {
                return await adminPlugin.GetManifestAsync();
            }

            // Fallback: create a basic manifest
            var type = pluginInstance.GetType();
            return new PluginManifest(
                Name: type.Name,
                Version: "1.0.0",
                Description: $"Plugin: {type.Name}",
                Author: "Unknown",
                Type: GetPluginTypeString(type),
                Provider: "Unknown",
                AssemblyName: type.Assembly.GetName().Name ?? "Unknown",
                EntryPoint: type.FullName ?? "Unknown",
                Dependencies: new List<PluginDependency>(),
                Configuration: new Dictionary<string, PluginConfigurationItem>(),
                SupportedFeatures: new List<string>()
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get manifest for plugin {PluginType}", pluginInstance.GetType().Name);
            return null;
        }
    }

    private string GetPluginTypeString(Type type)
    {
        if (typeof(ISmsPlugin).IsAssignableFrom(type)) return "SMS";
        if (typeof(IEmailPlugin).IsAssignableFrom(type)) return "Email";
        if (typeof(IPushPlugin).IsAssignableFrom(type)) return "Push";
        if (typeof(IWebAppPlugin).IsAssignableFrom(type)) return "WebApp";
        return "Unknown";
    }
}

/// <summary>
/// Represents a loaded plugin with its metadata.
/// </summary>
internal record LoadedPlugin(object Instance, PluginManifest Manifest, string AssemblyPath);
