{"sourceFile": "src/Plugins/Plugin.Email.SendGrid/SendGridEmailGateway.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1751210430129, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751210442789, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,11 @@\n public class SendGridEmailGateway : EmailGatewayBase\n {\n     private readonly HttpClient _httpClient;\n     private readonly RateLimitConfig _rateLimitConfig;\n+    private readonly IMessageSchedulingService _schedulingService;\n+    private readonly IMessageStorageService _storageService;\n+    private readonly string _gatewayId;\n     private string? _apiKey;\n     private string? _baseUrl;\n \n     public SendGridEmailGateway(ILogger<SendGridEmailGateway> logger) : base(logger)\n"}, {"date": 1751210457301, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,13 +19,19 @@\n     private readonly string _gatewayId;\n     private string? _apiKey;\n     private string? _baseUrl;\n \n-    public SendGridEmailGateway(ILogger<SendGridEmailGateway> logger) : base(logger)\n+    public SendGridEmailGateway(\n+        ILogger<SendGridEmailGateway> logger,\n+        IMessageSchedulingService? schedulingService = null,\n+        IMessageStorageService? storageService = null) : base(logger)\n     {\n         _httpClient = GatewayUtilities.CreateHttpClient(\"https://api.sendgrid.com/v3/\", TimeSpan.FromSeconds(30));\n         _rateLimitConfig = new RateLimitConfig { MaxRequests = 600, TimeWindow = TimeSpan.FromMinutes(1) }; // SendGrid rate limit\n-        \n+        _schedulingService = schedulingService ?? new DefaultMessageSchedulingService(logger);\n+        _storageService = storageService ?? new DefaultMessageStorageService(logger);\n+        _gatewayId = \"SendGrid\";\n+\n         InitializeConfiguration();\n     }\n \n     private void InitializeConfiguration()\n"}, {"date": 1751210467256, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,10 +26,10 @@\n         IMessageStorageService? storageService = null) : base(logger)\n     {\n         _httpClient = GatewayUtilities.CreateHttpClient(\"https://api.sendgrid.com/v3/\", TimeSpan.FromSeconds(30));\n         _rateLimitConfig = new RateLimitConfig { MaxRequests = 600, TimeWindow = TimeSpan.FromMinutes(1) }; // SendGrid rate limit\n-        _schedulingService = schedulingService ?? new DefaultMessageSchedulingService(logger);\n-        _storageService = storageService ?? new DefaultMessageStorageService(logger);\n+        _schedulingService = schedulingService ?? new DefaultMessageSchedulingService(_logger);\n+        _storageService = storageService ?? new DefaultMessageStorageService(_logger);\n         _gatewayId = \"SendGrid\";\n \n         InitializeConfiguration();\n     }\n"}, {"date": 1751210477740, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -456,15 +456,8 @@\n \n         return sendGridPayload;\n     }\n \n-    protected override void Dispose(bool disposing)\n-    {\n-        if (disposing)\n-        {\n-            _httpClient?.Dispose();\n-        }\n-        base.Dispose(disposing);\n-    }\n \n+\n     #endregion\n }\n"}, {"date": 1751210496920, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -343,20 +343,40 @@\n     #region Message Operations (Not implemented in base class)\n \n     public override async Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)\n     {\n-        // SendGrid doesn't support native scheduling, would need to implement with a job scheduler\n-        throw new NotSupportedException(\"Message scheduling is not supported by SendGrid gateway\");\n+        // SendGrid doesn't support native scheduling, so we use our scheduling service\n+        return await _schedulingService.ScheduleMessageAsync(_gatewayId, payload, scheduledTime, cancellationToken);\n     }\n \n     public override async Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)\n     {\n-        throw new NotSupportedException(\"Message scheduling is not supported by SendGrid gateway\");\n+        // Use our scheduling service to cancel the message\n+        return await _schedulingService.CancelScheduledMessageAsync(scheduledMessageId, cancellationToken);\n     }\n \n     public override async Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default)\n     {\n-        throw new NotSupportedException(\"Message resending is not supported by SendGrid gateway\");\n+        // Use our storage service to resend the message\n+        var storedMessage = await _storageService.GetStoredMessageAsync(originalMessageId, cancellationToken);\n+        if (storedMessage == null)\n+        {\n+            return GatewayUtilities.CreateFailureResult(null, \"Original message not found\");\n+        }\n+\n+        // Create a new correlation ID for the resend\n+        var resendPayload = storedMessage.Payload with\n+        {\n+            CorrelationId = $\"resend_{originalMessageId}_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}\"\n+        };\n+\n+        // Send the message again\n+        var sendResult = await SendEmailInternalAsync(resendPayload, cancellationToken);\n+\n+        // Update the storage with the resend result\n+        await _storageService.UpdateSendResultAsync(originalMessageId, sendResult, cancellationToken);\n+\n+        return sendResult;\n     }\n \n     public override async Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)\n     {\n"}, {"date": 1751210511798, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -68,9 +68,14 @@\n                 \n                 if (response.IsSuccessStatusCode)\n                 {\n                     var messageId = response.Headers.GetValues(\"X-Message-Id\").FirstOrDefault() ?? Guid.NewGuid().ToString();\n-                    return GatewayUtilities.CreateSuccessResult(messageId, payload.CorrelationId);\n+                    var result = GatewayUtilities.CreateSuccessResult(messageId, payload.CorrelationId);\n+\n+                    // Store the message for potential resending\n+                    await _storageService.StoreMessageAsync(messageId, _gatewayId, payload, result, cancellationToken);\n+\n+                    return result;\n                 }\n                 else\n                 {\n                     var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);\n"}, {"date": 1751211337085, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,7 @@\n using Microsoft.Extensions.Logging;\n using PluginCore.Base;\n+using PluginCore.Interfaces;\n using PluginCore.Models;\n using PluginCore.Utilities;\n using System.Text.Json;\n using System.Text;\n"}, {"date": 1751211768791, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,9 +10,9 @@\n \n /// <summary>\n /// SendGrid email gateway implementation with scheduling and storage capabilities.\n /// </summary>\n-public class SendGridEmailGateway : EmailGatewayBase\n+public class SendGridEmailGateway : EmailGatewayBase, IEmailPlugin\n {\n     private readonly HttpClient _httpClient;\n     private readonly RateLimitConfig _rateLimitConfig;\n     private readonly IMessageSchedulingService _schedulingService;\n"}], "date": 1751210430129, "name": "Commit-0", "content": "using Microsoft.Extensions.Logging;\nusing PluginCore.Base;\nusing PluginCore.Models;\nusing PluginCore.Utilities;\nusing System.Text.Json;\nusing System.Text;\n\nnamespace Plugin.Email.SendGrid;\n\n/// <summary>\n/// SendGrid email gateway implementation with scheduling and storage capabilities.\n/// </summary>\npublic class SendGridEmailGateway : EmailGatewayBase\n{\n    private readonly HttpClient _httpClient;\n    private readonly RateLimitConfig _rateLimitConfig;\n    private string? _apiKey;\n    private string? _baseUrl;\n\n    public SendGridEmailGateway(ILogger<SendGridEmailGateway> logger) : base(logger)\n    {\n        _httpClient = GatewayUtilities.CreateHttpClient(\"https://api.sendgrid.com/v3/\", TimeSpan.FromSeconds(30));\n        _rateLimitConfig = new RateLimitConfig { MaxRequests = 600, TimeWindow = TimeSpan.FromMinutes(1) }; // SendGrid rate limit\n        \n        InitializeConfiguration();\n    }\n\n    private void InitializeConfiguration()\n    {\n        // Set default configuration\n        SetConfiguration(\"ApiKey\", \"\");\n        SetConfiguration(\"DefaultSender\", \"<EMAIL>\");\n        SetConfiguration(\"DefaultSenderName\", \"Notification Service\");\n        SetConfiguration(\"EnableHtml\", true);\n        SetConfiguration(\"EnableAttachments\", true);\n        SetConfiguration(\"EnableTracking\", true);\n        SetConfiguration(\"EnableClickTracking\", false);\n        SetConfiguration(\"EnableOpenTracking\", false);\n    }\n\n    #region EmailGatewayBase Implementation\n\n    protected override async Task<MessageSendResult> SendEmailInternalAsync(MessagePayload payload, CancellationToken cancellationToken)\n    {\n        try\n        {\n            var rateLimitKey = $\"sendgrid_{_apiKey?[..8]}\";\n            \n            return await new Func<Task<MessageSendResult>>(async () =>\n            {\n                var sendGridPayload = CreateSendGridPayload(payload);\n                var json = JsonSerializer.Serialize(sendGridPayload);\n                var content = new StringContent(json, Encoding.UTF8, \"application/json\");\n\n                _httpClient.DefaultRequestHeaders.Clear();\n                _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Bearer {_apiKey}\");\n\n                var response = await _httpClient.PostAsync(\"mail/send\", content, cancellationToken);\n                \n                if (response.IsSuccessStatusCode)\n                {\n                    var messageId = response.Headers.GetValues(\"X-Message-Id\").FirstOrDefault() ?? Guid.NewGuid().ToString();\n                    return GatewayUtilities.CreateSuccessResult(messageId, payload.CorrelationId);\n                }\n                else\n                {\n                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);\n                    _logger.LogError(\"SendGrid API error: {StatusCode} - {Content}\", response.StatusCode, errorContent);\n                    return GatewayUtilities.CreateFailureResult(payload.CorrelationId, $\"SendGrid API error: {response.StatusCode}\");\n                }\n            }).ExecuteWithRateLimitAsync(rateLimitKey, _rateLimitConfig, _logger, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to send email via SendGrid\");\n            return GatewayUtilities.CreateFailureResult(payload.CorrelationId, ex.Message);\n        }\n    }\n\n    protected override async Task<IReadOnlyList<MessageSendResult>> SendBulkEmailInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken)\n    {\n        var results = new List<MessageSendResult>();\n        \n        // SendGrid doesn't have a native bulk API, so we'll send them individually with rate limiting\n        foreach (var payload in payloads)\n        {\n            var result = await SendEmailInternalAsync(payload, cancellationToken);\n            results.Add(result);\n            \n            // Small delay between requests to respect rate limits\n            await Task.Delay(100, cancellationToken);\n        }\n        \n        return results.AsReadOnly();\n    }\n\n    protected override async Task<EmailTemplate?> GetEmailTemplateAsync(string templateId, CancellationToken cancellationToken)\n    {\n        try\n        {\n            _httpClient.DefaultRequestHeaders.Clear();\n            _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Bearer {_apiKey}\");\n\n            var response = await _httpClient.GetAsync($\"templates/{templateId}\", cancellationToken);\n            \n            if (response.IsSuccessStatusCode)\n            {\n                var content = await response.Content.ReadAsStringAsync(cancellationToken);\n                var templateData = JsonSerializer.Deserialize<JsonElement>(content);\n                \n                // Extract template information from SendGrid response\n                var name = templateData.GetProperty(\"name\").GetString() ?? \"Unknown Template\";\n                var versions = templateData.GetProperty(\"versions\").EnumerateArray();\n                var activeVersion = versions.FirstOrDefault(v => v.GetProperty(\"active\").GetInt32() == 1);\n                \n                if (activeVersion.ValueKind != JsonValueKind.Undefined)\n                {\n                    var subject = activeVersion.GetProperty(\"subject\").GetString() ?? \"\";\n                    var htmlContent = activeVersion.GetProperty(\"html_content\").GetString() ?? \"\";\n                    \n                    return new EmailTemplate(subject, htmlContent);\n                }\n            }\n            \n            return null;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to get SendGrid template {TemplateId}\", templateId);\n            return null;\n        }\n    }\n\n    protected override async Task<IEnumerable<string>> ValidateEmailSpecificAsync(MessagePayload payload, CancellationToken cancellationToken)\n    {\n        var errors = new List<string>();\n        \n        // Validate API key\n        if (string.IsNullOrEmpty(_apiKey))\n        {\n            errors.Add(\"SendGrid API key is not configured\");\n        }\n        \n        // Validate sender\n        var sender = payload.From ?? GetDefaultSender();\n        if (string.IsNullOrEmpty(sender) || !IsValidEmailAddress(sender))\n        {\n            errors.Add(\"Invalid sender email address\");\n        }\n        \n        // Validate content length (SendGrid has limits)\n        if (payload.Content.Length > 1000000) // 1MB limit\n        {\n            errors.Add(\"Email content exceeds SendGrid size limit (1MB)\");\n        }\n        \n        return errors;\n    }\n\n    #endregion\n\n    #region Admin Implementation\n\n    protected override async Task<IEnumerable<GatewayConfiguration>> GetEmailProviderConfigurationsAsync(CancellationToken cancellationToken)\n    {\n        return new[]\n        {\n            new GatewayConfiguration(\"ApiKey\", \"SendGrid API Key\", \"\", \"Your SendGrid API key\", true, true),\n            new GatewayConfiguration(\"EnableTracking\", \"Enable email tracking\", GetConfiguration<bool>(\"EnableTracking\").ToString(), \"Enable SendGrid email tracking\", false, false, \"bool\"),\n            new GatewayConfiguration(\"EnableClickTracking\", \"Enable click tracking\", GetConfiguration<bool>(\"EnableClickTracking\").ToString(), \"Enable SendGrid click tracking\", false, false, \"bool\"),\n            new GatewayConfiguration(\"EnableOpenTracking\", \"Enable open tracking\", GetConfiguration<bool>(\"EnableOpenTracking\").ToString(), \"Enable SendGrid open tracking\", false, false, \"bool\")\n        };\n    }\n\n    protected override async Task UpdateEmailProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken)\n    {\n        switch (setting.Key)\n        {\n            case \"ApiKey\":\n                if (!string.IsNullOrEmpty(setting.Value))\n                {\n                    _apiKey = setting.Value;\n                    SetConfiguration(\"ApiKey\", setting.Value);\n                }\n                break;\n            case \"EnableTracking\":\n                if (bool.TryParse(setting.Value, out var enableTracking))\n                    SetConfiguration(\"EnableTracking\", enableTracking);\n                break;\n            case \"EnableClickTracking\":\n                if (bool.TryParse(setting.Value, out var enableClickTracking))\n                    SetConfiguration(\"EnableClickTracking\", enableClickTracking);\n                break;\n            case \"EnableOpenTracking\":\n                if (bool.TryParse(setting.Value, out var enableOpenTracking))\n                    SetConfiguration(\"EnableOpenTracking\", enableOpenTracking);\n                break;\n        }\n    }\n\n    protected override async Task<OperationResult> TestEmailConfigurationAsync(CancellationToken cancellationToken)\n    {\n        try\n        {\n            if (string.IsNullOrEmpty(_apiKey))\n            {\n                return GatewayUtilities.CreateOperationResult(false, \"API key is required\");\n            }\n\n            // Test API key by making a simple API call\n            _httpClient.DefaultRequestHeaders.Clear();\n            _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Bearer {_apiKey}\");\n\n            var response = await _httpClient.GetAsync(\"user/profile\", cancellationToken);\n            \n            if (response.IsSuccessStatusCode)\n            {\n                return GatewayUtilities.CreateOperationResult(true, \"SendGrid configuration is valid\");\n            }\n            else\n            {\n                return GatewayUtilities.CreateOperationResult(false, $\"SendGrid API test failed: {response.StatusCode}\");\n            }\n        }\n        catch (Exception ex)\n        {\n            return GatewayUtilities.CreateOperationResult(false, \"Configuration test failed\", ex);\n        }\n    }\n\n    protected override async Task<PluginManifest> GetEmailManifestAsync(CancellationToken cancellationToken)\n    {\n        return new PluginManifest(\n            Name: \"SendGrid Email Gateway\",\n            Version: \"1.0.0\",\n            Description: \"Email gateway implementation for SendGrid service\",\n            Author: \"NotificationService Team\",\n            Type: \"Email\",\n            Provider: \"SendGrid\",\n            AssemblyName: \"Plugin.Email.SendGrid.dll\",\n            EntryPoint: \"Plugin.Email.SendGrid.SendGridEmailGateway\",\n            Dependencies: new List<PluginDependency>(),\n            Configuration: new Dictionary<string, PluginConfigurationItem>\n            {\n                [\"ApiKey\"] = new(\"string\", \"SendGrid API Key\", null, null, true, true),\n                [\"EnableTracking\"] = new(\"bool\", \"Enable email tracking\", null, true, false, false),\n                [\"EnableClickTracking\"] = new(\"bool\", \"Enable click tracking\", null, false, false, false),\n                [\"EnableOpenTracking\"] = new(\"bool\", \"Enable open tracking\", null, false, false, false)\n            },\n            SupportedFeatures: new List<string> { \"SendEmail\", \"BulkEmail\", \"Templates\", \"Tracking\", \"Analytics\" }\n        );\n    }\n\n    #endregion\n\n    #region Metrics Implementation (Mock implementations for now)\n\n    protected override async Task<GatewayStatusReport> GetEmailStatusReportAsync(CancellationToken cancellationToken)\n    {\n        // Mock implementation - in a real scenario, this would call SendGrid's stats API\n        return new GatewayStatusReport(\n            IsHealthy: true,\n            Status: \"Operational\",\n            LastChecked: DateTimeOffset.UtcNow,\n            ResponseTime: TimeSpan.FromMilliseconds(150)\n        );\n    }\n\n    protected override async Task<IReadOnlyList<DeliveryResult>> GetEmailDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken)\n    {\n        // Mock implementation - would integrate with SendGrid's Event Webhook or Stats API\n        var results = new List<DeliveryResult>();\n        for (int i = 0; i < Math.Min(maxItems, 10); i++)\n        {\n            results.Add(GatewayUtilities.CreateMockDeliveryResult($\"sg_{Guid.NewGuid():N}\"));\n        }\n        return results.AsReadOnly();\n    }\n\n    protected override async Task<UsageMetrics> GetEmailUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        // Mock implementation - would call SendGrid's Stats API\n        return GatewayUtilities.CreateMockUsageMetrics(from, to);\n    }\n\n    // Additional mock implementations for other metrics methods...\n    protected override async Task<IReadOnlyList<GatewayErrorEntry>> GetEmailErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new List<GatewayErrorEntry>().AsReadOnly();\n    }\n\n    protected override async Task<PerformanceSnapshot> GetEmailPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken)\n    {\n        return new PerformanceSnapshot(DateTimeOffset.UtcNow, 100, 95, TimeSpan.FromMilliseconds(200));\n    }\n\n    protected override async Task<SlaReport> GetEmailSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new SlaReport(99.9, TimeSpan.FromMilliseconds(150), 0.1);\n    }\n\n    protected override async Task<LatencyMetrics> GetEmailLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new LatencyMetrics(TimeSpan.FromMilliseconds(150), TimeSpan.FromMilliseconds(200), TimeSpan.FromMilliseconds(100));\n    }\n\n    protected override async Task<IReadOnlyList<TrafficTrend>> GetEmailTrafficTrendsAsync(string granularity, CancellationToken cancellationToken)\n    {\n        return new List<TrafficTrend>().AsReadOnly();\n    }\n\n    protected override async Task<IReadOnlyList<AnomalyDetectionResult>> GetEmailAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new List<AnomalyDetectionResult>().AsReadOnly();\n    }\n\n    protected override async Task<GeneratedReport> GenerateEmailMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken)\n    {\n        return new GeneratedReport(\"email_metrics.csv\", \"text/csv\", Array.Empty<byte>());\n    }\n\n    protected override async Task<IReadOnlyList<RetryAttemptInfo>> GetEmailRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new List<RetryAttemptInfo>().AsReadOnly();\n    }\n\n    protected override async Task<IReadOnlyList<ConfigurationImpactRecord>> GetEmailChangeImpactHistoryAsync(CancellationToken cancellationToken)\n    {\n        return new List<ConfigurationImpactRecord>().AsReadOnly();\n    }\n\n    #endregion\n\n    #region Message Operations (Not implemented in base class)\n\n    public override async Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)\n    {\n        // SendGrid doesn't support native scheduling, would need to implement with a job scheduler\n        throw new NotSupportedException(\"Message scheduling is not supported by SendGrid gateway\");\n    }\n\n    public override async Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)\n    {\n        throw new NotSupportedException(\"Message scheduling is not supported by SendGrid gateway\");\n    }\n\n    public override async Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default)\n    {\n        throw new NotSupportedException(\"Message resending is not supported by SendGrid gateway\");\n    }\n\n    public override async Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)\n    {\n        // Would integrate with SendGrid's Event Webhook\n        return new MessageStatusInfo(messageId, \"Delivered\", DateTimeOffset.UtcNow);\n    }\n\n    public override async Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default)\n    {\n        // Would integrate with SendGrid's Event Webhook\n        return new DeliveryReceipt(messageId, \"Delivered\", DateTimeOffset.UtcNow);\n    }\n\n    public override async Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default)\n    {\n        var json = JsonSerializer.Serialize(rawPayload);\n        var content = new StringContent(json, Encoding.UTF8, \"application/json\");\n\n        _httpClient.DefaultRequestHeaders.Clear();\n        _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Bearer {_apiKey}\");\n\n        var response = await _httpClient.PostAsync(\"mail/send\", content, cancellationToken);\n        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);\n\n        return new RawGatewayResponse(response.IsSuccessStatusCode, (int)response.StatusCode, responseContent);\n    }\n\n    public override async Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        var sendGridPayload = CreateSendGridPayload(payload);\n        var validation = await ValidateMessageAsync(payload, cancellationToken);\n        \n        return new PreparedMessage(\n            sendGridPayload,\n            payload.Headers ?? new Dictionary<string, string>(),\n            validation\n        );\n    }\n\n    public override async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            if (string.IsNullOrEmpty(_apiKey))\n                return false;\n\n            _httpClient.DefaultRequestHeaders.Clear();\n            _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Bearer {_apiKey}\");\n\n            var response = await _httpClient.GetAsync(\"user/profile\", cancellationToken);\n            return response.IsSuccessStatusCode;\n        }\n        catch\n        {\n            return false;\n        }\n    }\n\n    #endregion\n\n    #region Helper Methods\n\n    private object CreateSendGridPayload(MessagePayload payload)\n    {\n        var sender = payload.From ?? GetDefaultSender();\n        var senderName = GetDefaultSenderName();\n\n        var sendGridPayload = new\n        {\n            personalizations = new[]\n            {\n                new\n                {\n                    to = new[] { new { email = payload.Recipient } },\n                    subject = payload.Subject ?? \"No Subject\"\n                }\n            },\n            from = new\n            {\n                email = sender,\n                name = senderName\n            },\n            content = new[]\n            {\n                new\n                {\n                    type = IsHtmlEnabled() ? \"text/html\" : \"text/plain\",\n                    value = payload.Content\n                }\n            },\n            tracking_settings = new\n            {\n                click_tracking = new { enable = GetConfiguration<bool>(\"EnableClickTracking\") },\n                open_tracking = new { enable = GetConfiguration<bool>(\"EnableOpenTracking\") }\n            }\n        };\n\n        return sendGridPayload;\n    }\n\n    protected override void Dispose(bool disposing)\n    {\n        if (disposing)\n        {\n            _httpClient?.Dispose();\n        }\n        base.Dispose(disposing);\n    }\n\n    #endregion\n}\n"}]}