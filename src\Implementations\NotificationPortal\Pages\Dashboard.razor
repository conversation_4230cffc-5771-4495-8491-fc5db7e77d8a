@page "/dashboard"
@page "/"
@attribute [Authorize]
@inject IPluginService PluginService
@inject IApiService ApiService
@inject IBreadcrumbService BreadcrumbService
@inject INotificationService NotificationService
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime
@implements IDisposable

<PageTitle>Dashboard - NotificationService</PageTitle>

<div class="dashboard-header mb-4">
    <div class="d-flex justify-space-between align-center">
        <div>
            <MudText Typo="Typo.h4" Class="dashboard-title">Dashboard</MudText>
            <MudText Typo="Typo.body2" Class="mud-text-secondary">
                Welcome back! Here's what's happening with your notification service.
            </MudText>
        </div>
        <div class="d-flex gap-2">
            <MudButton Variant="Variant.Outlined"
                       StartIcon="@Icons.Material.Filled.Refresh"
                       OnClick="RefreshData"
                       Disabled="_isLoading">
                Refresh
            </MudButton>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.Send"
                       Href="/send/email">
                Quick Send
            </MudButton>
        </div>
    </div>
</div>

<!-- System Health Cards -->
<MudGrid Class="mb-6">
    <MudItem xs="12" sm="6" md="3">
        <MudCard Elevation="4" Class="stat-card health-card">
            <MudCardContent>
                <div class="d-flex align-center">
                    <div class="stat-icon-container health-icon">
                        <MudIcon Icon="@GetHealthIcon(_systemHealth.Status)"
                                Color="@GetHealthColor(_systemHealth.Status)"
                                Size="Size.Large" />
                    </div>
                    <div class="flex-grow-1">
                        <MudText Typo="Typo.h6" Class="stat-value">@_systemHealth.Status</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary stat-label">System Health</MudText>
                        <MudText Typo="Typo.caption" Class="mud-text-secondary">
                            Uptime: @FormatUptime(_systemHealth.Uptime)
                        </MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard Elevation="4" Class="stat-card plugins-card">
            <MudCardContent>
                <div class="d-flex align-center">
                    <div class="stat-icon-container plugins-icon">
                        <MudIcon Icon="@Icons.Material.Filled.Extension" Color="Color.Primary" Size="Size.Large" />
                    </div>
                    <div class="flex-grow-1">
                        <MudText Typo="Typo.h6" Class="stat-value">@_stats.System.LoadedPlugins</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary stat-label">Active Plugins</MudText>
                        <div class="d-flex gap-1 mt-1">
                            @foreach (var pluginType in _pluginTypeCounts.Take(3))
                            {
                                <MudChip Size="Size.Small"
                                         Color="Color.Default"
                                         Variant="Variant.Outlined"
                                         Class="plugin-type-chip">
                                    @pluginType.Key: @pluginType.Value
                                </MudChip>
                            }
                        </div>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard Elevation="4" Class="stat-card">
            <MudCardContent>
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Business" Color="Color.Info" Size="Size.Large" Class="mr-3" />
                    <div>
                        <MudText Typo="Typo.h5">@_stats.System.ActiveTenants</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">Active Tenants</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard Elevation="4" Class="stat-card">
            <MudCardContent>
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.People" Color="Color.Warning" Size="Size.Large" Class="mr-3" />
                    <div>
                        <MudText Typo="Typo.h5">@_stats.System.TotalUsers</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">Total Users</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

<!-- Usage Statistics -->
<MudGrid Class="mb-6">
    <MudItem xs="12" md="8">
        <MudCard Elevation="4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Message Volume (Last 24 Hours)</MudText>
                </CardHeaderContent>
                <CardHeaderActions>
                    <MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="RefreshData" />
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent>
                <div class="chart-container">
                    <!-- Placeholder for chart - would integrate with a charting library -->
                    <div class="chart-placeholder">
                        <MudIcon Icon="@Icons.Material.Filled.BarChart" Size="Size.Large" Class="mud-text-secondary" />
                        <MudText Typo="Typo.body1" Class="mud-text-secondary">Chart visualization would go here</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" md="4">
        <MudCard Elevation="4" Class="h-100">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Today's Summary</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <div class="summary-item">
                    <MudIcon Icon="@Icons.Material.Filled.Email" Color="Color.Primary" Class="mr-2" />
                    <MudText>Emails: @_stats.Usage.EmailsSentToday.ToString("N0")</MudText>
                </div>
                <div class="summary-item">
                    <MudIcon Icon="@Icons.Material.Filled.Sms" Color="Color.Secondary" Class="mr-2" />
                    <MudText>SMS: @_stats.Usage.SmsSentToday.ToString("N0")</MudText>
                </div>
                <div class="summary-item">
                    <MudIcon Icon="@Icons.Material.Filled.NotificationsActive" Color="Color.Tertiary" Class="mr-2" />
                    <MudText>Push: @_stats.Usage.PushSentToday.ToString("N0")</MudText>
                </div>
                <div class="summary-item">
                    <MudIcon Icon="@Icons.Material.Filled.Api" Color="Color.Info" Class="mr-2" />
                    <MudText>API Calls: @_stats.Usage.ApiCallsToday.ToString("N0")</MudText>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

<!-- Plugin Status -->
<MudGrid Class="mb-6">
    <MudItem xs="12">
        <MudCard Elevation="4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Plugin Health Status</MudText>
                </CardHeaderContent>
                <CardHeaderActions>
                    <MudButton Variant="Variant.Text" Color="Color.Primary" Href="/plugins">
                        View All Plugins
                    </MudButton>
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent>
                <MudGrid>
                    @foreach (var plugin in _plugins.Take(6))
                    {
                        <MudItem xs="12" sm="6" md="4">
                            <MudCard Elevation="2" Class="plugin-status-card">
                                <MudCardContent Class="pa-3">
                                    <div class="d-flex align-center justify-space-between">
                                        <div class="d-flex align-center">
                                            <MudIcon Icon="@GetPluginIcon(plugin.Type)" Class="mr-2" />
                                            <div>
                                                <MudText Typo="Typo.subtitle2">@plugin.Name</MudText>
                                                <MudText Typo="Typo.caption" Class="mud-text-secondary">@plugin.Provider</MudText>
                                            </div>
                                        </div>
                                        <MudChip Size="Size.Small" 
                                                 Color="@(plugin.IsEnabled ? Color.Success : Color.Default)"
                                                 Variant="Variant.Filled">
                                            @(plugin.IsEnabled ? "Active" : "Inactive")
                                        </MudChip>
                                    </div>
                                    <div class="mt-2">
                                        <MudText Typo="Typo.caption">
                                            Requests: @plugin.Metrics.TotalRequests.ToString("N0") 
                                            | Success Rate: @plugin.Metrics.SuccessRate.ToString("F1")%
                                        </MudText>
                                    </div>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

<!-- Queue Status -->
<MudGrid>
    <MudItem xs="12" md="6">
        <MudCard Elevation="4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Message Queue Status</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <div class="queue-stats">
                    <div class="queue-stat-item">
                        <MudText Typo="Typo.h4" Color="Color.Warning">@_stats.Queue.PendingMessages</MudText>
                        <MudText Typo="Typo.body2">Pending</MudText>
                    </div>
                    <div class="queue-stat-item">
                        <MudText Typo="Typo.h4" Color="Color.Info">@_stats.Queue.ProcessingMessages</MudText>
                        <MudText Typo="Typo.body2">Processing</MudText>
                    </div>
                    <div class="queue-stat-item">
                        <MudText Typo="Typo.h4" Color="Color.Success">@_stats.Queue.CompletedToday</MudText>
                        <MudText Typo="Typo.body2">Completed</MudText>
                    </div>
                    <div class="queue-stat-item">
                        <MudText Typo="Typo.h4" Color="Color.Error">@_stats.Queue.FailedToday</MudText>
                        <MudText Typo="Typo.body2">Failed</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" md="6">
        <MudCard Elevation="4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">System Performance</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <div class="performance-metrics">
                    <div class="metric-item">
                        <MudText Typo="Typo.body2">Uptime</MudText>
                        <MudText Typo="Typo.h6">@FormatUptime(_stats.System.Uptime)</MudText>
                    </div>
                    <div class="metric-item">
                        <MudText Typo="Typo.body2">Avg Processing Time</MudText>
                        <MudText Typo="Typo.h6">@_stats.Queue.AverageProcessingTime.ToString("F2")ms</MudText>
                    </div>
                    <div class="metric-item">
                        <MudText Typo="Typo.body2">Version</MudText>
                        <MudText Typo="Typo.h6">@_stats.System.Version</MudText>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

@code {
    private DashboardStats _stats = new();
    private List<Plugin> _plugins = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            // Load plugins
            _plugins = await PluginService.GetPluginsAsync();

            // Simulate dashboard stats - in real implementation, call your API
            _stats = new DashboardStats
            {
                System = new SystemOverview
                {
                    Status = "Running",
                    Version = "2.0.0",
                    Uptime = TimeSpan.FromHours(72),
                    LoadedPlugins = _plugins.Count,
                    ActiveTenants = 5,
                    TotalUsers = 25
                },
                Queue = new QueueStats
                {
                    PendingMessages = 12,
                    ProcessingMessages = 3,
                    CompletedToday = 1547,
                    FailedToday = 8,
                    AverageProcessingTime = 245.7
                },
                Usage = new UsageStats
                {
                    EmailsSentToday = 892,
                    SmsSentToday = 456,
                    PushSentToday = 234,
                    ApiCallsToday = 2847
                }
            };
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Failed to load dashboard data: {ex.Message}", Severity.Error);
        }
    }

    private async Task RefreshData()
    {
        await LoadDashboardData();
        Snackbar.Add("Dashboard data refreshed", Severity.Success);
    }

    private string GetPluginIcon(string type) => type.ToLower() switch
    {
        "email" => Icons.Material.Filled.Email,
        "sms" => Icons.Material.Filled.Sms,
        "push" or "pushnotification" => Icons.Material.Filled.NotificationsActive,
        "messaging" => Icons.Material.Filled.Chat,
        _ => Icons.Material.Filled.Extension
    };

    private string FormatUptime(TimeSpan uptime)
    {
        if (uptime.TotalDays >= 1)
            return $"{uptime.Days}d {uptime.Hours}h";
        else if (uptime.TotalHours >= 1)
            return $"{uptime.Hours}h {uptime.Minutes}m";
        else
            return $"{uptime.Minutes}m";
    }
}

<style>
    .stat-card {
        height: 100%;
        transition: transform 0.2s ease-in-out;
    }

    .stat-card:hover {
        transform: translateY(-2px);
    }

    .chart-container {
        height: 300px;
    }

    .chart-placeholder {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 2px dashed var(--mud-palette-divider);
        border-radius: 8px;
    }

    .summary-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
    }

    .plugin-status-card {
        height: 100%;
        transition: transform 0.2s ease-in-out;
    }

    .plugin-status-card:hover {
        transform: translateY(-1px);
    }

    .queue-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .queue-stat-item {
        text-align: center;
        padding: 16px;
        border-radius: 8px;
        background-color: var(--mud-palette-background-grey);
    }

    .performance-metrics {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .metric-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid var(--mud-palette-divider);
    }

    .metric-item:last-child {
        border-bottom: none;
    }
</style>
