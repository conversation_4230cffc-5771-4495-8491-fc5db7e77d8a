{"sourceFile": "src/Core/NotifyMaster.Core/Middleware/TenantMiddleware.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751224348679, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751225316890, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,5 @@\n-using Microsoft.AspNetCore.Builder;\n-using Microsoft.AspNetCore.Http;\n-using Microsoft.Extensions.DependencyInjection;\n-using Microsoft.Extensions.Logging;\n-using NotifyMaster.Core.Services;\n-using PluginCore.Models;\n+// Using statements are handled by GlobalUsings.cs\n \n namespace NotifyMaster.Core.Middleware;\n \n /// <summary>\n"}], "date": 1751224348679, "name": "Commit-0", "content": "using Microsoft.AspNetCore.Builder;\nusing Microsoft.AspNetCore.Http;\nusing Microsoft.Extensions.DependencyInjection;\nusing Microsoft.Extensions.Logging;\nusing NotifyMaster.Core.Services;\nusing PluginCore.Models;\n\nnamespace NotifyMaster.Core.Middleware;\n\n/// <summary>\n/// Middleware that resolves and sets tenant context for each request\n/// </summary>\npublic class TenantMiddleware\n{\n    private readonly RequestDelegate _next;\n    private readonly ILogger<TenantMiddleware> _logger;\n\n    public TenantMiddleware(RequestDelegate next, ILogger<TenantMiddleware> logger)\n    {\n        _next = next;\n        _logger = logger;\n    }\n\n    public async Task InvokeAsync(HttpContext context)\n    {\n        try\n        {\n            // Skip tenant resolution for health checks and static files\n            if (ShouldSkipTenantResolution(context))\n            {\n                await _next(context);\n                return;\n            }\n\n            // Resolve tenant\n            var tenantResolutionService = context.RequestServices.GetRequiredService<ITenantResolutionService>();\n            var tenant = await tenantResolutionService.ResolveTenantAsync(context);\n\n            if (tenant != null)\n            {\n                // Set tenant in context\n                context.Items[\"Tenant\"] = tenant;\n                context.Items[\"TenantId\"] = tenant.Id;\n                context.Items[\"TenantDomain\"] = tenant.Domain;\n\n                _logger.LogDebug(\"Resolved tenant {TenantId} ({TenantDomain}) for request {RequestPath}\", \n                    tenant.Id, tenant.Domain, context.Request.Path);\n\n                // Check if tenant is active\n                if (tenant.Status != TenantStatus.Active)\n                {\n                    _logger.LogWarning(\"Request to inactive tenant {TenantId} ({Status})\", tenant.Id, tenant.Status);\n                    \n                    if (tenant.Status == TenantStatus.Suspended)\n                    {\n                        context.Response.StatusCode = 403;\n                        await context.Response.WriteAsync(\"Tenant is suspended\");\n                        return;\n                    }\n                }\n            }\n            else\n            {\n                _logger.LogWarning(\"Could not resolve tenant for request {RequestPath}\", context.Request.Path);\n                \n                // For API endpoints, return 400 if no tenant found\n                if (context.Request.Path.StartsWithSegments(\"/api\"))\n                {\n                    context.Response.StatusCode = 400;\n                    await context.Response.WriteAsync(\"Tenant not found\");\n                    return;\n                }\n            }\n\n            await _next(context);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error in tenant middleware\");\n            \n            // Continue with request even if tenant resolution fails\n            await _next(context);\n        }\n    }\n\n    private static bool ShouldSkipTenantResolution(HttpContext context)\n    {\n        var path = context.Request.Path.Value?.ToLowerInvariant();\n        \n        if (string.IsNullOrEmpty(path))\n            return false;\n\n        // Skip for health checks\n        if (path.StartsWith(\"/health\") || path.StartsWith(\"/ping\"))\n            return true;\n\n        // Skip for static files\n        if (path.StartsWith(\"/css\") || path.StartsWith(\"/js\") || path.StartsWith(\"/images\") || \n            path.StartsWith(\"/favicon\") || path.StartsWith(\"/robots.txt\"))\n            return true;\n\n        // Skip for setup endpoints\n        if (path.StartsWith(\"/api/setup\"))\n            return true;\n\n        // Skip for Hangfire dashboard\n        if (path.StartsWith(\"/hangfire\"))\n            return true;\n\n        // Skip for Swagger/OpenAPI\n        if (path.StartsWith(\"/swagger\") || path.StartsWith(\"/openapi\"))\n            return true;\n\n        return false;\n    }\n}\n\n/// <summary>\n/// Extension methods for tenant middleware\n/// </summary>\npublic static class TenantMiddlewareExtensions\n{\n    /// <summary>\n    /// Adds tenant middleware to the pipeline\n    /// </summary>\n    public static IApplicationBuilder UseTenantMiddleware(this IApplicationBuilder builder)\n    {\n        return builder.UseMiddleware<TenantMiddleware>();\n    }\n}\n\n/// <summary>\n/// Helper class to access tenant context\n/// </summary>\npublic static class TenantContext\n{\n    /// <summary>\n    /// Gets the current tenant from HTTP context\n    /// </summary>\n    public static Tenant? GetCurrentTenant(HttpContext context)\n    {\n        return context.Items[\"Tenant\"] as Tenant;\n    }\n\n    /// <summary>\n    /// Gets the current tenant ID from HTTP context\n    /// </summary>\n    public static string? GetCurrentTenantId(HttpContext context)\n    {\n        return context.Items[\"TenantId\"] as string;\n    }\n\n    /// <summary>\n    /// Gets the current tenant domain from HTTP context\n    /// </summary>\n    public static string? GetCurrentTenantDomain(HttpContext context)\n    {\n        return context.Items[\"TenantDomain\"] as string;\n    }\n\n    /// <summary>\n    /// Sets the current tenant in HTTP context\n    /// </summary>\n    public static void SetCurrentTenant(HttpContext context, Tenant tenant)\n    {\n        context.Items[\"Tenant\"] = tenant;\n        context.Items[\"TenantId\"] = tenant.Id;\n        context.Items[\"TenantDomain\"] = tenant.Domain;\n    }\n}\n"}]}