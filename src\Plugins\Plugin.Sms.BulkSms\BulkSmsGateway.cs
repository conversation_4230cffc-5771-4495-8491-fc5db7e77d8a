using Microsoft.Extensions.Logging;
using PluginCore.Base;
using PluginCore.Models;
using PluginCore.Utilities;
using System.Text.Json;
using System.Text;

namespace Plugin.Sms.BulkSms;

/// <summary>
/// BulkSMS gateway implementation with scheduling and storage capabilities.
/// </summary>
public class BulkSmsGateway : SmsGatewayBase
{
    private readonly HttpClient _httpClient;
    private readonly RateLimitConfig _rateLimitConfig;
    private readonly IMessageSchedulingService _schedulingService;
    private readonly IMessageStorageService _storageService;
    private readonly string _gatewayId;
    private string? _username;
    private string? _password;

    public BulkSmsGateway(
        ILogger<BulkSmsGateway> logger,
        IMessageSchedulingService? schedulingService = null,
        IMessageStorageService? storageService = null) : base(logger)
    {
        _httpClient = GatewayUtilities.CreateHttpClient("https://api.bulksms.com/v1/", TimeSpan.FromSeconds(30));
        _rateLimitConfig = new RateLimitConfig { MaxRequests = 100, TimeWindow = TimeSpan.FromMinutes(1) }; // Conservative rate limit
        _schedulingService = schedulingService ?? new DefaultMessageSchedulingService(_logger);
        _storageService = storageService ?? new DefaultMessageStorageService(_logger);
        _gatewayId = "BulkSMS";

        InitializeConfiguration();
    }

    private void InitializeConfiguration()
    {
        // Set default configuration
        SetConfiguration("Username", "");
        SetConfiguration("Password", "");
        SetConfiguration("DefaultSender", "");
        SetConfiguration("MaxMessageLength", 160);
        SetConfiguration("EnableDeliveryReports", true);
        SetConfiguration("EnableUnicode", false);
    }

    #region SmsGatewayBase Implementation

    protected override async Task<MessageSendResult> SendSmsInternalAsync(MessagePayload payload, CancellationToken cancellationToken)
    {
        try
        {
            var rateLimitKey = $"bulksms_{_username}";
            
            return await new Func<Task<MessageSendResult>>(async () =>
            {
                var bulkSmsPayload = CreateBulkSmsPayload(payload);
                var json = JsonSerializer.Serialize(bulkSmsPayload);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Set basic authentication
                var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_username}:{_password}"));
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {credentials}");

                var response = await _httpClient.PostAsync("messages", content, cancellationToken);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

                    var messageId = responseData.GetProperty("id").GetString() ?? Guid.NewGuid().ToString();
                    var result = GatewayUtilities.CreateSuccessResult(messageId, payload.CorrelationId);

                    // Store the message for potential resending
                    await _storageService.StoreMessageAsync(messageId, _gatewayId, payload, result, cancellationToken);

                    return result;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("BulkSMS API error: {StatusCode} - {Content}", response.StatusCode, errorContent);
                    return GatewayUtilities.CreateFailureResult(payload.CorrelationId, $"BulkSMS API error: {response.StatusCode}");
                }
            }).ExecuteWithRateLimitAsync(rateLimitKey, _rateLimitConfig, _logger, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS via BulkSMS");
            return GatewayUtilities.CreateFailureResult(payload.CorrelationId, ex.Message);
        }
    }

    protected override async Task<IReadOnlyList<MessageSendResult>> SendBulkSmsInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken)
    {
        try
        {
            var rateLimitKey = $"bulksms_{_username}";
            
            return await new Func<Task<IReadOnlyList<MessageSendResult>>>(async () =>
            {
                var bulkPayloads = payloads.Select(CreateBulkSmsPayload).ToArray();
                var json = JsonSerializer.Serialize(bulkPayloads);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_username}:{_password}"));
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {credentials}");

                var response = await _httpClient.PostAsync("messages", content, cancellationToken);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    var responseArray = JsonSerializer.Deserialize<JsonElement[]>(responseContent);
                    
                    var results = new List<MessageSendResult>();
                    for (int i = 0; i < responseArray.Length; i++)
                    {
                        var messageId = responseArray[i].GetProperty("id").GetString() ?? Guid.NewGuid().ToString();
                        var correlationId = payloads.ElementAtOrDefault(i)?.CorrelationId;
                        results.Add(GatewayUtilities.CreateSuccessResult(messageId, correlationId));
                    }
                    
                    return results.AsReadOnly();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("BulkSMS bulk API error: {StatusCode} - {Content}", response.StatusCode, errorContent);
                    
                    // Return failure results for all messages
                    return payloads.Select(p => GatewayUtilities.CreateFailureResult(p.CorrelationId, $"BulkSMS API error: {response.StatusCode}"))
                                  .ToList().AsReadOnly();
                }
            }).ExecuteWithRateLimitAsync(rateLimitKey, _rateLimitConfig, _logger, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send bulk SMS via BulkSMS");
            return payloads.Select(p => GatewayUtilities.CreateFailureResult(p.CorrelationId, ex.Message))
                          .ToList().AsReadOnly();
        }
    }

    protected override async Task<string?> GetSmsTemplateAsync(string templateId, CancellationToken cancellationToken)
    {
        // BulkSMS doesn't have a native template system, so we'll implement a simple local template store
        var templates = new Dictionary<string, string>
        {
            ["welcome"] = "Welcome {name}! Your account has been created successfully.",
            ["verification"] = "Your verification code is: {code}",
            ["reminder"] = "Hi {name}, this is a reminder about {event} on {date}.",
            ["alert"] = "ALERT: {message}. Please take immediate action."
        };

        return templates.TryGetValue(templateId.ToLowerInvariant(), out var template) ? template : null;
    }

    protected override async Task<IEnumerable<string>> ValidateSmsSpecificAsync(MessagePayload payload, CancellationToken cancellationToken)
    {
        var errors = new List<string>();
        
        // Validate credentials
        if (string.IsNullOrEmpty(_username) || string.IsNullOrEmpty(_password))
        {
            errors.Add("BulkSMS username and password are required");
        }
        
        // Validate phone number format for BulkSMS
        var phoneNumber = payload.Recipient;
        if (!phoneNumber.StartsWith("+"))
        {
            errors.Add("Phone number must be in international format starting with +");
        }
        
        // Check message length based on encoding
        var enableUnicode = GetConfiguration<bool>("EnableUnicode");
        var maxLength = enableUnicode ? 70 : 160; // Unicode SMS are shorter
        
        if (payload.Content.Length > maxLength)
        {
            errors.Add($"Message exceeds maximum length of {maxLength} characters for {(enableUnicode ? "Unicode" : "standard")} SMS");
        }
        
        return errors;
    }

    #endregion

    #region Admin Implementation

    protected override async Task<IEnumerable<GatewayConfiguration>> GetSmsProviderConfigurationsAsync(CancellationToken cancellationToken)
    {
        return new[]
        {
            new GatewayConfiguration("Username", "BulkSMS Username", "", "Your BulkSMS username", true, false),
            new GatewayConfiguration("Password", "BulkSMS Password", "", "Your BulkSMS password", true, true),
            new GatewayConfiguration("EnableUnicode", "Enable Unicode SMS", GetConfiguration<bool>("EnableUnicode").ToString(), "Enable Unicode SMS support", false, false, "bool")
        };
    }

    protected override async Task UpdateSmsProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken)
    {
        switch (setting.Key)
        {
            case "Username":
                if (!string.IsNullOrEmpty(setting.Value))
                {
                    _username = setting.Value;
                    SetConfiguration("Username", setting.Value);
                }
                break;
            case "Password":
                if (!string.IsNullOrEmpty(setting.Value))
                {
                    _password = setting.Value;
                    SetConfiguration("Password", setting.Value);
                }
                break;
            case "EnableUnicode":
                if (bool.TryParse(setting.Value, out var enableUnicode))
                    SetConfiguration("EnableUnicode", enableUnicode);
                break;
        }
    }

    protected override async Task<OperationResult> TestSmsConfigurationAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (string.IsNullOrEmpty(_username) || string.IsNullOrEmpty(_password))
            {
                return GatewayUtilities.CreateOperationResult(false, "Username and password are required");
            }

            // Test credentials by getting account balance
            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_username}:{_password}"));
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {credentials}");

            var response = await _httpClient.GetAsync("profile", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                return GatewayUtilities.CreateOperationResult(true, "BulkSMS configuration is valid");
            }
            else
            {
                return GatewayUtilities.CreateOperationResult(false, $"BulkSMS API test failed: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            return GatewayUtilities.CreateOperationResult(false, "Configuration test failed", ex);
        }
    }

    protected override async Task<PluginManifest> GetSmsManifestAsync(CancellationToken cancellationToken)
    {
        return new PluginManifest(
            Name: "BulkSMS Gateway",
            Version: "1.0.0",
            Description: "SMS gateway implementation for BulkSMS service",
            Author: "NotificationService Team",
            Type: "SMS",
            Provider: "BulkSMS",
            AssemblyName: "Plugin.Sms.BulkSms.dll",
            EntryPoint: "Plugin.Sms.BulkSms.BulkSmsGateway",
            Dependencies: new List<PluginDependency>(),
            Configuration: new Dictionary<string, PluginConfigurationItem>
            {
                ["Username"] = new("string", "BulkSMS Username", null, null, true, false),
                ["Password"] = new("string", "BulkSMS Password", null, null, true, true),
                ["EnableUnicode"] = new("bool", "Enable Unicode SMS", null, false, false, false)
            },
            SupportedFeatures: new List<string> { "SendSMS", "BulkSMS", "DeliveryReports", "Unicode" }
        );
    }

    #endregion

    #region Metrics Implementation (Mock implementations)

    protected override async Task<GatewayStatusReport> GetSmsStatusReportAsync(CancellationToken cancellationToken)
    {
        return new GatewayStatusReport(
            IsHealthy: true,
            Status: "Operational",
            LastChecked: DateTimeOffset.UtcNow,
            ResponseTime: TimeSpan.FromMilliseconds(200)
        );
    }

    protected override async Task<IReadOnlyList<DeliveryResult>> GetSmsDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken)
    {
        var results = new List<DeliveryResult>();
        for (int i = 0; i < Math.Min(maxItems, 10); i++)
        {
            results.Add(GatewayUtilities.CreateMockDeliveryResult($"bulk_{Guid.NewGuid():N}"));
        }
        return results.AsReadOnly();
    }

    protected override async Task<UsageMetrics> GetSmsUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return GatewayUtilities.CreateMockUsageMetrics(from, to);
    }

    // Additional mock implementations for other metrics methods...
    protected override async Task<IReadOnlyList<GatewayErrorEntry>> GetSmsErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new List<GatewayErrorEntry>().AsReadOnly();
    }

    protected override async Task<PerformanceSnapshot> GetSmsPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken)
    {
        return new PerformanceSnapshot(DateTimeOffset.UtcNow, 100, 95, TimeSpan.FromMilliseconds(200));
    }

    protected override async Task<SlaReport> GetSmsSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new SlaReport(99.5, TimeSpan.FromMilliseconds(200), 0.5);
    }

    protected override async Task<LatencyMetrics> GetSmsLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new LatencyMetrics(TimeSpan.FromMilliseconds(200), TimeSpan.FromMilliseconds(300), TimeSpan.FromMilliseconds(100));
    }

    protected override async Task<IReadOnlyList<TrafficTrend>> GetSmsTrafficTrendsAsync(string granularity, CancellationToken cancellationToken)
    {
        return new List<TrafficTrend>().AsReadOnly();
    }

    protected override async Task<IReadOnlyList<AnomalyDetectionResult>> GetSmsAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new List<AnomalyDetectionResult>().AsReadOnly();
    }

    protected override async Task<GeneratedReport> GenerateSmsMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken)
    {
        return new GeneratedReport("sms_metrics.csv", "text/csv", Array.Empty<byte>());
    }

    protected override async Task<IReadOnlyList<RetryAttemptInfo>> GetSmsRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new List<RetryAttemptInfo>().AsReadOnly();
    }

    protected override async Task<IReadOnlyList<ConfigurationImpactRecord>> GetSmsChangeImpactHistoryAsync(CancellationToken cancellationToken)
    {
        return new List<ConfigurationImpactRecord>().AsReadOnly();
    }

    #endregion

    #region Message Operations (Not implemented in base class)

    public override async Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)
    {
        // BulkSMS doesn't support native scheduling, so we use our scheduling service
        return await _schedulingService.ScheduleMessageAsync(_gatewayId, payload, scheduledTime, cancellationToken);
    }

    public override async Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)
    {
        // Use our scheduling service to cancel the message
        return await _schedulingService.CancelScheduledMessageAsync(scheduledMessageId, cancellationToken);
    }

    public override async Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default)
    {
        // Use our storage service to resend the message
        var storedMessage = await _storageService.GetStoredMessageAsync(originalMessageId, cancellationToken);
        if (storedMessage == null)
        {
            return GatewayUtilities.CreateFailureResult(null, "Original message not found");
        }

        // Create a new correlation ID for the resend
        var resendPayload = storedMessage.Payload with
        {
            CorrelationId = $"resend_{originalMessageId}_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}"
        };

        // Send the message again
        var sendResult = await SendSmsInternalAsync(resendPayload, cancellationToken);

        // Update the storage with the resend result
        await _storageService.UpdateSendResultAsync(originalMessageId, sendResult, cancellationToken);

        return sendResult;
    }

    public override async Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_username}:{_password}"));
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {credentials}");

            var response = await _httpClient.GetAsync($"messages/{messageId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var messageData = JsonSerializer.Deserialize<JsonElement>(content);
                var status = messageData.GetProperty("status").GetProperty("type").GetString() ?? "Unknown";
                
                return new MessageStatusInfo(messageId, status, DateTimeOffset.UtcNow);
            }
            
            return new MessageStatusInfo(messageId, "Unknown", DateTimeOffset.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get message status for {MessageId}", messageId);
            return new MessageStatusInfo(messageId, "Error", DateTimeOffset.UtcNow, ex.Message);
        }
    }

    public override async Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default)
    {
        var statusInfo = await GetMessageStatusAsync(messageId, cancellationToken);
        return new DeliveryReceipt(messageId, statusInfo.Status, statusInfo.Timestamp);
    }

    public override async Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(rawPayload);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_username}:{_password}"));
        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {credentials}");

        var response = await _httpClient.PostAsync("messages", content, cancellationToken);
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

        return new RawGatewayResponse(response.IsSuccessStatusCode, (int)response.StatusCode, responseContent);
    }

    public override async Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        var bulkSmsPayload = CreateBulkSmsPayload(payload);
        var validation = await ValidateMessageAsync(payload, cancellationToken);
        
        return new PreparedMessage(
            bulkSmsPayload,
            payload.Headers ?? new Dictionary<string, string>(),
            validation
        );
    }

    public override async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_username) || string.IsNullOrEmpty(_password))
                return false;

            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_username}:{_password}"));
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {credentials}");

            var response = await _httpClient.GetAsync("profile", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region Helper Methods

    private object CreateBulkSmsPayload(MessagePayload payload)
    {
        var sender = payload.From ?? GetDefaultSender();
        var enableUnicode = GetConfiguration<bool>("EnableUnicode");

        var bulkSmsPayload = new
        {
            to = payload.Recipient,
            body = payload.Content,
            from = !string.IsNullOrEmpty(sender) ? sender : null,
            encoding = enableUnicode ? "UNICODE" : "TEXT"
        };

        return bulkSmsPayload;
    }



    #endregion
}
