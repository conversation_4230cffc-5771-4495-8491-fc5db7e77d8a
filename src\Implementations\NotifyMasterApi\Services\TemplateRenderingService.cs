using Microsoft.Extensions.Logging;
using NotifyMasterApi.Infrastructure;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace NotifyMasterApi.Services;

public interface ITemplateRenderingService
{
    Task<string> RenderTemplateAsync(string templateContent, Dictionary<string, object> variables);
    Task<TemplateRenderResult> RenderTemplateWithValidationAsync(string templateId, Dictionary<string, object> variables);
    Task<List<string>> ExtractVariablesFromTemplateAsync(string templateContent);
    Task<bool> ValidateTemplateAsync(string templateContent);
}

public class TemplateRenderResult
{
    public bool Success { get; set; }
    public string RenderedContent { get; set; } = string.Empty;
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> UsedVariables { get; set; } = new();
    public List<string> MissingVariables { get; set; } = new();
}

public class TemplateRenderingService : ITemplateRenderingService
{
    private readonly ILogger<TemplateRenderingService> _logger;
    private static readonly Regex VariableRegex = new(@"\{\{([^}]+)\}\}", RegexOptions.Compiled);

    public TemplateRenderingService(ILogger<TemplateRenderingService> logger)
    {
        _logger = logger;
    }

    public async Task<string> RenderTemplateAsync(string templateContent, Dictionary<string, object> variables)
    {
        if (string.IsNullOrEmpty(templateContent))
        {
            return string.Empty;
        }

        var rendered = templateContent;

        // Replace variables in the format {{variable_name}}
        var matches = VariableRegex.Matches(templateContent);
        
        foreach (Match match in matches)
        {
            var variableName = match.Groups[1].Value.Trim();
            var placeholder = match.Value;

            if (variables.TryGetValue(variableName, out var value))
            {
                var stringValue = value?.ToString() ?? "";
                rendered = rendered.Replace(placeholder, stringValue);
            }
            else
            {
                // Keep placeholder if variable not found
                _logger.LogWarning("Variable '{VariableName}' not found in template variables", variableName);
            }
        }

        return rendered;
    }

    public async Task<TemplateRenderResult> RenderTemplateWithValidationAsync(string templateId, Dictionary<string, object> variables)
    {
        var result = new TemplateRenderResult();

        try
        {
            // In a real implementation, this would fetch the template from storage
            var templateContent = await GetTemplateContentAsync(templateId);
            
            if (string.IsNullOrEmpty(templateContent))
            {
                result.Errors.Add($"Template '{templateId}' not found");
                return result;
            }

            // Extract all variables from template
            var templateVariables = await ExtractVariablesFromTemplateAsync(templateContent);
            
            // Check for missing variables
            var missingVariables = templateVariables.Where(v => !variables.ContainsKey(v)).ToList();
            result.MissingVariables = missingVariables;
            
            if (missingVariables.Any())
            {
                result.Warnings.Add($"Missing variables: {string.Join(", ", missingVariables)}");
            }

            // Check for unused variables
            var unusedVariables = variables.Keys.Where(k => !templateVariables.Contains(k)).ToList();
            if (unusedVariables.Any())
            {
                result.Warnings.Add($"Unused variables: {string.Join(", ", unusedVariables)}");
            }

            // Render template
            result.RenderedContent = await RenderTemplateAsync(templateContent, variables);
            result.UsedVariables = templateVariables.Where(v => variables.ContainsKey(v)).ToList();
            result.Success = true;

            _logger.LogDebug("Successfully rendered template '{TemplateId}' with {VariableCount} variables", 
                templateId, variables.Count);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Errors.Add($"Error rendering template: {ex.Message}");
            _logger.LogError(ex, "Error rendering template '{TemplateId}'", templateId);
        }

        return result;
    }

    public async Task<List<string>> ExtractVariablesFromTemplateAsync(string templateContent)
    {
        if (string.IsNullOrEmpty(templateContent))
        {
            return new List<string>();
        }

        var variables = new HashSet<string>();
        var matches = VariableRegex.Matches(templateContent);

        foreach (Match match in matches)
        {
            var variableName = match.Groups[1].Value.Trim();
            variables.Add(variableName);
        }

        return variables.ToList();
    }

    public async Task<bool> ValidateTemplateAsync(string templateContent)
    {
        try
        {
            if (string.IsNullOrEmpty(templateContent))
            {
                return false;
            }

            // Check for balanced braces
            var openBraces = templateContent.Count(c => c == '{');
            var closeBraces = templateContent.Count(c => c == '}');
            
            if (openBraces != closeBraces)
            {
                return false;
            }

            // Validate variable syntax
            var matches = VariableRegex.Matches(templateContent);
            foreach (Match match in matches)
            {
                var variableName = match.Groups[1].Value.Trim();
                if (string.IsNullOrEmpty(variableName))
                {
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating template");
            return false;
        }
    }

    private async Task<string> GetTemplateContentAsync(string templateId)
    {
        // In a real implementation, this would fetch from database or storage
        // For now, return a sample template
        return templateId switch
        {
            "welcome_email" => "Hello {{user_name}}, welcome to {{app_name}}! Your account is now active.",
            "password_reset" => "Hi {{user_name}}, click here to reset your password: {{reset_link}}",
            "order_confirmation" => "Thank you {{customer_name}}! Your order #{{order_id}} for {{total_amount}} has been confirmed.",
            _ => ""
        };
    }
}

public interface ISchedulingService
{
    Task<string> ScheduleMessageAsync(ScheduledMessage message);
    Task<List<ScheduledMessage>> GetScheduledMessagesAsync(string? tenantId = null);
    Task<ScheduledMessage?> GetScheduledMessageAsync(string messageId);
    Task<bool> CancelScheduledMessageAsync(string messageId);
    Task<bool> UpdateScheduledMessageAsync(string messageId, ScheduledMessage message);
    Task ProcessScheduledMessagesAsync();
}

public class SchedulingService : ISchedulingService
{
    private readonly ILogger<SchedulingService> _logger;
    private readonly IEventStreamService _eventStreamService;
    private readonly Dictionary<string, ScheduledMessage> _scheduledMessages = new();

    public SchedulingService(ILogger<SchedulingService> logger, IEventStreamService eventStreamService)
    {
        _logger = logger;
        _eventStreamService = eventStreamService;
    }

    public async Task<string> ScheduleMessageAsync(ScheduledMessage message)
    {
        message.Id = Guid.NewGuid().ToString();
        message.Status = "Scheduled";
        message.CreatedAt = DateTime.UtcNow;

        _scheduledMessages[message.Id] = message;

        _logger.LogInformation("Scheduled message {MessageId} for {ScheduledFor} in tenant {TenantId}", 
            message.Id, message.ScheduledFor, message.TenantId);

        // Publish scheduling event
        await _eventStreamService.PublishEventAsync(new SystemEvent
        {
            TenantId = message.TenantId,
            Type = "scheduled",
            Channel = message.Channel,
            MessageId = message.Id,
            Status = "scheduled",
            Data = new Dictionary<string, object>
            {
                ["scheduled_for"] = message.ScheduledFor,
                ["cron_expression"] = message.CronExpression ?? "",
                ["created_by"] = message.CreatedBy ?? ""
            }
        });

        return message.Id;
    }

    public async Task<List<ScheduledMessage>> GetScheduledMessagesAsync(string? tenantId = null)
    {
        var messages = _scheduledMessages.Values.AsEnumerable();

        if (!string.IsNullOrEmpty(tenantId))
        {
            messages = messages.Where(m => m.TenantId == tenantId);
        }

        return messages.OrderBy(m => m.ScheduledFor).ToList();
    }

    public async Task<ScheduledMessage?> GetScheduledMessageAsync(string messageId)
    {
        _scheduledMessages.TryGetValue(messageId, out var message);
        return message;
    }

    public async Task<bool> CancelScheduledMessageAsync(string messageId)
    {
        if (!_scheduledMessages.TryGetValue(messageId, out var message))
        {
            return false;
        }

        if (message.Status != "Scheduled")
        {
            return false; // Already sent or cancelled
        }

        message.Status = "Cancelled";

        _logger.LogInformation("Cancelled scheduled message {MessageId} for tenant {TenantId}", 
            messageId, message.TenantId);

        // Publish cancellation event
        await _eventStreamService.PublishEventAsync(new SystemEvent
        {
            TenantId = message.TenantId,
            Type = "cancelled",
            Channel = message.Channel,
            MessageId = messageId,
            Status = "cancelled",
            Data = new Dictionary<string, object>
            {
                ["cancelled_at"] = DateTime.UtcNow,
                ["original_scheduled_for"] = message.ScheduledFor
            }
        });

        return true;
    }

    public async Task<bool> UpdateScheduledMessageAsync(string messageId, ScheduledMessage updatedMessage)
    {
        if (!_scheduledMessages.TryGetValue(messageId, out var existingMessage))
        {
            return false;
        }

        if (existingMessage.Status != "Scheduled")
        {
            return false; // Cannot update sent or cancelled messages
        }

        updatedMessage.Id = messageId;
        updatedMessage.CreatedAt = existingMessage.CreatedAt;
        _scheduledMessages[messageId] = updatedMessage;

        _logger.LogInformation("Updated scheduled message {MessageId} for tenant {TenantId}", 
            messageId, updatedMessage.TenantId);

        return true;
    }

    public async Task ProcessScheduledMessagesAsync()
    {
        var now = DateTime.UtcNow;
        var messagesToProcess = _scheduledMessages.Values
            .Where(m => m.Status == "Scheduled" && m.ScheduledFor <= now)
            .ToList();

        foreach (var message in messagesToProcess)
        {
            try
            {
                await ProcessScheduledMessageAsync(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing scheduled message {MessageId}", message.Id);
                message.Status = "Failed";
            }
        }
    }

    private async Task ProcessScheduledMessageAsync(ScheduledMessage message)
    {
        message.Status = "Sent";

        _logger.LogInformation("Processing scheduled message {MessageId} for tenant {TenantId}", 
            message.Id, message.TenantId);

        // In a real implementation, this would send the message through the appropriate channel
        // For now, we'll just simulate sending

        // Publish sent event
        await _eventStreamService.PublishEventAsync(new SystemEvent
        {
            TenantId = message.TenantId,
            Type = "sent",
            Channel = message.Channel,
            MessageId = message.Id,
            Status = "sent",
            Data = new Dictionary<string, object>
            {
                ["sent_at"] = DateTime.UtcNow,
                ["scheduled_for"] = message.ScheduledFor,
                ["processing_delay_ms"] = (DateTime.UtcNow - message.ScheduledFor).TotalMilliseconds
            }
        });

        // Handle recurring messages with cron expressions
        if (!string.IsNullOrEmpty(message.CronExpression))
        {
            var nextScheduledTime = CalculateNextCronExecution(message.CronExpression, DateTime.UtcNow);
            if (nextScheduledTime.HasValue)
            {
                var recurringMessage = new ScheduledMessage
                {
                    TenantId = message.TenantId,
                    Channel = message.Channel,
                    Payload = message.Payload,
                    ScheduledFor = nextScheduledTime.Value,
                    CronExpression = message.CronExpression,
                    CreatedBy = message.CreatedBy,
                    Metadata = message.Metadata
                };

                await ScheduleMessageAsync(recurringMessage);
            }
        }
    }

    private DateTime? CalculateNextCronExecution(string cronExpression, DateTime fromTime)
    {
        // In a real implementation, this would use a proper cron parser like NCrontab
        // For now, we'll do simple parsing for common patterns
        
        if (cronExpression == "0 0 * * *") // Daily at midnight
        {
            return fromTime.Date.AddDays(1);
        }
        else if (cronExpression == "0 * * * *") // Every hour
        {
            return fromTime.AddHours(1).Date.AddHours(fromTime.AddHours(1).Hour);
        }
        else if (cronExpression == "*/5 * * * *") // Every 5 minutes
        {
            return fromTime.AddMinutes(5);
        }

        return null; // Unsupported cron expression
    }
}
