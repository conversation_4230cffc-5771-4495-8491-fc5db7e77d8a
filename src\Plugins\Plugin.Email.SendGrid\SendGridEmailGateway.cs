using Microsoft.Extensions.Logging;
using PluginCore.Base;
using PluginCore.Models;
using PluginCore.Utilities;
using System.Text.Json;
using System.Text;

namespace Plugin.Email.SendGrid;

/// <summary>
/// SendGrid email gateway implementation.
/// </summary>
public class SendGridEmailGateway : EmailGatewayBase
{
    private readonly HttpClient _httpClient;
    private readonly RateLimitConfig _rateLimitConfig;
    private string? _apiKey;
    private string? _baseUrl;

    public SendGridEmailGateway(ILogger<SendGridEmailGateway> logger) : base(logger)
    {
        _httpClient = GatewayUtilities.CreateHttpClient("https://api.sendgrid.com/v3/", TimeSpan.FromSeconds(30));
        _rateLimitConfig = new RateLimitConfig { MaxRequests = 600, TimeWindow = TimeSpan.FromMinutes(1) }; // SendGrid rate limit
        
        InitializeConfiguration();
    }

    private void InitializeConfiguration()
    {
        // Set default configuration
        SetConfiguration("ApiKey", "");
        SetConfiguration("DefaultSender", "<EMAIL>");
        SetConfiguration("DefaultSenderName", "Notification Service");
        SetConfiguration("EnableHtml", true);
        SetConfiguration("EnableAttachments", true);
        SetConfiguration("EnableTracking", true);
        SetConfiguration("EnableClickTracking", false);
        SetConfiguration("EnableOpenTracking", false);
    }

    #region EmailGatewayBase Implementation

    protected override async Task<MessageSendResult> SendEmailInternalAsync(MessagePayload payload, CancellationToken cancellationToken)
    {
        try
        {
            var rateLimitKey = $"sendgrid_{_apiKey?[..8]}";
            
            return await new Func<Task<MessageSendResult>>(async () =>
            {
                var sendGridPayload = CreateSendGridPayload(payload);
                var json = JsonSerializer.Serialize(sendGridPayload);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

                var response = await _httpClient.PostAsync("mail/send", content, cancellationToken);
                
                if (response.IsSuccessStatusCode)
                {
                    var messageId = response.Headers.GetValues("X-Message-Id").FirstOrDefault() ?? Guid.NewGuid().ToString();
                    return GatewayUtilities.CreateSuccessResult(messageId, payload.CorrelationId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("SendGrid API error: {StatusCode} - {Content}", response.StatusCode, errorContent);
                    return GatewayUtilities.CreateFailureResult(payload.CorrelationId, $"SendGrid API error: {response.StatusCode}");
                }
            }).ExecuteWithRateLimitAsync(rateLimitKey, _rateLimitConfig, _logger, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email via SendGrid");
            return GatewayUtilities.CreateFailureResult(payload.CorrelationId, ex.Message);
        }
    }

    protected override async Task<IReadOnlyList<MessageSendResult>> SendBulkEmailInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken)
    {
        var results = new List<MessageSendResult>();
        
        // SendGrid doesn't have a native bulk API, so we'll send them individually with rate limiting
        foreach (var payload in payloads)
        {
            var result = await SendEmailInternalAsync(payload, cancellationToken);
            results.Add(result);
            
            // Small delay between requests to respect rate limits
            await Task.Delay(100, cancellationToken);
        }
        
        return results.AsReadOnly();
    }

    protected override async Task<EmailTemplate?> GetEmailTemplateAsync(string templateId, CancellationToken cancellationToken)
    {
        try
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

            var response = await _httpClient.GetAsync($"templates/{templateId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var templateData = JsonSerializer.Deserialize<JsonElement>(content);
                
                // Extract template information from SendGrid response
                var name = templateData.GetProperty("name").GetString() ?? "Unknown Template";
                var versions = templateData.GetProperty("versions").EnumerateArray();
                var activeVersion = versions.FirstOrDefault(v => v.GetProperty("active").GetInt32() == 1);
                
                if (activeVersion.ValueKind != JsonValueKind.Undefined)
                {
                    var subject = activeVersion.GetProperty("subject").GetString() ?? "";
                    var htmlContent = activeVersion.GetProperty("html_content").GetString() ?? "";
                    
                    return new EmailTemplate(subject, htmlContent);
                }
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get SendGrid template {TemplateId}", templateId);
            return null;
        }
    }

    protected override async Task<IEnumerable<string>> ValidateEmailSpecificAsync(MessagePayload payload, CancellationToken cancellationToken)
    {
        var errors = new List<string>();
        
        // Validate API key
        if (string.IsNullOrEmpty(_apiKey))
        {
            errors.Add("SendGrid API key is not configured");
        }
        
        // Validate sender
        var sender = payload.From ?? GetDefaultSender();
        if (string.IsNullOrEmpty(sender) || !IsValidEmailAddress(sender))
        {
            errors.Add("Invalid sender email address");
        }
        
        // Validate content length (SendGrid has limits)
        if (payload.Content.Length > 1000000) // 1MB limit
        {
            errors.Add("Email content exceeds SendGrid size limit (1MB)");
        }
        
        return errors;
    }

    #endregion

    #region Admin Implementation

    protected override async Task<IEnumerable<GatewayConfiguration>> GetEmailProviderConfigurationsAsync(CancellationToken cancellationToken)
    {
        return new[]
        {
            new GatewayConfiguration("ApiKey", "SendGrid API Key", "", "Your SendGrid API key", true, true),
            new GatewayConfiguration("EnableTracking", "Enable email tracking", GetConfiguration<bool>("EnableTracking").ToString(), "Enable SendGrid email tracking", false, false, "bool"),
            new GatewayConfiguration("EnableClickTracking", "Enable click tracking", GetConfiguration<bool>("EnableClickTracking").ToString(), "Enable SendGrid click tracking", false, false, "bool"),
            new GatewayConfiguration("EnableOpenTracking", "Enable open tracking", GetConfiguration<bool>("EnableOpenTracking").ToString(), "Enable SendGrid open tracking", false, false, "bool")
        };
    }

    protected override async Task UpdateEmailProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken)
    {
        switch (setting.Key)
        {
            case "ApiKey":
                if (!string.IsNullOrEmpty(setting.Value))
                {
                    _apiKey = setting.Value;
                    SetConfiguration("ApiKey", setting.Value);
                }
                break;
            case "EnableTracking":
                if (bool.TryParse(setting.Value, out var enableTracking))
                    SetConfiguration("EnableTracking", enableTracking);
                break;
            case "EnableClickTracking":
                if (bool.TryParse(setting.Value, out var enableClickTracking))
                    SetConfiguration("EnableClickTracking", enableClickTracking);
                break;
            case "EnableOpenTracking":
                if (bool.TryParse(setting.Value, out var enableOpenTracking))
                    SetConfiguration("EnableOpenTracking", enableOpenTracking);
                break;
        }
    }

    protected override async Task<OperationResult> TestEmailConfigurationAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (string.IsNullOrEmpty(_apiKey))
            {
                return GatewayUtilities.CreateOperationResult(false, "API key is required");
            }

            // Test API key by making a simple API call
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

            var response = await _httpClient.GetAsync("user/profile", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                return GatewayUtilities.CreateOperationResult(true, "SendGrid configuration is valid");
            }
            else
            {
                return GatewayUtilities.CreateOperationResult(false, $"SendGrid API test failed: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            return GatewayUtilities.CreateOperationResult(false, "Configuration test failed", ex);
        }
    }

    protected override async Task<PluginManifest> GetEmailManifestAsync(CancellationToken cancellationToken)
    {
        return new PluginManifest(
            Name: "SendGrid Email Gateway",
            Version: "1.0.0",
            Description: "Email gateway implementation for SendGrid service",
            Author: "NotificationService Team",
            Type: "Email",
            Provider: "SendGrid",
            AssemblyName: "Plugin.Email.SendGrid.dll",
            EntryPoint: "Plugin.Email.SendGrid.SendGridEmailGateway",
            Dependencies: new List<PluginDependency>(),
            Configuration: new Dictionary<string, PluginConfigurationItem>
            {
                ["ApiKey"] = new("string", "SendGrid API Key", null, null, true, true),
                ["EnableTracking"] = new("bool", "Enable email tracking", null, true, false, false),
                ["EnableClickTracking"] = new("bool", "Enable click tracking", null, false, false, false),
                ["EnableOpenTracking"] = new("bool", "Enable open tracking", null, false, false, false)
            },
            SupportedFeatures: new List<string> { "SendEmail", "BulkEmail", "Templates", "Tracking", "Analytics" }
        );
    }

    #endregion

    #region Metrics Implementation (Mock implementations for now)

    protected override async Task<GatewayStatusReport> GetEmailStatusReportAsync(CancellationToken cancellationToken)
    {
        // Mock implementation - in a real scenario, this would call SendGrid's stats API
        return new GatewayStatusReport(
            IsHealthy: true,
            Status: "Operational",
            LastChecked: DateTimeOffset.UtcNow,
            ResponseTime: TimeSpan.FromMilliseconds(150)
        );
    }

    protected override async Task<IReadOnlyList<DeliveryResult>> GetEmailDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken)
    {
        // Mock implementation - would integrate with SendGrid's Event Webhook or Stats API
        var results = new List<DeliveryResult>();
        for (int i = 0; i < Math.Min(maxItems, 10); i++)
        {
            results.Add(GatewayUtilities.CreateMockDeliveryResult($"sg_{Guid.NewGuid():N}"));
        }
        return results.AsReadOnly();
    }

    protected override async Task<UsageMetrics> GetEmailUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        // Mock implementation - would call SendGrid's Stats API
        return GatewayUtilities.CreateMockUsageMetrics(from, to);
    }

    // Additional mock implementations for other metrics methods...
    protected override async Task<IReadOnlyList<GatewayErrorEntry>> GetEmailErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new List<GatewayErrorEntry>().AsReadOnly();
    }

    protected override async Task<PerformanceSnapshot> GetEmailPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken)
    {
        return new PerformanceSnapshot(DateTimeOffset.UtcNow, 100, 95, TimeSpan.FromMilliseconds(200));
    }

    protected override async Task<SlaReport> GetEmailSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new SlaReport(99.9, TimeSpan.FromMilliseconds(150), 0.1);
    }

    protected override async Task<LatencyMetrics> GetEmailLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new LatencyMetrics(TimeSpan.FromMilliseconds(150), TimeSpan.FromMilliseconds(200), TimeSpan.FromMilliseconds(100));
    }

    protected override async Task<IReadOnlyList<TrafficTrend>> GetEmailTrafficTrendsAsync(string granularity, CancellationToken cancellationToken)
    {
        return new List<TrafficTrend>().AsReadOnly();
    }

    protected override async Task<IReadOnlyList<AnomalyDetectionResult>> GetEmailAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new List<AnomalyDetectionResult>().AsReadOnly();
    }

    protected override async Task<GeneratedReport> GenerateEmailMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken)
    {
        return new GeneratedReport("email_metrics.csv", "text/csv", Array.Empty<byte>());
    }

    protected override async Task<IReadOnlyList<RetryAttemptInfo>> GetEmailRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)
    {
        return new List<RetryAttemptInfo>().AsReadOnly();
    }

    protected override async Task<IReadOnlyList<ConfigurationImpactRecord>> GetEmailChangeImpactHistoryAsync(CancellationToken cancellationToken)
    {
        return new List<ConfigurationImpactRecord>().AsReadOnly();
    }

    #endregion

    #region Message Operations (Not implemented in base class)

    public override async Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)
    {
        // SendGrid doesn't support native scheduling, would need to implement with a job scheduler
        throw new NotSupportedException("Message scheduling is not supported by SendGrid gateway");
    }

    public override async Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)
    {
        throw new NotSupportedException("Message scheduling is not supported by SendGrid gateway");
    }

    public override async Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default)
    {
        throw new NotSupportedException("Message resending is not supported by SendGrid gateway");
    }

    public override async Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        // Would integrate with SendGrid's Event Webhook
        return new MessageStatusInfo(messageId, "Delivered", DateTimeOffset.UtcNow);
    }

    public override async Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default)
    {
        // Would integrate with SendGrid's Event Webhook
        return new DeliveryReceipt(messageId, "Delivered", DateTimeOffset.UtcNow);
    }

    public override async Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(rawPayload);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

        var response = await _httpClient.PostAsync("mail/send", content, cancellationToken);
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

        return new RawGatewayResponse(response.IsSuccessStatusCode, (int)response.StatusCode, responseContent);
    }

    public override async Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        var sendGridPayload = CreateSendGridPayload(payload);
        var validation = await ValidateMessageAsync(payload, cancellationToken);
        
        return new PreparedMessage(
            sendGridPayload,
            payload.Headers ?? new Dictionary<string, string>(),
            validation
        );
    }

    public override async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_apiKey))
                return false;

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

            var response = await _httpClient.GetAsync("user/profile", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region Helper Methods

    private object CreateSendGridPayload(MessagePayload payload)
    {
        var sender = payload.From ?? GetDefaultSender();
        var senderName = GetDefaultSenderName();

        var sendGridPayload = new
        {
            personalizations = new[]
            {
                new
                {
                    to = new[] { new { email = payload.Recipient } },
                    subject = payload.Subject ?? "No Subject"
                }
            },
            from = new
            {
                email = sender,
                name = senderName
            },
            content = new[]
            {
                new
                {
                    type = IsHtmlEnabled() ? "text/html" : "text/plain",
                    value = payload.Content
                }
            },
            tracking_settings = new
            {
                click_tracking = new { enable = GetConfiguration<bool>("EnableClickTracking") },
                open_tracking = new { enable = GetConfiguration<bool>("EnableOpenTracking") }
            }
        };

        return sendGridPayload;
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _httpClient?.Dispose();
        }
        base.Dispose(disposing);
    }

    #endregion
}
