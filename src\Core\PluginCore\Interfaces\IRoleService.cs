using PluginCore.Models;

namespace PluginCore.Interfaces;

/// <summary>
/// Interface for role management service
/// </summary>
public interface IRoleService
{
    /// <summary>
    /// Gets a role by ID
    /// </summary>
    Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a role by name
    /// </summary>
    Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all roles with pagination
    /// </summary>
    Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Creates a new role
    /// </summary>
    Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing role
    /// </summary>
    Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a role
    /// </summary>
    Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Assigns a permission to a role
    /// </summary>
    Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Removes a permission from a role
    /// </summary>
    Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all permissions for a role
    /// </summary>
    Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all users assigned to a role
    /// </summary>
    Task<IReadOnlyList<User>> GetRoleUsersAsync(string roleId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for permission management service
/// </summary>
public interface IPermissionService
{
    /// <summary>
    /// Gets a permission by ID
    /// </summary>
    Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a permission by resource and action
    /// </summary>
    Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all permissions with pagination
    /// </summary>
    Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Creates a new permission
    /// </summary>
    Task<OperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing permission
    /// </summary>
    Task<OperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a permission
    /// </summary>
    Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all roles that have a specific permission
    /// </summary>
    Task<IReadOnlyList<Role>> GetPermissionRolesAsync(string permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all users that have a specific permission (directly or through roles)
    /// </summary>
    Task<IReadOnlyList<User>> GetPermissionUsersAsync(string permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Seeds default system permissions
    /// </summary>
    Task<OperationResult> SeedDefaultPermissionsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Request model for creating a role
/// </summary>
public class CreateRoleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public RoleScope Scope { get; set; } = RoleScope.Tenant;
    public bool IsSystemRole { get; set; } = false;
    public List<string> PermissionIds { get; set; } = new();
}

/// <summary>
/// Request model for updating a role
/// </summary>
public class UpdateRoleRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public RoleScope? Scope { get; set; }
}

/// <summary>
/// Request model for creating a permission
/// </summary>
public class CreatePermissionRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Resource { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public bool IsSystemPermission { get; set; } = false;
}

/// <summary>
/// Request model for updating a permission
/// </summary>
public class UpdatePermissionRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Resource { get; set; }
    public string? Action { get; set; }
}

/// <summary>
/// Predefined system permissions
/// </summary>
public static class SystemPermissions
{
    // Tenant management
    public const string TenantCreate = "tenant:create";
    public const string TenantRead = "tenant:read";
    public const string TenantUpdate = "tenant:update";
    public const string TenantDelete = "tenant:delete";
    public const string TenantSuspend = "tenant:suspend";
    
    // User management
    public const string UserCreate = "user:create";
    public const string UserRead = "user:read";
    public const string UserUpdate = "user:update";
    public const string UserDelete = "user:delete";
    public const string UserSuspend = "user:suspend";
    
    // Role management
    public const string RoleCreate = "role:create";
    public const string RoleRead = "role:read";
    public const string RoleUpdate = "role:update";
    public const string RoleDelete = "role:delete";
    public const string RoleAssign = "role:assign";
    
    // Permission management
    public const string PermissionCreate = "permission:create";
    public const string PermissionRead = "permission:read";
    public const string PermissionUpdate = "permission:update";
    public const string PermissionDelete = "permission:delete";
    public const string PermissionGrant = "permission:grant";
    
    // Plugin management
    public const string PluginRead = "plugin:read";
    public const string PluginConfigure = "plugin:configure";
    public const string PluginInstall = "plugin:install";
    public const string PluginUninstall = "plugin:uninstall";
    public const string PluginEnable = "plugin:enable";
    public const string PluginDisable = "plugin:disable";
    
    // Message management
    public const string MessageSend = "message:send";
    public const string MessageRead = "message:read";
    public const string MessageDelete = "message:delete";
    public const string MessageSchedule = "message:schedule";
    
    // Template management
    public const string TemplateCreate = "template:create";
    public const string TemplateRead = "template:read";
    public const string TemplateUpdate = "template:update";
    public const string TemplateDelete = "template:delete";
    
    // Webhook management
    public const string WebhookCreate = "webhook:create";
    public const string WebhookRead = "webhook:read";
    public const string WebhookUpdate = "webhook:update";
    public const string WebhookDelete = "webhook:delete";
    
    // System administration
    public const string SystemRead = "system:read";
    public const string SystemConfigure = "system:configure";
    public const string SystemMaintenance = "system:maintenance";
    
    // Metrics and monitoring
    public const string MetricsRead = "metrics:read";
    public const string MetricsExport = "metrics:export";
    public const string LogsRead = "logs:read";
    
    /// <summary>
    /// Gets all default system permissions
    /// </summary>
    public static IReadOnlyList<(string Name, string Description, string Resource, string Action)> GetDefaultPermissions()
    {
        return new List<(string, string, string, string)>
        {
            // Tenant permissions
            (TenantCreate, "Create new tenants", "tenant", "create"),
            (TenantRead, "View tenant information", "tenant", "read"),
            (TenantUpdate, "Update tenant settings", "tenant", "update"),
            (TenantDelete, "Delete tenants", "tenant", "delete"),
            (TenantSuspend, "Suspend/activate tenants", "tenant", "suspend"),
            
            // User permissions
            (UserCreate, "Create new users", "user", "create"),
            (UserRead, "View user information", "user", "read"),
            (UserUpdate, "Update user profiles", "user", "update"),
            (UserDelete, "Delete users", "user", "delete"),
            (UserSuspend, "Suspend/activate users", "user", "suspend"),
            
            // Role permissions
            (RoleCreate, "Create new roles", "role", "create"),
            (RoleRead, "View role information", "role", "read"),
            (RoleUpdate, "Update role definitions", "role", "update"),
            (RoleDelete, "Delete roles", "role", "delete"),
            (RoleAssign, "Assign roles to users", "role", "assign"),
            
            // Permission permissions
            (PermissionCreate, "Create new permissions", "permission", "create"),
            (PermissionRead, "View permission information", "permission", "read"),
            (PermissionUpdate, "Update permission definitions", "permission", "update"),
            (PermissionDelete, "Delete permissions", "permission", "delete"),
            (PermissionGrant, "Grant permissions to users/roles", "permission", "grant"),
            
            // Plugin permissions
            (PluginRead, "View plugin information", "plugin", "read"),
            (PluginConfigure, "Configure plugin settings", "plugin", "configure"),
            (PluginInstall, "Install new plugins", "plugin", "install"),
            (PluginUninstall, "Uninstall plugins", "plugin", "uninstall"),
            (PluginEnable, "Enable plugins", "plugin", "enable"),
            (PluginDisable, "Disable plugins", "plugin", "disable"),
            
            // Message permissions
            (MessageSend, "Send messages", "message", "send"),
            (MessageRead, "View message history", "message", "read"),
            (MessageDelete, "Delete messages", "message", "delete"),
            (MessageSchedule, "Schedule messages", "message", "schedule"),
            
            // Template permissions
            (TemplateCreate, "Create message templates", "template", "create"),
            (TemplateRead, "View message templates", "template", "read"),
            (TemplateUpdate, "Update message templates", "template", "update"),
            (TemplateDelete, "Delete message templates", "template", "delete"),
            
            // Webhook permissions
            (WebhookCreate, "Create webhooks", "webhook", "create"),
            (WebhookRead, "View webhook configurations", "webhook", "read"),
            (WebhookUpdate, "Update webhook settings", "webhook", "update"),
            (WebhookDelete, "Delete webhooks", "webhook", "delete"),
            
            // System permissions
            (SystemRead, "View system information", "system", "read"),
            (SystemConfigure, "Configure system settings", "system", "configure"),
            (SystemMaintenance, "Perform system maintenance", "system", "maintenance"),
            
            // Monitoring permissions
            (MetricsRead, "View system metrics", "metrics", "read"),
            (MetricsExport, "Export metrics data", "metrics", "export"),
            (LogsRead, "View system logs", "logs", "read")
        };
    }
}

/// <summary>
/// Predefined system roles
/// </summary>
public static class SystemRoles
{
    public const string SuperAdmin = "SuperAdmin";
    public const string TenantAdmin = "TenantAdmin";
    public const string User = "User";
    public const string Viewer = "Viewer";
    
    /// <summary>
    /// Gets default role definitions with their permissions
    /// </summary>
    public static IReadOnlyList<(string Name, string Description, RoleScope Scope, string[] Permissions)> GetDefaultRoles()
    {
        return new List<(string, string, RoleScope, string[])>
        {
            (SuperAdmin, "System administrator with full access", RoleScope.System, new[]
            {
                SystemPermissions.TenantCreate, SystemPermissions.TenantRead, SystemPermissions.TenantUpdate, 
                SystemPermissions.TenantDelete, SystemPermissions.TenantSuspend,
                SystemPermissions.UserCreate, SystemPermissions.UserRead, SystemPermissions.UserUpdate, 
                SystemPermissions.UserDelete, SystemPermissions.UserSuspend,
                SystemPermissions.RoleCreate, SystemPermissions.RoleRead, SystemPermissions.RoleUpdate, 
                SystemPermissions.RoleDelete, SystemPermissions.RoleAssign,
                SystemPermissions.PermissionCreate, SystemPermissions.PermissionRead, SystemPermissions.PermissionUpdate, 
                SystemPermissions.PermissionDelete, SystemPermissions.PermissionGrant,
                SystemPermissions.PluginRead, SystemPermissions.PluginConfigure, SystemPermissions.PluginInstall, 
                SystemPermissions.PluginUninstall, SystemPermissions.PluginEnable, SystemPermissions.PluginDisable,
                SystemPermissions.SystemRead, SystemPermissions.SystemConfigure, SystemPermissions.SystemMaintenance,
                SystemPermissions.MetricsRead, SystemPermissions.MetricsExport, SystemPermissions.LogsRead
            }),
            
            (TenantAdmin, "Tenant administrator with full tenant access", RoleScope.Tenant, new[]
            {
                SystemPermissions.UserCreate, SystemPermissions.UserRead, SystemPermissions.UserUpdate, 
                SystemPermissions.UserDelete, SystemPermissions.UserSuspend,
                SystemPermissions.RoleRead, SystemPermissions.RoleAssign,
                SystemPermissions.PluginRead, SystemPermissions.PluginConfigure, SystemPermissions.PluginEnable, SystemPermissions.PluginDisable,
                SystemPermissions.MessageSend, SystemPermissions.MessageRead, SystemPermissions.MessageDelete, SystemPermissions.MessageSchedule,
                SystemPermissions.TemplateCreate, SystemPermissions.TemplateRead, SystemPermissions.TemplateUpdate, SystemPermissions.TemplateDelete,
                SystemPermissions.WebhookCreate, SystemPermissions.WebhookRead, SystemPermissions.WebhookUpdate, SystemPermissions.WebhookDelete,
                SystemPermissions.MetricsRead, SystemPermissions.MetricsExport
            }),
            
            (User, "Regular user with message sending capabilities", RoleScope.Tenant, new[]
            {
                SystemPermissions.MessageSend, SystemPermissions.MessageRead, SystemPermissions.MessageSchedule,
                SystemPermissions.TemplateRead, SystemPermissions.TemplateCreate, SystemPermissions.TemplateUpdate,
                SystemPermissions.WebhookRead
            }),
            
            (Viewer, "Read-only access to tenant resources", RoleScope.Tenant, new[]
            {
                SystemPermissions.MessageRead, SystemPermissions.TemplateRead, SystemPermissions.WebhookRead, 
                SystemPermissions.MetricsRead
            })
        };
    }
}
