{"sourceFile": "src/Core/PluginCore/Base/SmsGatewayBase.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1751209317749, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751209358978, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -270,5 +270,238 @@\n         return default;\n     }\n \n     #endregion\n+\n+    #region IGatewayAdminPluginType Implementation\n+\n+    public virtual async Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default)\n+    {\n+        var configs = new List<GatewayConfiguration>();\n+\n+        // Add SMS-specific configuration items\n+        configs.Add(new GatewayConfiguration(\"DefaultSender\", \"Default SMS sender ID\", GetConfiguration<string>(\"DefaultSender\") ?? \"\", false));\n+        configs.Add(new GatewayConfiguration(\"MaxMessageLength\", \"Maximum SMS message length\", GetConfiguration<int>(\"MaxMessageLength\").ToString() ?? \"160\", false));\n+        configs.Add(new GatewayConfiguration(\"EnableDeliveryReports\", \"Enable SMS delivery reports\", GetConfiguration<bool>(\"EnableDeliveryReports\").ToString() ?? \"true\", false));\n+\n+        // Add provider-specific configurations\n+        var providerConfigs = await GetSmsProviderConfigurationsAsync(cancellationToken);\n+        configs.AddRange(providerConfigs);\n+\n+        return configs.AsReadOnly();\n+    }\n+\n+    public virtual async Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default)\n+    {\n+        try\n+        {\n+            foreach (var setting in settingsToUpdate)\n+            {\n+                switch (setting.Key)\n+                {\n+                    case \"DefaultSender\":\n+                        SetConfiguration(\"DefaultSender\", setting.Value);\n+                        break;\n+                    case \"MaxMessageLength\":\n+                        if (int.TryParse(setting.Value, out var maxLength))\n+                            SetConfiguration(\"MaxMessageLength\", maxLength);\n+                        break;\n+                    case \"EnableDeliveryReports\":\n+                        if (bool.TryParse(setting.Value, out var enableReports))\n+                            SetConfiguration(\"EnableDeliveryReports\", enableReports);\n+                        break;\n+                    default:\n+                        await UpdateSmsProviderConfigurationAsync(setting, cancellationToken);\n+                        break;\n+                }\n+            }\n+\n+            return new OperationResult(true, \"Configuration updated successfully\");\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"Failed to update SMS gateway configuration\");\n+            return new OperationResult(false, \"Failed to update configuration\", new[] { new ErrorDetail(\"ConfigurationError\", ex.Message) });\n+        }\n+    }\n+\n+    public virtual async Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default)\n+    {\n+        try\n+        {\n+            // Test basic SMS functionality\n+            var isAvailable = await IsAvailableAsync(cancellationToken);\n+            if (!isAvailable)\n+            {\n+                return new OperationResult(false, \"SMS gateway is not available\");\n+            }\n+\n+            // Perform SMS-specific configuration tests\n+            var testResult = await TestSmsConfigurationAsync(cancellationToken);\n+            return testResult;\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"SMS configuration test failed\");\n+            return new OperationResult(false, \"Configuration test failed\", new[] { new ErrorDetail(\"TestError\", ex.Message) });\n+        }\n+    }\n+\n+    public virtual async Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsManifestAsync(cancellationToken);\n+    }\n+\n+    #endregion\n+\n+    #region IGatewayMetricsPluginType Implementation\n+\n+    public virtual async Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsStatusReportAsync(cancellationToken);\n+    }\n+\n+    public virtual async Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsDeliveryReportsAsync(maxItems, cancellationToken);\n+    }\n+\n+    public virtual async Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsUsageMetricsAsync(from, to, cancellationToken);\n+    }\n+\n+    public virtual async Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsErrorLogAsync(from, to, cancellationToken);\n+    }\n+\n+    public virtual async Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsPerformanceSnapshotAsync(resolution, cancellationToken);\n+    }\n+\n+    public virtual async Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsSlaReportAsync(from, to, cancellationToken);\n+    }\n+\n+    public virtual async Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsLatencyMetricsAsync(from, to, cancellationToken);\n+    }\n+\n+    public virtual async Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = \"daily\", CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsTrafficTrendsAsync(granularity, cancellationToken);\n+    }\n+\n+    public virtual async Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsAnomalyReportAsync(from, to, cancellationToken);\n+    }\n+\n+    public virtual async Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default)\n+    {\n+        return await GenerateSmsMetricsReportAsync(options, cancellationToken);\n+    }\n+\n+    public virtual async Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsRetryHistoryAsync(from, to, cancellationToken);\n+    }\n+\n+    public virtual async Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default)\n+    {\n+        return await GetSmsChangeImpactHistoryAsync(cancellationToken);\n+    }\n+\n+    #endregion\n+\n+    #region SMS-Specific Abstract Methods for Admin\n+\n+    /// <summary>\n+    /// Gets SMS provider-specific configuration items.\n+    /// </summary>\n+    protected abstract Task<IEnumerable<GatewayConfiguration>> GetSmsProviderConfigurationsAsync(CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Updates SMS provider-specific configuration.\n+    /// </summary>\n+    protected abstract Task UpdateSmsProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Tests SMS provider-specific configuration.\n+    /// </summary>\n+    protected abstract Task<OperationResult> TestSmsConfigurationAsync(CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets the SMS plugin manifest.\n+    /// </summary>\n+    protected abstract Task<PluginManifest> GetSmsManifestAsync(CancellationToken cancellationToken);\n+\n+    #endregion\n+\n+    #region SMS-Specific Abstract Methods for Metrics\n+\n+    /// <summary>\n+    /// Gets SMS-specific status report.\n+    /// </summary>\n+    protected abstract Task<GatewayStatusReport> GetSmsStatusReportAsync(CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS delivery reports.\n+    /// </summary>\n+    protected abstract Task<IReadOnlyList<DeliveryResult>> GetSmsDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS usage metrics.\n+    /// </summary>\n+    protected abstract Task<UsageMetrics> GetSmsUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS error logs.\n+    /// </summary>\n+    protected abstract Task<IReadOnlyList<GatewayErrorEntry>> GetSmsErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS performance snapshot.\n+    /// </summary>\n+    protected abstract Task<PerformanceSnapshot> GetSmsPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS SLA report.\n+    /// </summary>\n+    protected abstract Task<SlaReport> GetSmsSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS latency metrics.\n+    /// </summary>\n+    protected abstract Task<LatencyMetrics> GetSmsLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS traffic trends.\n+    /// </summary>\n+    protected abstract Task<IReadOnlyList<TrafficTrend>> GetSmsTrafficTrendsAsync(string granularity, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS anomaly report.\n+    /// </summary>\n+    protected abstract Task<IReadOnlyList<AnomalyDetectionResult>> GetSmsAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Generates SMS metrics report.\n+    /// </summary>\n+    protected abstract Task<GeneratedReport> GenerateSmsMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS retry history.\n+    /// </summary>\n+    protected abstract Task<IReadOnlyList<RetryAttemptInfo>> GetSmsRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n+\n+    /// <summary>\n+    /// Gets SMS configuration change impact history.\n+    /// </summary>\n+    protected abstract Task<IReadOnlyList<ConfigurationImpactRecord>> GetSmsChangeImpactHistoryAsync(CancellationToken cancellationToken);\n+\n+    #endregion\n }\n"}, {"date": 1751209384529, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -278,11 +278,11 @@\n     {\n         var configs = new List<GatewayConfiguration>();\n \n         // Add SMS-specific configuration items\n-        configs.Add(new GatewayConfiguration(\"DefaultSender\", \"Default SMS sender ID\", GetConfiguration<string>(\"DefaultSender\") ?? \"\", false));\n-        configs.Add(new GatewayConfiguration(\"MaxMessageLength\", \"Maximum SMS message length\", GetConfiguration<int>(\"MaxMessageLength\").ToString() ?? \"160\", false));\n-        configs.Add(new GatewayConfiguration(\"EnableDeliveryReports\", \"Enable SMS delivery reports\", GetConfiguration<bool>(\"EnableDeliveryReports\").ToString() ?? \"true\", false));\n+        configs.Add(new GatewayConfiguration(\"DefaultSender\", \"Default SMS sender ID\", GetConfiguration<string>(\"DefaultSender\") ?? \"\", \"Default SMS sender ID or phone number\", false, false));\n+        configs.Add(new GatewayConfiguration(\"MaxMessageLength\", \"Maximum SMS message length\", GetConfiguration<int>(\"MaxMessageLength\").ToString() ?? \"160\", \"Maximum length for SMS messages\", false, false, \"int\"));\n+        configs.Add(new GatewayConfiguration(\"EnableDeliveryReports\", \"Enable SMS delivery reports\", GetConfiguration<bool>(\"EnableDeliveryReports\").ToString() ?? \"true\", \"Enable or disable SMS delivery reports\", false, false, \"bool\"));\n \n         // Add provider-specific configurations\n         var providerConfigs = await GetSmsProviderConfigurationsAsync(cancellationToken);\n         configs.AddRange(providerConfigs);\n"}, {"date": 1751209397682, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -298,9 +298,10 @@\n             {\n                 switch (setting.Key)\n                 {\n                     case \"DefaultSender\":\n-                        SetConfiguration(\"DefaultSender\", setting.Value);\n+                        if (setting.Value != null)\n+                            SetConfiguration(\"DefaultSender\", setting.Value);\n                         break;\n                     case \"MaxMessageLength\":\n                         if (int.TryParse(setting.Value, out var maxLength))\n                             SetConfiguration(\"MaxMessageLength\", maxLength);\n"}], "date": 1751209317749, "name": "Commit-0", "content": "using PluginCore.Interfaces;\nusing PluginCore.Models;\nusing System.ComponentModel.DataAnnotations;\nusing System.Text.RegularExpressions;\n\nnamespace PluginCore.Base;\n\n/// <summary>\n/// Abstract base class for SMS gateway plugins.\n/// Provides common SMS-specific functionality, validation, and helper methods.\n/// Implements all three gateway interfaces: Message, Admin, and Metrics.\n/// </summary>\npublic abstract class SmsGatewayBase : ISmsPlugin, IGatewayAdminPluginType, IGatewayMetricsPluginType\n{\n    protected readonly ILogger _logger;\n    protected readonly Dictionary<string, object> _configuration = new();\n\n    protected SmsGatewayBase(ILogger logger)\n    {\n        _logger = logger ?? throw new ArgumentNullException(nameof(logger));\n    }\n\n    #region IGatewayMessagePluginType Implementation\n\n    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var validationResult = await ValidateMessageAsync(payload, cancellationToken);\n            if (!validationResult.IsValid)\n            {\n                return new MessageSendResult(\n                    MessageId: Guid.NewGuid().ToString(),\n                    Timestamp: DateTimeOffset.UtcNow,\n                    Status: \"Failed\",\n                    CorrelationId: payload.CorrelationId\n                );\n            }\n\n            var normalizedPayload = NormalizeSmsPayload(payload);\n            return await SendSmsInternalAsync(normalizedPayload, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to send SMS message to {Recipient}\", payload.Recipient);\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\",\n                CorrelationId: payload.CorrelationId\n            );\n        }\n    }\n\n    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)\n    {\n        var results = new List<MessageSendResult>();\n        var validPayloads = new List<MessagePayload>();\n\n        // Validate all payloads first\n        foreach (var payload in payloads)\n        {\n            var validationResult = await ValidateMessageAsync(payload, cancellationToken);\n            if (validationResult.IsValid)\n            {\n                validPayloads.Add(NormalizeSmsPayload(payload));\n            }\n            else\n            {\n                results.Add(new MessageSendResult(\n                    MessageId: Guid.NewGuid().ToString(),\n                    Timestamp: DateTimeOffset.UtcNow,\n                    Status: \"Failed\",\n                    CorrelationId: payload.CorrelationId\n                ));\n            }\n        }\n\n        if (validPayloads.Any())\n        {\n            var bulkResults = await SendBulkSmsInternalAsync(validPayloads, cancellationToken);\n            results.AddRange(bulkResults);\n        }\n\n        return results.AsReadOnly();\n    }\n\n    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)\n    {\n        var template = await GetSmsTemplateAsync(templateId, cancellationToken);\n        if (template == null)\n        {\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\"\n            );\n        }\n\n        var content = ProcessSmsTemplate(template, templateData);\n        var payload = new MessagePayload(\n            Recipient: recipient.PhoneNumber ?? recipient.Address,\n            Content: content,\n            From: GetDefaultSender(),\n            Headers: new Dictionary<string, string> { [\"TemplateId\"] = templateId }\n        );\n\n        return await SendMessageAsync(payload, cancellationToken);\n    }\n\n    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);\n\n    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);\n\n    public virtual async Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        var errors = new List<string>();\n\n        // Validate phone number\n        if (!IsValidPhoneNumber(payload.Recipient))\n        {\n            errors.Add(\"Invalid phone number format\");\n        }\n\n        // Validate message content\n        if (string.IsNullOrWhiteSpace(payload.Content))\n        {\n            errors.Add(\"Message content cannot be empty\");\n        }\n        else if (payload.Content.Length > GetMaxMessageLength())\n        {\n            errors.Add($\"Message content exceeds maximum length of {GetMaxMessageLength()} characters\");\n        }\n\n        // Provider-specific validation\n        var providerErrors = await ValidateSmsSpecificAsync(payload, cancellationToken);\n        errors.AddRange(providerErrors);\n\n        return new ValidationResult\n        {\n            IsValid = !errors.Any(),\n            ErrorMessage = errors.Any() ? string.Join(\"; \", errors) : null\n        };\n    }\n\n    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);\n\n    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);\n\n    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);\n\n    #endregion\n\n    #region SMS-Specific Abstract Methods\n\n    /// <summary>\n    /// Sends a single SMS message using the provider's API.\n    /// </summary>\n    protected abstract Task<MessageSendResult> SendSmsInternalAsync(MessagePayload payload, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Sends multiple SMS messages in a single operation.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkSmsInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets an SMS template by ID.\n    /// </summary>\n    protected abstract Task<string?> GetSmsTemplateAsync(string templateId, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Performs provider-specific SMS validation.\n    /// </summary>\n    protected abstract Task<IEnumerable<string>> ValidateSmsSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);\n\n    #endregion\n\n    #region SMS-Specific Helper Methods\n\n    /// <summary>\n    /// Validates phone number format for SMS.\n    /// </summary>\n    protected virtual bool IsValidPhoneNumber(string phoneNumber)\n    {\n        if (string.IsNullOrWhiteSpace(phoneNumber))\n            return false;\n\n        // Remove common formatting characters\n        var cleaned = Regex.Replace(phoneNumber, @\"[\\s\\-\\(\\)\\+]\", \"\");\n        \n        // Basic validation: should be 10-15 digits\n        return Regex.IsMatch(cleaned, @\"^\\d{10,15}$\");\n    }\n\n    /// <summary>\n    /// Gets the maximum message length for SMS (default 160 characters).\n    /// </summary>\n    protected virtual int GetMaxMessageLength() => 160;\n\n    /// <summary>\n    /// Normalizes SMS payload for consistent processing.\n    /// </summary>\n    protected virtual MessagePayload NormalizeSmsPayload(MessagePayload payload)\n    {\n        var normalizedRecipient = NormalizePhoneNumber(payload.Recipient);\n        return payload with { Recipient = normalizedRecipient };\n    }\n\n    /// <summary>\n    /// Normalizes phone number format.\n    /// </summary>\n    protected virtual string NormalizePhoneNumber(string phoneNumber)\n    {\n        if (string.IsNullOrWhiteSpace(phoneNumber))\n            return phoneNumber;\n\n        // Remove formatting and ensure it starts with +\n        var cleaned = Regex.Replace(phoneNumber, @\"[\\s\\-\\(\\)]\", \"\");\n        if (!cleaned.StartsWith(\"+\"))\n        {\n            cleaned = \"+\" + cleaned;\n        }\n        return cleaned;\n    }\n\n    /// <summary>\n    /// Processes SMS template with data substitution.\n    /// </summary>\n    protected virtual string ProcessSmsTemplate(string template, IDictionary<string, string> templateData)\n    {\n        var result = template;\n        foreach (var kvp in templateData)\n        {\n            result = result.Replace($\"{{{kvp.Key}}}\", kvp.Value);\n        }\n        return result;\n    }\n\n    /// <summary>\n    /// Gets the default sender for SMS messages.\n    /// </summary>\n    protected virtual string? GetDefaultSender()\n    {\n        return _configuration.TryGetValue(\"DefaultSender\", out var sender) ? sender?.ToString() : null;\n    }\n\n    /// <summary>\n    /// Sets configuration value.\n    /// </summary>\n    protected virtual void SetConfiguration(string key, object value)\n    {\n        _configuration[key] = value;\n    }\n\n    /// <summary>\n    /// Gets configuration value.\n    /// </summary>\n    protected virtual T? GetConfiguration<T>(string key)\n    {\n        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)\n        {\n            return typedValue;\n        }\n        return default;\n    }\n\n    #endregion\n}\n"}]}