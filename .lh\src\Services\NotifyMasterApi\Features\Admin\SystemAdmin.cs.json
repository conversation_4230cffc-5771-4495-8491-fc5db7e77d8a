{"sourceFile": "src/Services/NotifyMasterApi/Features/Admin/SystemAdmin.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751190743265, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751190743265, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing NotifyMasterApi.Interfaces;\r\nusing NotifyMasterApi.Services;\r\nusing NotifyMasterApi.Documentation;\r\nusing System.Diagnostics;\r\n\r\nnamespace NotifyMasterApi.Features.Admin;\r\n\r\npublic class GetSystemStatusEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly IPluginManager _pluginManager;\r\n    private readonly ILogger<GetSystemStatusEndpoint> _logger;\r\n\r\n    public GetSystemStatusEndpoint(IPluginManager pluginManager, ILogger<GetSystemStatusEndpoint> logger)\r\n    {\r\n        _pluginManager = pluginManager;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        this.ConfigureAdminEndpoint(\r\n            \"GET\",\r\n            \"/api/admin/system/status\",\r\n            \"System Status Overview\",\r\n            \"Get comprehensive system status information for administrative monitoring and troubleshooting.\\n\\n\" +\r\n            \"## 🎯 System Overview\\n\" +\r\n            \"- **Application Status**: Core service health and uptime\\n\" +\r\n            \"- **Plugin Status**: All loaded plugins and their health\\n\" +\r\n            \"- **Resource Usage**: Memory, CPU, and disk utilization\\n\" +\r\n            \"- **Performance Metrics**: Response times and throughput\\n\" +\r\n            \"- **Error Rates**: Recent error statistics and trends\\n\\n\" +\r\n            \"## 📊 Monitoring Data\\n\" +\r\n            \"- Real-time system metrics\\n\" +\r\n            \"- Plugin connectivity status\\n\" +\r\n            \"- Database performance indicators\\n\" +\r\n            \"- Queue depths and processing rates\\n\" +\r\n            \"- External service dependencies\\n\\n\" +\r\n            \"## 🔍 Diagnostic Information\\n\" +\r\n            \"- Application version and build info\\n\" +\r\n            \"- Environment configuration\\n\" +\r\n            \"- Feature flags and capabilities\\n\" +\r\n            \"- Recent system events and logs\\n\" +\r\n            \"- Performance bottlenecks\\n\\n\" +\r\n            \"## 🚨 Alert Conditions\\n\" +\r\n            \"- High memory usage (>80%)\\n\" +\r\n            \"- Plugin failures or disconnections\\n\" +\r\n            \"- Database connectivity issues\\n\" +\r\n            \"- Queue backlog warnings\\n\" +\r\n            \"- External service timeouts\\n\\n\" +\r\n            \"## 📈 Historical Data\\n\" +\r\n            \"- System uptime statistics\\n\" +\r\n            \"- Performance trend analysis\\n\" +\r\n            \"- Error rate patterns\\n\" +\r\n            \"- Resource usage history\",\r\n            new[] { \"Monitoring\", \"Diagnostics\", \"Operations\" }\r\n        );\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var plugins = await _pluginManager.GetLoadedPluginsAsync();\r\n            var enabledPlugins = plugins.Where(p => p.IsEnabled).ToList();\r\n\r\n            var systemStatus = new\r\n            {\r\n                System = new\r\n                {\r\n                    Status = \"Running\",\r\n                    Timestamp = DateTime.UtcNow,\r\n                    Version = \"2.0.0\",\r\n                    Environment = Environment.GetEnvironmentVariable(\"ASPNETCORE_ENVIRONMENT\") ?? \"Development\",\r\n                    LoadedPlugins = plugins.Count(),\r\n                    EnabledPlugins = enabledPlugins.Count\r\n                },\r\n                Services = new\r\n                {\r\n                    Email = new\r\n                    {\r\n                        Available = enabledPlugins.Any(p => p.Type.ToString() == \"Email\"),\r\n                        Providers = enabledPlugins.Where(p => p.Type.ToString() == \"Email\").Select(p => p.Provider).ToList()\r\n                    },\r\n                    SMS = new\r\n                    {\r\n                        Available = enabledPlugins.Any(p => p.Type.ToString() == \"Sms\"),\r\n                        Providers = enabledPlugins.Where(p => p.Type.ToString() == \"Sms\").Select(p => p.Provider).ToList()\r\n                    },\r\n                    Push = new\r\n                    {\r\n                        Available = enabledPlugins.Any(p => p.Type.ToString() == \"PushNotification\"),\r\n                        Providers = enabledPlugins.Where(p => p.Type.ToString() == \"PushNotification\").Select(p => p.Provider).ToList()\r\n                    },\r\n                    Messaging = new\r\n                    {\r\n                        Available = enabledPlugins.Any(p => p.Type.ToString() == \"Messaging\"),\r\n                        Providers = enabledPlugins.Where(p => p.Type.ToString() == \"Messaging\").Select(p => p.Provider).ToList()\r\n                    }\r\n                },\r\n                Infrastructure = new\r\n                {\r\n                    Database = new { Type = \"InMemory\", Status = \"Active\" },\r\n                    Logging = new { Provider = \"Serilog\", Status = \"Active\" },\r\n                    ApiDocumentation = new { Provider = \"Scalar\", Status = \"Active\" },\r\n                    HealthChecks = new { Status = \"Active\", Endpoint = \"/health/ready\" }\r\n                },\r\n                Performance = new\r\n                {\r\n                    ProcessId = Environment.ProcessId,\r\n                    WorkingSet = GC.GetTotalMemory(false),\r\n                    StartTime = Process.GetCurrentProcess().StartTime,\r\n                    Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime\r\n                }\r\n            };\r\n\r\n            await SendOkAsync(systemStatus, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting system status\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetSystemMetricsEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly IPluginManager _pluginManager;\r\n    private readonly INotificationLoggingService _loggingService;\r\n    private readonly ILogger<GetSystemMetricsEndpoint> _logger;\r\n\r\n    public GetSystemMetricsEndpoint(\r\n        IPluginManager pluginManager,\r\n        INotificationLoggingService loggingService,\r\n        ILogger<GetSystemMetricsEndpoint> logger)\r\n    {\r\n        _pluginManager = pluginManager;\r\n        _loggingService = loggingService;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/admin/metrics\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get system metrics\";\r\n            s.Description = \"Get comprehensive system metrics across all plugins\";\r\n            s.Responses[200] = \"System metrics retrieved successfully\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"System Admin\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var plugins = await _pluginManager.GetLoadedPluginsAsync();\r\n            var enabledPlugins = plugins.Where(p => p.IsEnabled).ToList();\r\n\r\n            var pluginMetrics = new List<object>();\r\n            foreach (var plugin in enabledPlugins)\r\n            {\r\n                try\r\n                {\r\n                    var metrics = await _pluginManager.GetPluginMetricsAsync(plugin.Name ?? \"\");\r\n                    pluginMetrics.Add(new\r\n                    {\r\n                        PluginName = plugin.Name,\r\n                        Provider = plugin.Provider,\r\n                        Type = plugin.Type,\r\n                        Metrics = metrics\r\n                    });\r\n                }\r\n                catch (Exception ex)\r\n                {\r\n                    _logger.LogWarning(ex, \"Could not get metrics for plugin {PluginName}\", plugin.Name);\r\n                }\r\n            }\r\n\r\n            var systemMetrics = new\r\n            {\r\n                Overview = new\r\n                {\r\n                    TotalPlugins = plugins.Count(),\r\n                    ActivePlugins = enabledPlugins.Count,\r\n                    PluginTypes = enabledPlugins.GroupBy(p => p.Type.ToString()).ToDictionary(g => g.Key ?? \"Unknown\", g => g.Count())\r\n                },\r\n                PluginMetrics = pluginMetrics,\r\n                System = new\r\n                {\r\n                    Timestamp = DateTime.UtcNow,\r\n                    CollectionPeriod = \"Real-time\",\r\n                    MemoryUsage = GC.GetTotalMemory(false),\r\n                    ProcessUptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime\r\n                }\r\n            };\r\n\r\n            await SendOkAsync(systemMetrics, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting system metrics\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetSystemConfigurationEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly IConfiguration _configuration;\r\n    private readonly IPluginManager _pluginManager;\r\n    private readonly ILogger<GetSystemConfigurationEndpoint> _logger;\r\n\r\n    public GetSystemConfigurationEndpoint(\r\n        IConfiguration configuration,\r\n        IPluginManager pluginManager,\r\n        ILogger<GetSystemConfigurationEndpoint> logger)\r\n    {\r\n        _configuration = configuration;\r\n        _pluginManager = pluginManager;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/admin/config\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get system configuration\";\r\n            s.Description = \"Get current system configuration settings\";\r\n            s.Responses[200] = \"Configuration retrieved successfully\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"System Admin\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var plugins = await _pluginManager.GetLoadedPluginsAsync();\r\n\r\n            var systemConfig = new\r\n            {\r\n                Application = new\r\n                {\r\n                    Name = \"NotificationService\",\r\n                    Version = \"2.0.0\",\r\n                    Environment = _configuration[\"ASPNETCORE_ENVIRONMENT\"] ?? \"Development\",\r\n                    Framework = \".NET 9.0\"\r\n                },\r\n                Features = new\r\n                {\r\n                    PluginManagement = true,\r\n                    MetricsTracking = true,\r\n                    HealthMonitoring = true,\r\n                    FeatureDetection = true,\r\n                    FastEndpoints = true,\r\n                    ScalarDocumentation = true\r\n                },\r\n                Plugins = new\r\n                {\r\n                    TotalLoaded = plugins.Count(),\r\n                    EnabledCount = plugins.Count(p => p.IsEnabled),\r\n                    ByType = plugins.GroupBy(p => p.Type.ToString()).ToDictionary(\r\n                        g => g.Key ?? \"Unknown\",\r\n                        g => new\r\n                        {\r\n                            Total = g.Count(),\r\n                            Enabled = g.Count(p => p.IsEnabled),\r\n                            Providers = g.Select(p => p.Provider).Distinct().ToList()\r\n                        }\r\n                    )\r\n                },\r\n                Infrastructure = new\r\n                {\r\n                    Database = new\r\n                    {\r\n                        Provider = \"EntityFramework\",\r\n                        Type = \"InMemory\",\r\n                        Status = \"Active\"\r\n                    },\r\n                    Logging = new\r\n                    {\r\n                        Provider = \"Serilog\",\r\n                        Sinks = new[] { \"Console\", \"Debug\" },\r\n                        Level = \"Information\"\r\n                    },\r\n                    Documentation = new\r\n                    {\r\n                        Provider = \"Scalar\",\r\n                        OpenApiVersion = \"3.0\",\r\n                        Endpoint = \"/scalar/v1\"\r\n                    }\r\n                },\r\n                Endpoints = new\r\n                {\r\n                    Health = \"/health/ready\",\r\n                    Plugins = \"/api/plugins\",\r\n                    Features = \"/api/system/features\",\r\n                    Documentation = \"/scalar/v1\",\r\n                    Admin = \"/api/admin\"\r\n                }\r\n            };\r\n\r\n            await SendOkAsync(systemConfig, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting system configuration\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n"}]}