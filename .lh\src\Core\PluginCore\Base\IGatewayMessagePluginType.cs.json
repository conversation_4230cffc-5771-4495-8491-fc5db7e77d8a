{"sourceFile": "src/Core/PluginCore/Base/IGatewayMessagePluginType.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751197349485, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751197380992, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,9 @@\n     /// <summary>\r\n     /// Sends multiple messages in one operation.\r\n     /// Optimized for bulk notifications, campaigns, and alerts.\r\n     /// </summary>\r\n-    Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(   IEnumerable<MessagePayload> payloads,\r\n+    Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads,\r\n         CancellationToken cancellationToken = default);\r\n \r\n     /// <summary>\r\n     /// Sends a message using a registered template and dynamic data model.\r\n"}, {"date": 1751197401804, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,10 +15,9 @@\n     /// <summary>\r\n     /// Sends a message using a registered template and dynamic data model.\r\n     /// Automatically resolves placeholders before dispatch.\r\n     /// </summary>\r\n-    Task<MessageSendResult> SendTemplatedMessageAsync(string templateId,IDictionary<string, string> templateData,\r\n-        Recipient recipient,\r\n+    Task<MessageSendResult> SendTemplatedMessageAsync(string templateId,IDictionary<string, string> templateData,        Recipient recipient,\r\n         CancellationToken cancellationToken = default);\r\n \r\n     /// <summary>\r\n     /// Schedules a message to be delivered at a specific time.\r\n"}], "date": 1751197349485, "name": "Commit-0", "content": "public interface IGatewayMessagePluginType\r\n{\r\n    /// <summary>\r\n    /// Sends a single outbound message through the gateway.\r\n    /// Supports SMS, Email, Push, or any other transport type the plugin handles.\r\n    /// </summary>\r\n    Task<MessageSendResult> SendMessageAsync(\r\n        MessagePayload payload,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Sends multiple messages in one operation.\r\n    /// Optimized for bulk notifications, campaigns, and alerts.\r\n    /// </summary>\r\n    Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(\r\n        IEnumerable<MessagePayload> payloads,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Sends a message using a registered template and dynamic data model.\r\n    /// Automatically resolves placeholders before dispatch.\r\n    /// </summary>\r\n    Task<MessageSendResult> SendTemplatedMessageAsync(\r\n        string templateId,\r\n        IDictionary<string, string> templateData,\r\n        Recipient recipient,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Schedules a message to be delivered at a specific time.\r\n    /// Plugin must declare scheduling support explicitly.\r\n    /// </summary>\r\n    Task<MessageScheduleResult> ScheduleMessageAsync(\r\n        MessagePayload payload,\r\n        DateTimeOffset scheduledTime,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Cancels a previously scheduled message.\r\n    /// Useful for time-sensitive or retractable communications.\r\n    /// </summary>\r\n    Task<OperationResult> CancelScheduledMessageAsync(\r\n        string scheduledMessageId,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Attempts to resend a previously failed or pending message by ID.\r\n    /// May preserve or regenerate payload metadata as needed.\r\n    /// </summary>\r\n    Task<MessageSendResult> ResendMessageAsync(\r\n        string originalMessageId,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves the real-time status of a specific message, such as queued, sent, failed, or unknown.\r\n    /// Does not include analytics or performance data.\r\n    /// </summary>\r\n    Task<MessageStatusInfo> GetMessageStatusAsync(\r\n        string messageId,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves a provider-issued delivery receipt if available.\r\n    /// Includes response codes, destination metadata, and delivery confirmation.\r\n    /// </summary>\r\n    Task<DeliveryReceipt> GetDeliveryReceiptAsync(\r\n        string messageId,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Validates a message against plugin-specific constraints:\r\n    /// length, encoding, headers, attachments, etc.\r\n    /// Returns structured feedback but does not send the message.\r\n    /// </summary>\r\n    Task<ValidationResult> ValidateMessageAsync(\r\n        MessagePayload payload,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Sends an unstructured or provider-native request payload.\r\n    /// Intended for custom or advanced use cases not covered by standard abstractions.\r\n    /// </summary>\r\n    Task<RawGatewayResponse> SendRawPayloadAsync(\r\n        object rawPayload,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Prepares a message for sending without dispatching it.\r\n    /// Used for dry-run rendering, template previewing, or transformation inspection.\r\n    /// </summary>\r\n    Task<PreparedMessage> PrepareMessageAsync(\r\n        MessagePayload payload,\r\n        CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Checks if the gateway is currently accepting traffic and ready for message operations.\r\n    /// Used to preflight send attempts or circuit-break outbound queues.\r\n    /// </summary>\r\n    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);\r\n}"}]}