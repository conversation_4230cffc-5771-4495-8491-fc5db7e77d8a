{"sourceFile": "src/Core/PluginCore/Base/WebAppGatewayBase.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751209703591, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751209703591, "name": "Commit-0", "content": "using PluginCore.Interfaces;\nusing PluginCore.Models;\nusing System.ComponentModel.DataAnnotations;\nusing System.Text.Json;\nusing System.Text.RegularExpressions;\n\nnamespace PluginCore.Base;\n\n/// <summary>\n/// Abstract base class for Web Application gateway plugins.\n/// Provides common web notification-specific functionality, validation, and helper methods.\n/// Implements all three gateway interfaces: Message, Admin, and Metrics.\n/// Supports browser notifications, in-app messages, web push, and real-time notifications.\n/// </summary>\npublic abstract class WebAppGatewayBase : IWebAppPlugin, IGatewayAdminPluginType, IGatewayMetricsPluginType\n{\n    protected readonly ILogger _logger;\n    protected readonly Dictionary<string, object> _configuration = new();\n\n    protected WebAppGatewayBase(ILogger logger)\n    {\n        _logger = logger ?? throw new ArgumentNullException(nameof(logger));\n    }\n\n    #region IGatewayMessagePluginType Implementation\n\n    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var validationResult = await ValidateMessageAsync(payload, cancellationToken);\n            if (!validationResult.IsValid)\n            {\n                return new MessageSendResult(\n                    MessageId: Guid.NewGuid().ToString(),\n                    Timestamp: DateTimeOffset.UtcNow,\n                    Status: \"Failed\",\n                    CorrelationId: payload.CorrelationId\n                );\n            }\n\n            var normalizedPayload = NormalizeWebAppPayload(payload);\n            return await SendWebAppInternalAsync(normalizedPayload, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to send web app notification to {Recipient}\", payload.Recipient);\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\",\n                CorrelationId: payload.CorrelationId\n            );\n        }\n    }\n\n    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)\n    {\n        var results = new List<MessageSendResult>();\n        var validPayloads = new List<MessagePayload>();\n\n        // Validate all payloads first\n        foreach (var payload in payloads)\n        {\n            var validationResult = await ValidateMessageAsync(payload, cancellationToken);\n            if (validationResult.IsValid)\n            {\n                validPayloads.Add(NormalizeWebAppPayload(payload));\n            }\n            else\n            {\n                results.Add(new MessageSendResult(\n                    MessageId: Guid.NewGuid().ToString(),\n                    Timestamp: DateTimeOffset.UtcNow,\n                    Status: \"Failed\",\n                    CorrelationId: payload.CorrelationId\n                ));\n            }\n        }\n\n        if (validPayloads.Any())\n        {\n            var bulkResults = await SendBulkWebAppInternalAsync(validPayloads, cancellationToken);\n            results.AddRange(bulkResults);\n        }\n\n        return results.AsReadOnly();\n    }\n\n    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)\n    {\n        var template = await GetWebAppTemplateAsync(templateId, cancellationToken);\n        if (template == null)\n        {\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\"\n            );\n        }\n\n        var title = ProcessWebAppTemplate(template.Title, templateData);\n        var content = ProcessWebAppTemplate(template.Content, templateData);\n        var actionUrl = template.ActionUrl != null ? ProcessWebAppTemplate(template.ActionUrl, templateData) : null;\n        \n        var payload = new MessagePayload(\n            Recipient: recipient.UserId ?? recipient.Address,\n            Content: content,\n            Subject: title,\n            Headers: new Dictionary<string, string> \n            { \n                [\"TemplateId\"] = templateId,\n                [\"NotificationType\"] = template.Type ?? \"toast\",\n                [\"ActionUrl\"] = actionUrl ?? \"\",\n                [\"IconUrl\"] = template.IconUrl ?? \"\"\n            }\n        );\n\n        return await SendMessageAsync(payload, cancellationToken);\n    }\n\n    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);\n\n    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);\n\n    public virtual async Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        var errors = new List<string>();\n\n        // Validate recipient (user ID, session ID, or connection ID)\n        if (!IsValidWebAppRecipient(payload.Recipient))\n        {\n            errors.Add(\"Invalid web app recipient format (should be user ID, session ID, or connection ID)\");\n        }\n\n        // Validate message content\n        if (string.IsNullOrWhiteSpace(payload.Content))\n        {\n            errors.Add(\"Web app notification content cannot be empty\");\n        }\n        else if (payload.Content.Length > GetMaxContentLength())\n        {\n            errors.Add($\"Web app notification content exceeds maximum length of {GetMaxContentLength()} characters\");\n        }\n\n        // Validate title\n        if (!string.IsNullOrWhiteSpace(payload.Subject) && payload.Subject.Length > GetMaxTitleLength())\n        {\n            errors.Add($\"Web app notification title exceeds maximum length of {GetMaxTitleLength()} characters\");\n        }\n\n        // Validate notification type\n        var notificationType = payload.Headers?.GetValueOrDefault(\"NotificationType\") ?? \"toast\";\n        if (!IsValidNotificationType(notificationType))\n        {\n            errors.Add($\"Invalid notification type: {notificationType}\");\n        }\n\n        // Provider-specific validation\n        var providerErrors = await ValidateWebAppSpecificAsync(payload, cancellationToken);\n        errors.AddRange(providerErrors);\n\n        return new ValidationResult\n        {\n            IsValid = !errors.Any(),\n            ErrorMessage = errors.Any() ? string.Join(\"; \", errors) : null\n        };\n    }\n\n    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);\n\n    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);\n\n    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);\n\n    #endregion\n\n    #region IGatewayAdminPluginType Implementation\n\n    public virtual async Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default)\n    {\n        var configs = new List<GatewayConfiguration>();\n        \n        // Add WebApp-specific configuration items\n        configs.Add(new GatewayConfiguration(\"SupportedTypes\", \"Supported notification types\", GetConfiguration<string>(\"SupportedTypes\") ?? \"toast,banner,modal,inline\", \"Comma-separated list of supported notification types\", false, false));\n        configs.Add(new GatewayConfiguration(\"MaxTitleLength\", \"Maximum title length\", GetConfiguration<int>(\"MaxTitleLength\").ToString() ?? \"100\", \"Maximum length for notification title\", false, false, \"int\"));\n        configs.Add(new GatewayConfiguration(\"MaxContentLength\", \"Maximum content length\", GetConfiguration<int>(\"MaxContentLength\").ToString() ?? \"500\", \"Maximum length for notification content\", false, false, \"int\"));\n        configs.Add(new GatewayConfiguration(\"EnableRealTime\", \"Enable real-time notifications\", GetConfiguration<bool>(\"EnableRealTime\").ToString() ?? \"true\", \"Enable or disable real-time notification delivery\", false, false, \"bool\"));\n        configs.Add(new GatewayConfiguration(\"EnablePersistence\", \"Enable notification persistence\", GetConfiguration<bool>(\"EnablePersistence\").ToString() ?? \"true\", \"Enable or disable notification persistence\", false, false, \"bool\"));\n        configs.Add(new GatewayConfiguration(\"DefaultIconUrl\", \"Default notification icon URL\", GetConfiguration<string>(\"DefaultIconUrl\") ?? \"\", \"Default icon URL for notifications\", false, false));\n        \n        // Add provider-specific configurations\n        var providerConfigs = await GetWebAppProviderConfigurationsAsync(cancellationToken);\n        configs.AddRange(providerConfigs);\n        \n        return configs.AsReadOnly();\n    }\n\n    public virtual async Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            foreach (var setting in settingsToUpdate)\n            {\n                switch (setting.Key)\n                {\n                    case \"SupportedTypes\":\n                        if (setting.Value != null)\n                            SetConfiguration(\"SupportedTypes\", setting.Value);\n                        break;\n                    case \"MaxTitleLength\":\n                        if (int.TryParse(setting.Value, out var maxTitleLength))\n                            SetConfiguration(\"MaxTitleLength\", maxTitleLength);\n                        break;\n                    case \"MaxContentLength\":\n                        if (int.TryParse(setting.Value, out var maxContentLength))\n                            SetConfiguration(\"MaxContentLength\", maxContentLength);\n                        break;\n                    case \"EnableRealTime\":\n                        if (bool.TryParse(setting.Value, out var enableRealTime))\n                            SetConfiguration(\"EnableRealTime\", enableRealTime);\n                        break;\n                    case \"EnablePersistence\":\n                        if (bool.TryParse(setting.Value, out var enablePersistence))\n                            SetConfiguration(\"EnablePersistence\", enablePersistence);\n                        break;\n                    case \"DefaultIconUrl\":\n                        if (setting.Value != null)\n                            SetConfiguration(\"DefaultIconUrl\", setting.Value);\n                        break;\n                    default:\n                        await UpdateWebAppProviderConfigurationAsync(setting, cancellationToken);\n                        break;\n                }\n            }\n\n            return new OperationResult(true, \"Configuration updated successfully\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to update WebApp gateway configuration\");\n            return new OperationResult(false, \"Failed to update configuration\", new[] { new ErrorDetail(\"ConfigurationError\", ex.Message) });\n        }\n    }\n\n    public virtual async Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Test basic WebApp functionality\n            var isAvailable = await IsAvailableAsync(cancellationToken);\n            if (!isAvailable)\n            {\n                return new OperationResult(false, \"WebApp gateway is not available\");\n            }\n\n            // Perform WebApp-specific configuration tests\n            var testResult = await TestWebAppConfigurationAsync(cancellationToken);\n            return testResult;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"WebApp configuration test failed\");\n            return new OperationResult(false, \"Configuration test failed\", new[] { new ErrorDetail(\"TestError\", ex.Message) });\n        }\n    }\n\n    public virtual async Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppManifestAsync(cancellationToken);\n    }\n\n    #endregion\n\n    #region IGatewayMetricsPluginType Implementation\n\n    public virtual async Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppStatusReportAsync(cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppDeliveryReportsAsync(maxItems, cancellationToken);\n    }\n\n    public virtual async Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppUsageMetricsAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppErrorLogAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppPerformanceSnapshotAsync(resolution, cancellationToken);\n    }\n\n    public virtual async Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppSlaReportAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppLatencyMetricsAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = \"daily\", CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppTrafficTrendsAsync(granularity, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppAnomalyReportAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default)\n    {\n        return await GenerateWebAppMetricsReportAsync(options, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppRetryHistoryAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetWebAppChangeImpactHistoryAsync(cancellationToken);\n    }\n\n    #endregion\n\n    #region WebApp-Specific Abstract Methods\n\n    /// <summary>\n    /// Sends a single web app notification using the provider's API.\n    /// </summary>\n    protected abstract Task<MessageSendResult> SendWebAppInternalAsync(MessagePayload payload, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Sends multiple web app notifications in a single operation.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkWebAppInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets a web app notification template by ID.\n    /// </summary>\n    protected abstract Task<WebAppTemplate?> GetWebAppTemplateAsync(string templateId, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Performs provider-specific web app notification validation.\n    /// </summary>\n    protected abstract Task<IEnumerable<string>> ValidateWebAppSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);\n\n    #endregion\n\n    #region WebApp-Specific Abstract Methods for Admin\n\n    /// <summary>\n    /// Gets web app provider-specific configuration items.\n    /// </summary>\n    protected abstract Task<IEnumerable<GatewayConfiguration>> GetWebAppProviderConfigurationsAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Updates web app provider-specific configuration.\n    /// </summary>\n    protected abstract Task UpdateWebAppProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Tests web app provider-specific configuration.\n    /// </summary>\n    protected abstract Task<OperationResult> TestWebAppConfigurationAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets the web app plugin manifest.\n    /// </summary>\n    protected abstract Task<PluginManifest> GetWebAppManifestAsync(CancellationToken cancellationToken);\n\n    #endregion\n\n    #region WebApp-Specific Abstract Methods for Metrics\n\n    /// <summary>\n    /// Gets web app-specific status report.\n    /// </summary>\n    protected abstract Task<GatewayStatusReport> GetWebAppStatusReportAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app delivery reports.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<DeliveryResult>> GetWebAppDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app usage metrics.\n    /// </summary>\n    protected abstract Task<UsageMetrics> GetWebAppUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app error logs.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<GatewayErrorEntry>> GetWebAppErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app performance snapshot.\n    /// </summary>\n    protected abstract Task<PerformanceSnapshot> GetWebAppPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app SLA report.\n    /// </summary>\n    protected abstract Task<SlaReport> GetWebAppSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app latency metrics.\n    /// </summary>\n    protected abstract Task<LatencyMetrics> GetWebAppLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app traffic trends.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<TrafficTrend>> GetWebAppTrafficTrendsAsync(string granularity, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app anomaly report.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<AnomalyDetectionResult>> GetWebAppAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Generates web app metrics report.\n    /// </summary>\n    protected abstract Task<GeneratedReport> GenerateWebAppMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app retry history.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<RetryAttemptInfo>> GetWebAppRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets web app configuration change impact history.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<ConfigurationImpactRecord>> GetWebAppChangeImpactHistoryAsync(CancellationToken cancellationToken);\n\n    #endregion\n\n    #region WebApp-Specific Helper Methods\n\n    /// <summary>\n    /// Validates web app recipient format (user ID, session ID, or connection ID).\n    /// </summary>\n    protected virtual bool IsValidWebAppRecipient(string recipient)\n    {\n        if (string.IsNullOrWhiteSpace(recipient))\n            return false;\n\n        // Basic validation: should be a reasonable length and contain valid characters\n        return recipient.Length >= 3 && recipient.Length <= 256 &&\n               recipient.All(c => char.IsLetterOrDigit(c) || c == '-' || c == '_' || c == '@' || c == '.');\n    }\n\n    /// <summary>\n    /// Validates notification type for web app notifications.\n    /// </summary>\n    protected virtual bool IsValidNotificationType(string notificationType)\n    {\n        var supportedTypes = GetSupportedNotificationTypes();\n        return supportedTypes.Contains(notificationType.ToLowerInvariant());\n    }\n\n    /// <summary>\n    /// Gets the maximum title length for web app notifications (default 100 characters).\n    /// </summary>\n    protected virtual int GetMaxTitleLength() => GetConfiguration<int>(\"MaxTitleLength\") > 0 ? GetConfiguration<int>(\"MaxTitleLength\") : 100;\n\n    /// <summary>\n    /// Gets the maximum content length for web app notifications (default 500 characters).\n    /// </summary>\n    protected virtual int GetMaxContentLength() => GetConfiguration<int>(\"MaxContentLength\") > 0 ? GetConfiguration<int>(\"MaxContentLength\") : 500;\n\n    /// <summary>\n    /// Normalizes web app payload for consistent processing.\n    /// </summary>\n    protected virtual MessagePayload NormalizeWebAppPayload(MessagePayload payload)\n    {\n        var normalizedRecipient = payload.Recipient.Trim();\n        var normalizedTitle = payload.Subject?.Trim() ?? \"\";\n        var normalizedContent = payload.Content.Trim();\n\n        return payload with\n        {\n            Recipient = normalizedRecipient,\n            Subject = normalizedTitle,\n            Content = normalizedContent\n        };\n    }\n\n    /// <summary>\n    /// Processes web app template with data substitution.\n    /// </summary>\n    protected virtual string ProcessWebAppTemplate(string template, IDictionary<string, string> templateData)\n    {\n        var result = template;\n        foreach (var kvp in templateData)\n        {\n            result = result.Replace($\"{{{kvp.Key}}}\", kvp.Value);\n        }\n        return result;\n    }\n\n    /// <summary>\n    /// Gets supported notification types for web app notifications.\n    /// </summary>\n    protected virtual string[] GetSupportedNotificationTypes()\n    {\n        var types = GetConfiguration<string>(\"SupportedTypes\") ?? \"toast,banner,modal,inline\";\n        return types.Split(',', StringSplitOptions.RemoveEmptyEntries)\n                   .Select(t => t.Trim().ToLowerInvariant())\n                   .ToArray();\n    }\n\n    /// <summary>\n    /// Checks if real-time notifications are enabled.\n    /// </summary>\n    protected virtual bool IsRealTimeEnabled()\n    {\n        return GetConfiguration<bool>(\"EnableRealTime\");\n    }\n\n    /// <summary>\n    /// Checks if notification persistence is enabled.\n    /// </summary>\n    protected virtual bool IsPersistenceEnabled()\n    {\n        return GetConfiguration<bool>(\"EnablePersistence\");\n    }\n\n    /// <summary>\n    /// Gets the default icon URL for notifications.\n    /// </summary>\n    protected virtual string? GetDefaultIconUrl()\n    {\n        return GetConfiguration<string>(\"DefaultIconUrl\");\n    }\n\n    /// <summary>\n    /// Sets configuration value.\n    /// </summary>\n    protected virtual void SetConfiguration(string key, object value)\n    {\n        _configuration[key] = value;\n    }\n\n    /// <summary>\n    /// Gets configuration value.\n    /// </summary>\n    protected virtual T? GetConfiguration<T>(string key)\n    {\n        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)\n        {\n            return typedValue;\n        }\n        return default;\n    }\n\n    #endregion\n}\n\n/// <summary>\n/// Represents a web app notification template with title, content, type, action URL, and icon.\n/// </summary>\npublic record WebAppTemplate(string Title, string Content, string? Type = null, string? ActionUrl = null, string? IconUrl = null);\n"}]}