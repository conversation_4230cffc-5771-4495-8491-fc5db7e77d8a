{"sourceFile": "src/Core/PluginCore/Base/Recipient.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751199032364, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751199032364, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic class Recipient\r\n{\r\n    public string? Email { get; set; }\r\n    public string? PhoneNumber { get; set; }\r\n    public string? DeviceToken { get; set; }\r\n    public string? CustomIdentifier { get; set; }\r\n    public string? Metadata { get; set; }\r\n    public string? Tags { get; set; }\r\n    public string? Groups { get; set; }\r\n    public string? Segments { get; set; }\r\n    public string? CustomData { get; set; }\r\n    public string? Preferences { get; set; }\r\n    public string? Language { get; set; }\r\n    public string? Timezone { get; set; }\r\n    public string? Country { get; set; }\r\n    public string? Region { get; set; }\r\n    public string? City { get; set; }\r\n    public string? PostalCode { get; set; }\r\n    public string? Address { get; set; }\r\n    public string? Latitude { get; set; }\r\n\r\n    public string? Longitude { get; set; }\r\n\r\n\r\n}\r\n"}]}