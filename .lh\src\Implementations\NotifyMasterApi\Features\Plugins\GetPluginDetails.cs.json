{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Plugins/GetPluginDetails.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1751212929467, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751212956508, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,30 +9,8 @@\n {\r\n     public string PluginName { get; set; } = string.Empty;\r\n }\r\n \r\n-public class GetPluginDetailsResponse\r\n-{\r\n-    public PluginDetailInfo? Plugin { get; set; }\r\n-    public PluginAdminInfo? AdminInfo { get; set; }\r\n-    public PluginMetrics? Metrics { get; set; }\r\n-    public object? HealthStatus { get; set; }\r\n-    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n-}\r\n-\r\n-public class PluginDetailInfo\r\n-{\r\n-    public string Name { get; set; } = string.Empty;\r\n-    public string Version { get; set; } = string.Empty;\r\n-    public string Description { get; set; } = string.Empty;\r\n-    public string Type { get; set; } = string.Empty;\r\n-    public string Provider { get; set; } = string.Empty;\r\n-    public string Author { get; set; } = string.Empty;\r\n-    public bool IsEnabled { get; set; }\r\n-    public DateTime? LoadedAt { get; set; }\r\n-    public List<string> SupportedFeatures { get; set; } = new();\r\n-}\r\n-\r\n public class GetPluginDetailsEndpoint : Endpoint<GetPluginDetailsRequest, GetPluginDetailsResponse>\r\n {\r\n     private readonly IPluginManager _pluginManager;\r\n     private readonly ILogger<GetPluginDetailsEndpoint> _logger;\r\n"}, {"date": 1751212973470, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,17 +9,15 @@\n {\r\n     public string PluginName { get; set; } = string.Empty;\r\n }\r\n \r\n-public class GetPluginDetailsEndpoint : Endpoint<GetPluginDetailsRequest, GetPluginDetailsResponse>\r\n+public class GetPluginDetailsEndpoint : Endpoint<GetPluginDetailsRequest, Plugin?>\r\n {\r\n-    private readonly IPluginManager _pluginManager;\r\n-    private readonly ILogger<GetPluginDetailsEndpoint> _logger;\r\n+    private readonly IMediator _mediator;\r\n \r\n-    public GetPluginDetailsEndpoint(IPluginManager pluginManager, ILogger<GetPluginDetailsEndpoint> logger)\r\n+    public GetPluginDetailsEndpoint(IMediator mediator)\r\n     {\r\n-        _pluginManager = pluginManager;\r\n-        _logger = logger;\r\n+        _mediator = mediator;\r\n     }\r\n \r\n     public override void Configure()\r\n     {\r\n"}, {"date": 1751212997139, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,15 +24,13 @@\n         Get(\"/api/plugins/{pluginName}\");\r\n         AllowAnonymous();\r\n         Summary(s =>\r\n         {\r\n-            s.Summary = \"Get detailed plugin information\";\r\n-            s.Description = \"Retrieve detailed information about a specific plugin including admin info and metrics\";\r\n-            s.Responses[200] = \"Plugin details retrieved successfully\";\r\n-            s.Responses[404] = \"Plugin not found\";\r\n-            s.Responses[500] = \"Internal server error\";\r\n+            s.Summary = \"Get Plugin Details\";\r\n+            s.Description = \"Get detailed information about a specific plugin\";\r\n+            s.Response<Plugin>(200, \"Plugin details retrieved successfully\");\r\n+            s.Response(404, \"Plugin not found\");\r\n         });\r\n-        Tags(\"Plugin Management\");\r\n     }\r\n \r\n     public override async Task HandleAsync(GetPluginDetailsRequest req, CancellationToken ct)\r\n     {\r\n"}, {"date": 1751213066374, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,48 +33,18 @@\n     }\r\n \r\n     public override async Task HandleAsync(GetPluginDetailsRequest req, CancellationToken ct)\r\n     {\r\n-        try\r\n-        {\r\n-            var plugin = await _pluginManager.GetPluginAsync(req.PluginName);\r\n-            if (plugin == null)\r\n-            {\r\n-                await SendNotFoundAsync(ct);\r\n-                return;\r\n-            }\r\n+        var query = new GetPluginQuery(req.PluginName);\r\n+        var result = await _mediator.Send(query, ct);\r\n \r\n-            // Get additional plugin information\r\n-            var adminInfo = await GetPluginAdminInfoSafely(req.PluginName);\r\n-            var metrics = await GetPluginMetricsSafely(req.PluginName);\r\n-            var healthStatus = await GetPluginHealthSafely(req.PluginName);\r\n-\r\n-            var pluginDetail = new PluginDetailInfo\r\n-            {\r\n-                Name = plugin.Name ?? \"Unknown\",\r\n-                Version = plugin.Version ?? \"Unknown\",\r\n-                Description = plugin.Description ?? \"No description available\",\r\n-                Type = plugin.Type.ToString(),\r\n-                Provider = plugin.Provider ?? \"Unknown\",\r\n-                Author = plugin.Author ?? \"Unknown\",\r\n-                IsEnabled = plugin.IsEnabled,\r\n-                LoadedAt = plugin.LoadedAt,\r\n-                SupportedFeatures = new List<string>() // This would come from manifest\r\n-            };\r\n-\r\n-            await SendOkAsync(new GetPluginDetailsResponse\r\n-            {\r\n-                Plugin = pluginDetail,\r\n-                AdminInfo = adminInfo,\r\n-                Metrics = metrics,\r\n-                HealthStatus = healthStatus\r\n-            }, ct);\r\n-        }\r\n-        catch (Exception ex)\r\n+        if (result == null)\r\n         {\r\n-            _logger.LogError(ex, \"Error retrieving plugin details for {PluginName}\", req.PluginName);\r\n-            await SendErrorsAsync(500, ct);\r\n+            await SendNotFoundAsync(ct);\r\n+            return;\r\n         }\r\n+\r\n+        await SendOkAsync(result, ct);\r\n     }\r\n \r\n     private async Task<PluginAdminInfo?> GetPluginAdminInfoSafely(string pluginName)\r\n     {\r\n"}, {"date": 1751213096850, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -45,63 +45,5 @@\n \r\n         await SendOkAsync(result, ct);\r\n     }\r\n \r\n-    private async Task<PluginAdminInfo?> GetPluginAdminInfoSafely(string pluginName)\r\n-    {\r\n-        try\r\n-        {\r\n-            var config = await _pluginManager.GetPluginConfigurationAsync(pluginName);\r\n-            // Convert to PluginAdminInfo - this is a simplified version\r\n-            return new PluginAdminInfo(\r\n-                pluginName,\r\n-                \"1.0.0\", // Would get from actual plugin\r\n-                \"Unknown\", // Would get from actual plugin\r\n-                true, // Would get from actual plugin\r\n-                new Dictionary<string, object>(),\r\n-                new List<ConfigurationField>(),\r\n-                DateTime.UtcNow,\r\n-                \"Active\"\r\n-            );\r\n-        }\r\n-        catch (Exception ex)\r\n-        {\r\n-            _logger.LogWarning(ex, \"Could not get admin info for plugin {PluginName}\", pluginName);\r\n-            return null;\r\n-        }\r\n-    }\r\n-\r\n-    private async Task<PluginMetrics?> GetPluginMetricsSafely(string pluginName)\r\n-    {\r\n-        try\r\n-        {\r\n-            var metrics = await _pluginManager.GetPluginMetricsAsync(pluginName);\r\n-            // This would need to be converted from the object returned by the plugin manager\r\n-            return new PluginMetrics(\r\n-                pluginName,\r\n-                DateTime.UtcNow.AddHours(-1),\r\n-                DateTime.UtcNow,\r\n-                0, 0, 0, 0, 0, 0,\r\n-                new Dictionary<string, long>(),\r\n-                new Dictionary<string, object>()\r\n-            );\r\n-        }\r\n-        catch (Exception ex)\r\n-        {\r\n-            _logger.LogWarning(ex, \"Could not get metrics for plugin {PluginName}\", pluginName);\r\n-            return null;\r\n-        }\r\n-    }\r\n-\r\n-    private async Task<object?> GetPluginHealthSafely(string pluginName)\r\n-    {\r\n-        try\r\n-        {\r\n-            return await _pluginManager.GetPluginHealthStatusAsync(pluginName);\r\n-        }\r\n-        catch (Exception ex)\r\n-        {\r\n-            _logger.LogWarning(ex, \"Could not get health status for plugin {PluginName}\", pluginName);\r\n-            return new { status = \"unknown\", error = ex.Message };\r\n-        }\r\n-    }\r\n }\r\n"}], "date": 1751212929467, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing MediatR;\r\nusing NotifyMasterApi.Features.Plugins.Queries;\r\nusing NotifyMasterApi.Models;\r\n\r\nnamespace NotifyMasterApi.Features.Plugins;\r\n\r\npublic class GetPluginDetailsRequest\r\n{\r\n    public string PluginName { get; set; } = string.Empty;\r\n}\r\n\r\npublic class GetPluginDetailsResponse\r\n{\r\n    public PluginDetailInfo? Plugin { get; set; }\r\n    public PluginAdminInfo? AdminInfo { get; set; }\r\n    public PluginMetrics? Metrics { get; set; }\r\n    public object? HealthStatus { get; set; }\r\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n}\r\n\r\npublic class PluginDetailInfo\r\n{\r\n    public string Name { get; set; } = string.Empty;\r\n    public string Version { get; set; } = string.Empty;\r\n    public string Description { get; set; } = string.Empty;\r\n    public string Type { get; set; } = string.Empty;\r\n    public string Provider { get; set; } = string.Empty;\r\n    public string Author { get; set; } = string.Empty;\r\n    public bool IsEnabled { get; set; }\r\n    public DateTime? LoadedAt { get; set; }\r\n    public List<string> SupportedFeatures { get; set; } = new();\r\n}\r\n\r\npublic class GetPluginDetailsEndpoint : Endpoint<GetPluginDetailsRequest, GetPluginDetailsResponse>\r\n{\r\n    private readonly IPluginManager _pluginManager;\r\n    private readonly ILogger<GetPluginDetailsEndpoint> _logger;\r\n\r\n    public GetPluginDetailsEndpoint(IPluginManager pluginManager, ILogger<GetPluginDetailsEndpoint> logger)\r\n    {\r\n        _pluginManager = pluginManager;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/plugins/{pluginName}\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get detailed plugin information\";\r\n            s.Description = \"Retrieve detailed information about a specific plugin including admin info and metrics\";\r\n            s.Responses[200] = \"Plugin details retrieved successfully\";\r\n            s.Responses[404] = \"Plugin not found\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Plugin Management\");\r\n    }\r\n\r\n    public override async Task HandleAsync(GetPluginDetailsRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var plugin = await _pluginManager.GetPluginAsync(req.PluginName);\r\n            if (plugin == null)\r\n            {\r\n                await SendNotFoundAsync(ct);\r\n                return;\r\n            }\r\n\r\n            // Get additional plugin information\r\n            var adminInfo = await GetPluginAdminInfoSafely(req.PluginName);\r\n            var metrics = await GetPluginMetricsSafely(req.PluginName);\r\n            var healthStatus = await GetPluginHealthSafely(req.PluginName);\r\n\r\n            var pluginDetail = new PluginDetailInfo\r\n            {\r\n                Name = plugin.Name ?? \"Unknown\",\r\n                Version = plugin.Version ?? \"Unknown\",\r\n                Description = plugin.Description ?? \"No description available\",\r\n                Type = plugin.Type.ToString(),\r\n                Provider = plugin.Provider ?? \"Unknown\",\r\n                Author = plugin.Author ?? \"Unknown\",\r\n                IsEnabled = plugin.IsEnabled,\r\n                LoadedAt = plugin.LoadedAt,\r\n                SupportedFeatures = new List<string>() // This would come from manifest\r\n            };\r\n\r\n            await SendOkAsync(new GetPluginDetailsResponse\r\n            {\r\n                Plugin = pluginDetail,\r\n                AdminInfo = adminInfo,\r\n                Metrics = metrics,\r\n                HealthStatus = healthStatus\r\n            }, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error retrieving plugin details for {PluginName}\", req.PluginName);\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n\r\n    private async Task<PluginAdminInfo?> GetPluginAdminInfoSafely(string pluginName)\r\n    {\r\n        try\r\n        {\r\n            var config = await _pluginManager.GetPluginConfigurationAsync(pluginName);\r\n            // Convert to PluginAdminInfo - this is a simplified version\r\n            return new PluginAdminInfo(\r\n                pluginName,\r\n                \"1.0.0\", // Would get from actual plugin\r\n                \"Unknown\", // Would get from actual plugin\r\n                true, // Would get from actual plugin\r\n                new Dictionary<string, object>(),\r\n                new List<ConfigurationField>(),\r\n                DateTime.UtcNow,\r\n                \"Active\"\r\n            );\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogWarning(ex, \"Could not get admin info for plugin {PluginName}\", pluginName);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private async Task<PluginMetrics?> GetPluginMetricsSafely(string pluginName)\r\n    {\r\n        try\r\n        {\r\n            var metrics = await _pluginManager.GetPluginMetricsAsync(pluginName);\r\n            // This would need to be converted from the object returned by the plugin manager\r\n            return new PluginMetrics(\r\n                pluginName,\r\n                DateTime.UtcNow.AddHours(-1),\r\n                DateTime.UtcNow,\r\n                0, 0, 0, 0, 0, 0,\r\n                new Dictionary<string, long>(),\r\n                new Dictionary<string, object>()\r\n            );\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogWarning(ex, \"Could not get metrics for plugin {PluginName}\", pluginName);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private async Task<object?> GetPluginHealthSafely(string pluginName)\r\n    {\r\n        try\r\n        {\r\n            return await _pluginManager.GetPluginHealthStatusAsync(pluginName);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogWarning(ex, \"Could not get health status for plugin {PluginName}\", pluginName);\r\n            return new { status = \"unknown\", error = ex.Message };\r\n        }\r\n    }\r\n}\r\n"}]}