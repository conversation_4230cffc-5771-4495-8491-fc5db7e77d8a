@page "/tenants"
@attribute [Authorize]
@inject ITenantService TenantService
@inject IBreadcrumbService BreadcrumbService
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Tenant Management - NotificationService</PageTitle>

<div class="page-header mb-4">
    <div class="d-flex justify-space-between align-center">
        <div>
            <MudText Typo="Typo.h4">Tenant Management</MudText>
            <MudText Typo="Typo.body2" Class="mud-text-secondary">
                Manage organizations and their settings
            </MudText>
        </div>
        <MudButton Variant="Variant.Filled" 
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.Add"
                   OnClick="CreateTenant">
            Create Tenant
        </MudButton>
    </div>
</div>

<!-- Filters and Search -->
<MudCard Class="mb-4" Elevation="2">
    <MudCardContent>
        <MudGrid>
            <MudItem xs="12" md="4">
                <MudTextField @bind-Value="_searchTerm"
                              Label="Search tenants"
                              Variant="Variant.Outlined"
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Search"
                              OnKeyUp="@((e) => FilterTenants())"
                              Clearable="true" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect @bind-Value="_statusFilter"
                           Label="Status"
                           Variant="Variant.Outlined"
                           OnSelectionChanged="FilterTenants">
                    <MudSelectItem Value="@("All")">All Status</MudSelectItem>
                    <MudSelectItem Value="@("Active")">Active</MudSelectItem>
                    <MudSelectItem Value="@("Inactive")">Inactive</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudDatePicker @bind-Date="_createdAfter"
                               Label="Created After"
                               Variant="Variant.Outlined"
                               OnDateChanged="FilterTenants" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Outlined"
                           FullWidth="true"
                           OnClick="ClearFilters">
                    Clear Filters
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudCardContent>
</MudCard>

<!-- Tenants Table -->
<MudCard Elevation="4">
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h6">Tenants (@_filteredTenants.Count)</MudText>
        </CardHeaderContent>
        <CardHeaderActions>
            <MudIconButton Icon="@Icons.Material.Filled.Refresh" 
                          OnClick="LoadTenants" 
                          Title="Refresh" />
            <MudMenu Icon="@Icons.Material.Filled.MoreVert">
                <MudMenuItem OnClick="ExportTenants">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.Download" Class="mr-2" />
                        Export
                    </div>
                </MudMenuItem>
                <MudMenuItem OnClick="BulkActions">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.SelectAll" Class="mr-2" />
                        Bulk Actions
                    </div>
                </MudMenuItem>
            </MudMenu>
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardContent Class="pa-0">
        <MudTable Items="@_filteredTenants" 
                  Hover="true" 
                  Striped="true"
                  Loading="@_isLoading"
                  LoadingProgressColor="Color.Primary">
            <HeaderContent>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<Tenant, object>(x => x.Name)">
                        Name
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>ID</MudTh>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<Tenant, object>(x => x.IsActive)">
                        Status
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>Users</MudTh>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<Tenant, object>(x => x.CreatedAt)">
                        Created
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>Actions</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="Name">
                    <div class="d-flex align-center">
                        @if (!string.IsNullOrEmpty(context.Whitelabel.LogoUrl))
                        {
                            <MudAvatar Size="Size.Small" Class="mr-2">
                                <MudImage Src="@context.Whitelabel.LogoUrl" />
                            </MudAvatar>
                        }
                        else
                        {
                            <MudAvatar Size="Size.Small" Color="Color.Primary" Class="mr-2">
                                @context.Name.Substring(0, 1).ToUpper()
                            </MudAvatar>
                        }
                        <div>
                            <MudText Typo="Typo.body2">@context.Name</MudText>
                            @if (!string.IsNullOrEmpty(context.Description))
                            {
                                <MudText Typo="Typo.caption" Class="mud-text-secondary">
                                    @context.Description
                                </MudText>
                            }
                        </div>
                    </div>
                </MudTd>
                <MudTd DataLabel="ID">
                    <MudText Typo="Typo.body2" Class="font-monospace">@context.Id</MudText>
                </MudTd>
                <MudTd DataLabel="Status">
                    <MudChip Size="Size.Small" 
                             Color="@(context.IsActive ? Color.Success : Color.Default)"
                             Variant="Variant.Filled">
                        @(context.IsActive ? "Active" : "Inactive")
                    </MudChip>
                </MudTd>
                <MudTd DataLabel="Users">
                    <MudText Typo="Typo.body2">@GetTenantUserCount(context.Id)</MudText>
                </MudTd>
                <MudTd DataLabel="Created">
                    <MudText Typo="Typo.body2">@context.CreatedAt.ToString("MMM dd, yyyy")</MudText>
                    <MudText Typo="Typo.caption" Class="mud-text-secondary">
                        @context.CreatedAt.ToString("HH:mm")
                    </MudText>
                </MudTd>
                <MudTd DataLabel="Actions">
                    <div class="d-flex gap-1">
                        <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                      Size="Size.Small"
                                      OnClick="@(() => ViewTenant(context))"
                                      Title="View Details" />
                        <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                      Size="Size.Small"
                                      OnClick="@(() => EditTenant(context))"
                                      Title="Edit Tenant" />
                        <MudIconButton Icon="@Icons.Material.Filled.Settings"
                                      Size="Size.Small"
                                      OnClick="@(() => ConfigureTenant(context))"
                                      Title="Configure" />
                        <MudMenu Icon="@Icons.Material.Filled.MoreVert" Size="Size.Small">
                            <MudMenuItem OnClick="@(() => SwitchToTenant(context))">
                                <div class="d-flex align-center">
                                    <MudIcon Icon="@Icons.Material.Filled.SwapHoriz" Class="mr-2" />
                                    Switch To
                                </div>
                            </MudMenuItem>
                            <MudMenuItem OnClick="@(() => ViewUsage(context))">
                                <div class="d-flex align-center">
                                    <MudIcon Icon="@Icons.Material.Filled.Analytics" Class="mr-2" />
                                    Usage Stats
                                </div>
                            </MudMenuItem>
                            <MudDivider />
                            <MudMenuItem OnClick="@(() => ToggleTenantStatus(context))">
                                <div class="d-flex align-center">
                                    <MudIcon Icon="@(context.IsActive ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                            Class="mr-2" />
                                    @(context.IsActive ? "Deactivate" : "Activate")
                                </div>
                            </MudMenuItem>
                            @if (!context.IsActive)
                            {
                                <MudMenuItem OnClick="@(() => DeleteTenant(context))" Class="text-error">
                                    <div class="d-flex align-center">
                                        <MudIcon Icon="@Icons.Material.Filled.Delete" Class="mr-2" />
                                        Delete
                                    </div>
                                </MudMenuItem>
                            }
                        </MudMenu>
                    </div>
                </MudTd>
            </RowTemplate>
            <NoRecordsContent>
                <div class="text-center pa-8">
                    <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Large" Class="mud-text-secondary mb-2" />
                    <MudText Typo="Typo.h6" Class="mud-text-secondary">No tenants found</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary mb-4">
                        @if (string.IsNullOrEmpty(_searchTerm))
                        {
                            <text>Get started by creating your first tenant</text>
                        }
                        else
                        {
                            <text>No tenants match your search criteria</text>
                        }
                    </MudText>
                    @if (string.IsNullOrEmpty(_searchTerm))
                    {
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Add"
                                   OnClick="CreateTenant">
                            Create First Tenant
                        </MudButton>
                    }
                </div>
            </NoRecordsContent>
        </MudTable>
    </MudCardContent>
</MudCard>

@code {
    private List<Tenant> _tenants = new();
    private List<Tenant> _filteredTenants = new();
    private bool _isLoading = true;
    private string _searchTerm = "";
    private string _statusFilter = "All";
    private DateTime? _createdAfter;

    protected override async Task OnInitializedAsync()
    {
        BreadcrumbService.SetBreadcrumbs(
            new BreadcrumbItem { Text = "Home", Href = "/dashboard", Icon = Icons.Material.Filled.Home },
            new BreadcrumbItem { Text = "Tenants", Href = "/tenants", Icon = Icons.Material.Filled.Business, IsActive = true }
        );

        await LoadTenants();
    }

    private async Task LoadTenants()
    {
        _isLoading = true;
        try
        {
            _tenants = await TenantService.GetTenantsAsync();
            FilterTenants();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Failed to load tenants: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private void FilterTenants()
    {
        _filteredTenants = _tenants.Where(t =>
            (string.IsNullOrEmpty(_searchTerm) || 
             t.Name.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) ||
             t.Id.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase)) &&
            (_statusFilter == "All" || 
             (_statusFilter == "Active" && t.IsActive) ||
             (_statusFilter == "Inactive" && !t.IsActive)) &&
            (!_createdAfter.HasValue || t.CreatedAt >= _createdAfter.Value)
        ).ToList();
    }

    private void ClearFilters()
    {
        _searchTerm = "";
        _statusFilter = "All";
        _createdAfter = null;
        FilterTenants();
    }

    private async Task CreateTenant()
    {
        Navigation.NavigateTo("/tenants/create");
    }

    private async Task ViewTenant(Tenant tenant)
    {
        Navigation.NavigateTo($"/tenants/{tenant.Id}");
    }

    private async Task EditTenant(Tenant tenant)
    {
        Navigation.NavigateTo($"/tenants/{tenant.Id}/edit");
    }

    private async Task ConfigureTenant(Tenant tenant)
    {
        Navigation.NavigateTo($"/tenants/{tenant.Id}/settings");
    }

    private async Task SwitchToTenant(Tenant tenant)
    {
        // Implementation would switch current tenant context
        Snackbar.Add($"Switched to tenant: {tenant.Name}", Severity.Success);
    }

    private async Task ViewUsage(Tenant tenant)
    {
        Navigation.NavigateTo($"/tenants/{tenant.Id}/usage");
    }

    private async Task ToggleTenantStatus(Tenant tenant)
    {
        var action = tenant.IsActive ? "deactivate" : "activate";
        var confirmed = await DialogService.ShowMessageBox(
            $"Confirm {action}",
            $"Are you sure you want to {action} tenant '{tenant.Name}'?",
            yesText: action.ToUpper(), cancelText: "Cancel");

        if (confirmed == true)
        {
            tenant.IsActive = !tenant.IsActive;
            // In real implementation, call API to update tenant
            Snackbar.Add($"Tenant {action}d successfully", Severity.Success);
            FilterTenants();
        }
    }

    private async Task DeleteTenant(Tenant tenant)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "Confirm Delete",
            $"Are you sure you want to permanently delete tenant '{tenant.Name}'? This action cannot be undone.",
            yesText: "DELETE", cancelText: "Cancel");

        if (confirmed == true)
        {
            try
            {
                await TenantService.DeleteTenantAsync(tenant.Id);
                _tenants.Remove(tenant);
                FilterTenants();
                Snackbar.Add("Tenant deleted successfully", Severity.Success);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Failed to delete tenant: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ExportTenants()
    {
        // Implementation for exporting tenants
        Snackbar.Add("Export functionality coming soon", Severity.Info);
    }

    private async Task BulkActions()
    {
        // Implementation for bulk actions
        Snackbar.Add("Bulk actions functionality coming soon", Severity.Info);
    }

    private int GetTenantUserCount(string tenantId)
    {
        // In real implementation, this would come from the API
        return new Random().Next(1, 50);
    }
}
