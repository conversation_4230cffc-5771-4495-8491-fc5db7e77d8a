{"sourceFile": "src/Libraries/EmailService.Library/Extensions/ServiceCollectionExtensions.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751193455067, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751193455067, "name": "Commit-0", "content": "using EmailService.Library.Configuration;\r\nusing EmailService.Library.Interfaces;\r\nusing EmailService.Library.Services;\r\nusing Microsoft.Extensions.DependencyInjection;\r\nusing Microsoft.Extensions.Configuration;\r\n\r\nnamespace EmailService.Library.Extensions;\r\n\r\npublic static class ServiceCollectionExtensions\r\n{\r\n    \r\n}\r\n"}]}