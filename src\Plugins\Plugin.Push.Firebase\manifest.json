{"name": "Firebase Push Plugin", "version": "1.0.0", "description": "Push notification plugin for Firebase Cloud Messaging (FCM)", "author": "NotificationService Team", "type": "<PERSON><PERSON>", "provider": "Firebase", "assemblyName": "Plugin.Push.Firebase.dll", "entryPoint": "Plugin.Push.Firebase.FirebasePlugin", "dependencies": [{"name": "FirebaseAdmin", "version": "3.0.1", "isRequired": true}], "configuration": {"serviceAccountJson": {"type": "string", "description": "Firebase Service Account JSON credentials", "isRequired": true, "isSecret": true}, "projectId": {"type": "string", "description": "Firebase Project ID", "isRequired": true, "isSecret": false}, "androidPriority": {"type": "string", "description": "Android notification priority (high/normal)", "defaultValue": "high", "isRequired": false, "isSecret": false}, "androidTtl": {"type": "int", "description": "Android notification TTL in seconds", "defaultValue": 3600, "isRequired": false, "isSecret": false}, "androidIcon": {"type": "string", "description": "Android notification icon", "isRequired": false, "isSecret": false}, "androidColor": {"type": "string", "description": "Android notification color", "isRequired": false, "isSecret": false}, "androidSound": {"type": "string", "description": "Android notification sound", "defaultValue": "default", "isRequired": false, "isSecret": false}, "androidChannelId": {"type": "string", "description": "Android notification channel ID", "defaultValue": "default", "isRequired": false, "isSecret": false}, "iosBadge": {"type": "int", "description": "iOS badge count", "isRequired": false, "isSecret": false}, "iosSound": {"type": "string", "description": "iOS notification sound", "defaultValue": "default", "isRequired": false, "isSecret": false}, "iosContentAvailable": {"type": "bool", "description": "iOS content available flag", "defaultValue": false, "isRequired": false, "isSecret": false}, "iosMutableContent": {"type": "bool", "description": "iOS mutable content flag", "defaultValue": false, "isRequired": false, "isSecret": false}, "webIcon": {"type": "string", "description": "Web notification icon URL", "isRequired": false, "isSecret": false}, "webBadge": {"type": "string", "description": "Web notification badge URL", "isRequired": false, "isSecret": false}}, "supportedFeatures": ["SendPush", "BulkPush", "AndroidNotifications", "iOSNotifications", "WebNotifications", "DataMessages", "TopicMessaging"], "minimumFrameworkVersion": "net9.0", "isEnabled": true, "priority": 100, "metadata": {"website": "https://firebase.google.com", "documentation": "https://firebase.google.com/docs/cloud-messaging"}}