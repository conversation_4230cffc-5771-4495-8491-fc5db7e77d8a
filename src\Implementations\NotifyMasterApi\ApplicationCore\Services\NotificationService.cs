using EmailContract.Models;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotifyMasterApi.Interfaces;
using PushNotificationContract.Models;
using SmsContract.Models;

namespace NotifyMasterApi.Services;

public sealed class NotificationService : INotificationService
{
    private readonly IQueueService _queueService;

    public NotificationService(IQueueService queueService)
    {
        _queueService = queueService;
    }
    public async Task SendAsync(SendNotificationRequest request)
    {
        var tasks = new List<Task>();

        if(request.NotificationTypes.Contains(NotificationType.Email))
        {
            var emailRequest = (SendEmailRequest)request;
            tasks.Add(_queueService.PublishAsync(QueueNames.EmailQueue, emailRequest));
        }

        if(request.NotificationTypes.Contains(NotificationType.Sms))
        {
            var smsRequest = (SendSmsRequest)request;
            tasks.Add(_queueService.PublishAsync(QueueNames.SmsQueue, smsRequest));
        }

        if(request.NotificationTypes.Contains(NotificationType.PushMessage))
        {
            var pushRequest = (SendPushMessageRequest)request;
            tasks.Add(_queueService.PublishAsync(QueueNames.PushQueue, pushRequest));
        }

        await Task.WhenAll(tasks);
    }
}