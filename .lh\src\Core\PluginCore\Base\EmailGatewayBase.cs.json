{"sourceFile": "src/Core/PluginCore/Base/EmailGatewayBase.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751209496962, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751209496962, "name": "Commit-0", "content": "using PluginCore.Interfaces;\nusing PluginCore.Models;\nusing System.ComponentModel.DataAnnotations;\nusing System.Text.RegularExpressions;\n\nnamespace PluginCore.Base;\n\n/// <summary>\n/// Abstract base class for Email gateway plugins.\n/// Provides common Email-specific functionality, validation, and helper methods.\n/// Implements all three gateway interfaces: Message, Admin, and Metrics.\n/// </summary>\npublic abstract class EmailGatewayBase : IEmailPlugin, IGatewayAdminPluginType, IGatewayMetricsPluginType\n{\n    protected readonly ILogger _logger;\n    protected readonly Dictionary<string, object> _configuration = new();\n\n    protected EmailGatewayBase(ILogger logger)\n    {\n        _logger = logger ?? throw new ArgumentNullException(nameof(logger));\n    }\n\n    #region IGatewayMessagePluginType Implementation\n\n    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var validationResult = await ValidateMessageAsync(payload, cancellationToken);\n            if (!validationResult.IsValid)\n            {\n                return new MessageSendResult(\n                    MessageId: Guid.NewGuid().ToString(),\n                    Timestamp: DateTimeOffset.UtcNow,\n                    Status: \"Failed\",\n                    CorrelationId: payload.CorrelationId\n                );\n            }\n\n            var normalizedPayload = NormalizeEmailPayload(payload);\n            return await SendEmailInternalAsync(normalizedPayload, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to send email message to {Recipient}\", payload.Recipient);\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\",\n                CorrelationId: payload.CorrelationId\n            );\n        }\n    }\n\n    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)\n    {\n        var results = new List<MessageSendResult>();\n        var validPayloads = new List<MessagePayload>();\n\n        // Validate all payloads first\n        foreach (var payload in payloads)\n        {\n            var validationResult = await ValidateMessageAsync(payload, cancellationToken);\n            if (validationResult.IsValid)\n            {\n                validPayloads.Add(NormalizeEmailPayload(payload));\n            }\n            else\n            {\n                results.Add(new MessageSendResult(\n                    MessageId: Guid.NewGuid().ToString(),\n                    Timestamp: DateTimeOffset.UtcNow,\n                    Status: \"Failed\",\n                    CorrelationId: payload.CorrelationId\n                ));\n            }\n        }\n\n        if (validPayloads.Any())\n        {\n            var bulkResults = await SendBulkEmailInternalAsync(validPayloads, cancellationToken);\n            results.AddRange(bulkResults);\n        }\n\n        return results.AsReadOnly();\n    }\n\n    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)\n    {\n        var template = await GetEmailTemplateAsync(templateId, cancellationToken);\n        if (template == null)\n        {\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\"\n            );\n        }\n\n        var content = ProcessEmailTemplate(template.Content, templateData);\n        var subject = ProcessEmailTemplate(template.Subject ?? \"No Subject\", templateData);\n        \n        var payload = new MessagePayload(\n            Recipient: recipient.Email ?? recipient.Address,\n            Content: content,\n            From: GetDefaultSender(),\n            Subject: subject,\n            Headers: new Dictionary<string, string> { [\"TemplateId\"] = templateId }\n        );\n\n        return await SendMessageAsync(payload, cancellationToken);\n    }\n\n    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);\n\n    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);\n\n    public virtual async Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        var errors = new List<string>();\n\n        // Validate email address\n        if (!IsValidEmailAddress(payload.Recipient))\n        {\n            errors.Add(\"Invalid email address format\");\n        }\n\n        // Validate message content\n        if (string.IsNullOrWhiteSpace(payload.Content))\n        {\n            errors.Add(\"Message content cannot be empty\");\n        }\n\n        // Validate subject (optional but recommended)\n        if (string.IsNullOrWhiteSpace(payload.Subject))\n        {\n            errors.Add(\"Email subject is recommended\");\n        }\n\n        // Provider-specific validation\n        var providerErrors = await ValidateEmailSpecificAsync(payload, cancellationToken);\n        errors.AddRange(providerErrors);\n\n        return new ValidationResult\n        {\n            IsValid = !errors.Any(),\n            ErrorMessage = errors.Any() ? string.Join(\"; \", errors) : null\n        };\n    }\n\n    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);\n\n    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);\n\n    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);\n\n    #endregion\n\n    #region IGatewayAdminPluginType Implementation\n\n    public virtual async Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default)\n    {\n        var configs = new List<GatewayConfiguration>();\n        \n        // Add Email-specific configuration items\n        configs.Add(new GatewayConfiguration(\"DefaultSender\", \"Default Email sender address\", GetConfiguration<string>(\"DefaultSender\") ?? \"\", \"Default email address for sending emails\", false, false));\n        configs.Add(new GatewayConfiguration(\"DefaultSenderName\", \"Default sender name\", GetConfiguration<string>(\"DefaultSenderName\") ?? \"\", \"Default display name for email sender\", false, false));\n        configs.Add(new GatewayConfiguration(\"EnableHtml\", \"Enable HTML emails\", GetConfiguration<bool>(\"EnableHtml\").ToString() ?? \"true\", \"Enable or disable HTML email support\", false, false, \"bool\"));\n        configs.Add(new GatewayConfiguration(\"EnableAttachments\", \"Enable email attachments\", GetConfiguration<bool>(\"EnableAttachments\").ToString() ?? \"true\", \"Enable or disable email attachment support\", false, false, \"bool\"));\n        \n        // Add provider-specific configurations\n        var providerConfigs = await GetEmailProviderConfigurationsAsync(cancellationToken);\n        configs.AddRange(providerConfigs);\n        \n        return configs.AsReadOnly();\n    }\n\n    public virtual async Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            foreach (var setting in settingsToUpdate)\n            {\n                switch (setting.Key)\n                {\n                    case \"DefaultSender\":\n                        if (setting.Value != null && IsValidEmailAddress(setting.Value))\n                            SetConfiguration(\"DefaultSender\", setting.Value);\n                        break;\n                    case \"DefaultSenderName\":\n                        if (setting.Value != null)\n                            SetConfiguration(\"DefaultSenderName\", setting.Value);\n                        break;\n                    case \"EnableHtml\":\n                        if (bool.TryParse(setting.Value, out var enableHtml))\n                            SetConfiguration(\"EnableHtml\", enableHtml);\n                        break;\n                    case \"EnableAttachments\":\n                        if (bool.TryParse(setting.Value, out var enableAttachments))\n                            SetConfiguration(\"EnableAttachments\", enableAttachments);\n                        break;\n                    default:\n                        await UpdateEmailProviderConfigurationAsync(setting, cancellationToken);\n                        break;\n                }\n            }\n\n            return new OperationResult(true, \"Configuration updated successfully\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to update Email gateway configuration\");\n            return new OperationResult(false, \"Failed to update configuration\", new[] { new ErrorDetail(\"ConfigurationError\", ex.Message) });\n        }\n    }\n\n    public virtual async Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Test basic Email functionality\n            var isAvailable = await IsAvailableAsync(cancellationToken);\n            if (!isAvailable)\n            {\n                return new OperationResult(false, \"Email gateway is not available\");\n            }\n\n            // Perform Email-specific configuration tests\n            var testResult = await TestEmailConfigurationAsync(cancellationToken);\n            return testResult;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Email configuration test failed\");\n            return new OperationResult(false, \"Configuration test failed\", new[] { new ErrorDetail(\"TestError\", ex.Message) });\n        }\n    }\n\n    public virtual async Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetEmailManifestAsync(cancellationToken);\n    }\n\n    #endregion\n\n    #region IGatewayMetricsPluginType Implementation\n\n    public virtual async Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetEmailStatusReportAsync(cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default)\n    {\n        return await GetEmailDeliveryReportsAsync(maxItems, cancellationToken);\n    }\n\n    public virtual async Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetEmailUsageMetricsAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetEmailErrorLogAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default)\n    {\n        return await GetEmailPerformanceSnapshotAsync(resolution, cancellationToken);\n    }\n\n    public virtual async Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetEmailSlaReportAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetEmailLatencyMetricsAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = \"daily\", CancellationToken cancellationToken = default)\n    {\n        return await GetEmailTrafficTrendsAsync(granularity, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetEmailAnomalyReportAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default)\n    {\n        return await GenerateEmailMetricsReportAsync(options, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetEmailRetryHistoryAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetEmailChangeImpactHistoryAsync(cancellationToken);\n    }\n\n    #endregion\n\n    #region Email-Specific Abstract Methods\n\n    /// <summary>\n    /// Sends a single email message using the provider's API.\n    /// </summary>\n    protected abstract Task<MessageSendResult> SendEmailInternalAsync(MessagePayload payload, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Sends multiple email messages in a single operation.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkEmailInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets an email template by ID.\n    /// </summary>\n    protected abstract Task<EmailTemplate?> GetEmailTemplateAsync(string templateId, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Performs provider-specific email validation.\n    /// </summary>\n    protected abstract Task<IEnumerable<string>> ValidateEmailSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);\n\n    #endregion\n\n    #region Email-Specific Abstract Methods for Admin\n\n    /// <summary>\n    /// Gets email provider-specific configuration items.\n    /// </summary>\n    protected abstract Task<IEnumerable<GatewayConfiguration>> GetEmailProviderConfigurationsAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Updates email provider-specific configuration.\n    /// </summary>\n    protected abstract Task UpdateEmailProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Tests email provider-specific configuration.\n    /// </summary>\n    protected abstract Task<OperationResult> TestEmailConfigurationAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets the email plugin manifest.\n    /// </summary>\n    protected abstract Task<PluginManifest> GetEmailManifestAsync(CancellationToken cancellationToken);\n\n    #endregion\n\n    #region Email-Specific Abstract Methods for Metrics\n\n    /// <summary>\n    /// Gets email-specific status report.\n    /// </summary>\n    protected abstract Task<GatewayStatusReport> GetEmailStatusReportAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email delivery reports.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<DeliveryResult>> GetEmailDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email usage metrics.\n    /// </summary>\n    protected abstract Task<UsageMetrics> GetEmailUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email error logs.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<GatewayErrorEntry>> GetEmailErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email performance snapshot.\n    /// </summary>\n    protected abstract Task<PerformanceSnapshot> GetEmailPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email SLA report.\n    /// </summary>\n    protected abstract Task<SlaReport> GetEmailSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email latency metrics.\n    /// </summary>\n    protected abstract Task<LatencyMetrics> GetEmailLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email traffic trends.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<TrafficTrend>> GetEmailTrafficTrendsAsync(string granularity, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email anomaly report.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<AnomalyDetectionResult>> GetEmailAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Generates email metrics report.\n    /// </summary>\n    protected abstract Task<GeneratedReport> GenerateEmailMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email retry history.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<RetryAttemptInfo>> GetEmailRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets email configuration change impact history.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<ConfigurationImpactRecord>> GetEmailChangeImpactHistoryAsync(CancellationToken cancellationToken);\n\n    #endregion\n\n    #region Email-Specific Helper Methods\n\n    /// <summary>\n    /// Validates email address format.\n    /// </summary>\n    protected virtual bool IsValidEmailAddress(string email)\n    {\n        if (string.IsNullOrWhiteSpace(email))\n            return false;\n\n        try\n        {\n            // Use a more comprehensive email validation regex\n            var emailRegex = new Regex(@\"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$\", RegexOptions.IgnoreCase);\n            return emailRegex.IsMatch(email);\n        }\n        catch\n        {\n            return false;\n        }\n    }\n\n    /// <summary>\n    /// Normalizes email payload for consistent processing.\n    /// </summary>\n    protected virtual MessagePayload NormalizeEmailPayload(MessagePayload payload)\n    {\n        var normalizedRecipient = payload.Recipient.Trim().ToLowerInvariant();\n        var normalizedSubject = payload.Subject?.Trim() ?? \"No Subject\";\n\n        return payload with\n        {\n            Recipient = normalizedRecipient,\n            Subject = normalizedSubject\n        };\n    }\n\n    /// <summary>\n    /// Processes email template with data substitution.\n    /// </summary>\n    protected virtual string ProcessEmailTemplate(string template, IDictionary<string, string> templateData)\n    {\n        var result = template;\n        foreach (var kvp in templateData)\n        {\n            result = result.Replace($\"{{{kvp.Key}}}\", kvp.Value);\n        }\n        return result;\n    }\n\n    /// <summary>\n    /// Gets the default sender for email messages.\n    /// </summary>\n    protected virtual string? GetDefaultSender()\n    {\n        return _configuration.TryGetValue(\"DefaultSender\", out var sender) ? sender?.ToString() : null;\n    }\n\n    /// <summary>\n    /// Gets the default sender name for email messages.\n    /// </summary>\n    protected virtual string? GetDefaultSenderName()\n    {\n        return _configuration.TryGetValue(\"DefaultSenderName\", out var senderName) ? senderName?.ToString() : null;\n    }\n\n    /// <summary>\n    /// Checks if HTML emails are enabled.\n    /// </summary>\n    protected virtual bool IsHtmlEnabled()\n    {\n        return GetConfiguration<bool>(\"EnableHtml\");\n    }\n\n    /// <summary>\n    /// Checks if email attachments are enabled.\n    /// </summary>\n    protected virtual bool AreAttachmentsEnabled()\n    {\n        return GetConfiguration<bool>(\"EnableAttachments\");\n    }\n\n    /// <summary>\n    /// Sets configuration value.\n    /// </summary>\n    protected virtual void SetConfiguration(string key, object value)\n    {\n        _configuration[key] = value;\n    }\n\n    /// <summary>\n    /// Gets configuration value.\n    /// </summary>\n    protected virtual T? GetConfiguration<T>(string key)\n    {\n        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)\n        {\n            return typedValue;\n        }\n        return default;\n    }\n\n    #endregion\n}\n\n/// <summary>\n/// Represents an email template with subject and content.\n/// </summary>\npublic record EmailTemplate(string Subject, string Content);\n"}]}