{"sourceFile": "src/Services/NotifyMasterApi/Ioc/Configuration.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751192188932, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751192311388, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -14,12 +14,12 @@\n }\r\n \r\n public class AppSetting\r\n {\r\n-    public required Logging Logging { get; set; }\r\n-    public required string AllowedHosts { get; set; }\r\n-    public required SeriLog SeriLog { get; set; }\r\n-    public required ElasticSearch ElasticSearch { get; set; }\r\n+    public Logging Logging { get; set; } = new();\r\n+    public string AllowedHosts { get; set; } = \"*\";\r\n+    public SeriLog SeriLog { get; set; } = new();\r\n+    public ElasticSearch ElasticSearch { get; set; } = new();\r\n }\r\n public struct Logging\r\n {\r\n     public LogLevel LogLevel { get; set; }\r\n"}, {"date": 1751192325319, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,9 @@\n namespace NotifyMasterApi.Ioc;\r\n \r\n public struct Configuration\r\n {\r\n-    public static AppSetting AppSetting { get; private set; }\r\n+    public static AppSetting AppSetting { get; private set; } = new();\r\n     public static void SetUp(IConfiguration configuration)\r\n     {\r\n         AppSetting appsetting = new();\r\n         configuration.Bind(appsetting);\r\n"}], "date": 1751192188932, "name": "Commit-0", "content": "\r\n\r\nnamespace NotifyMasterApi.Ioc;\r\n\r\npublic struct Configuration\r\n{\r\n    public static AppSetting AppSetting { get; private set; }\r\n    public static void SetUp(IConfiguration configuration)\r\n    {\r\n        AppSetting appsetting = new();\r\n        configuration.Bind(appsetting);\r\n        Configuration.AppSetting = appsetting;\r\n    }\r\n}\r\n\r\npublic class AppSetting\r\n{\r\n    public required Logging Logging { get; set; }\r\n    public required string AllowedHosts { get; set; }\r\n    public required SeriLog SeriLog { get; set; }\r\n    public required ElasticSearch ElasticSearch { get; set; }\r\n}\r\npublic struct Logging\r\n{\r\n    public LogLevel LogLevel { get; set; }\r\n}\r\npublic struct LogLevel\r\n{\r\n    public string Default { get; set; }\r\n    public string Microsoft_AspNetCore { get; set; }\r\n}\r\npublic struct SeriLog\r\n{\r\n    public MinimumLevel MinimumLevel { get; set; }\r\n}\r\npublic struct MinimumLevel\r\n{\r\n    public string Default { get; set; }\r\n    public Override Override { get; set; }\r\n}\r\npublic struct Override\r\n{\r\n    public string Microsoft { get; set; }\r\n    public string System { get; set; }\r\n}\r\npublic struct ElasticSearch \r\n{\r\n    public string Uri { get; set; }\r\n}\r\n\r\n"}]}