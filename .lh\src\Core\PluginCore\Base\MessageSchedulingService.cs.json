{"sourceFile": "src/Core/PluginCore/Base/MessageSchedulingService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751214102031, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751214136529, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -194,9 +194,9 @@\n                 .OrderBy(m => m.ScheduledTime)\n                 .ToList()\n                 .AsReadOnly();\n \n-            return Task.FromResult(pendingMessages);\n+            return Task.FromResult<IReadOnlyList<ScheduledMessage>>(pendingMessages);\n         }\n     }\n \n     public Task<OperationResult> MarkMessageAsSentAsync(\n"}], "date": 1751214102031, "name": "Commit-0", "content": "using PluginCore.Models;\nusing Microsoft.Extensions.Logging;\n\nnamespace PluginCore.Base;\n\n/// <summary>\n/// Service interface for scheduling messages when gateways don't have native scheduling support.\n/// </summary>\npublic interface IMessageSchedulingService\n{\n    /// <summary>\n    /// Schedules a message for future delivery.\n    /// </summary>\n    Task<MessageScheduleResult> ScheduleMessageAsync(\n        string gatewayId,\n        MessagePayload payload,\n        DateTimeOffset scheduledTime,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Cancels a scheduled message.\n    /// </summary>\n    Task<OperationResult> CancelScheduledMessageAsync(\n        string scheduledMessageId,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Gets all scheduled messages for a gateway.\n    /// </summary>\n    Task<IReadOnlyList<ScheduledMessage>> GetScheduledMessagesAsync(\n        string gatewayId,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Gets scheduled messages that are ready to be sent.\n    /// </summary>\n    Task<IReadOnlyList<ScheduledMessage>> GetPendingMessagesAsync(\n        DateTimeOffset cutoffTime,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Marks a scheduled message as sent.\n    /// </summary>\n    Task<OperationResult> MarkMessageAsSentAsync(\n        string scheduledMessageId,\n        MessageSendResult sendResult,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Marks a scheduled message as failed.\n    /// </summary>\n    Task<OperationResult> MarkMessageAsFailedAsync(\n        string scheduledMessageId,\n        string errorMessage,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Updates the scheduled time for a message.\n    /// </summary>\n    Task<OperationResult> RescheduleMessageAsync(\n        string scheduledMessageId,\n        DateTimeOffset newScheduledTime,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Gets the status of a scheduled message.\n    /// </summary>\n    Task<ScheduledMessageStatus?> GetScheduledMessageStatusAsync(\n        string scheduledMessageId,\n        CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Represents a scheduled message.\n/// </summary>\npublic record ScheduledMessage(\n    string Id,\n    string GatewayId,\n    MessagePayload Payload,\n    DateTimeOffset ScheduledTime,\n    DateTimeOffset CreatedAt,\n    ScheduledMessageStatus Status,\n    string? ErrorMessage = null,\n    MessageSendResult? SendResult = null,\n    int RetryCount = 0);\n\n/// <summary>\n/// Status of a scheduled message.\n/// </summary>\npublic enum ScheduledMessageStatus\n{\n    Pending,\n    Sent,\n    Failed,\n    Cancelled,\n    Expired\n}\n\n/// <summary>\n/// Default implementation of the message scheduling service.\n/// Uses in-memory storage by default, but can be extended for persistent storage.\n/// </summary>\npublic class DefaultMessageSchedulingService : IMessageSchedulingService\n{\n    private readonly Dictionary<string, ScheduledMessage> _scheduledMessages = new();\n    private readonly object _lock = new();\n    private readonly ILogger? _logger;\n\n    public DefaultMessageSchedulingService(ILogger? logger = null)\n    {\n        _logger = logger;\n    }\n\n    public Task<MessageScheduleResult> ScheduleMessageAsync(\n        string gatewayId,\n        MessagePayload payload,\n        DateTimeOffset scheduledTime,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            var scheduledMessageId = $\"sched_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid():N}\";\n            \n            var scheduledMessage = new ScheduledMessage(\n                Id: scheduledMessageId,\n                GatewayId: gatewayId,\n                Payload: payload,\n                ScheduledTime: scheduledTime,\n                CreatedAt: DateTimeOffset.UtcNow,\n                Status: ScheduledMessageStatus.Pending\n            );\n\n            _scheduledMessages[scheduledMessageId] = scheduledMessage;\n            \n            _logger?.LogInformation(\"Message scheduled with ID {ScheduledMessageId} for {ScheduledTime}\", \n                scheduledMessageId, scheduledTime);\n\n            return Task.FromResult(new MessageScheduleResult(\n                ScheduledMessageId: scheduledMessageId,\n                ScheduledTime: scheduledTime,\n                Status: \"Scheduled\"\n            ));\n        }\n    }\n\n    public Task<OperationResult> CancelScheduledMessageAsync(\n        string scheduledMessageId,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            if (!_scheduledMessages.TryGetValue(scheduledMessageId, out var message))\n            {\n                return Task.FromResult(new OperationResult(false, \"Scheduled message not found\"));\n            }\n\n            if (message.Status != ScheduledMessageStatus.Pending)\n            {\n                return Task.FromResult(new OperationResult(false, $\"Cannot cancel message with status {message.Status}\"));\n            }\n\n            _scheduledMessages[scheduledMessageId] = message with { Status = ScheduledMessageStatus.Cancelled };\n            \n            _logger?.LogInformation(\"Scheduled message {ScheduledMessageId} cancelled\", scheduledMessageId);\n            \n            return Task.FromResult(new OperationResult(true, \"Message cancelled successfully\"));\n        }\n    }\n\n    public Task<IReadOnlyList<ScheduledMessage>> GetScheduledMessagesAsync(\n        string gatewayId,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            var messages = _scheduledMessages.Values\n                .Where(m => m.GatewayId == gatewayId)\n                .OrderBy(m => m.ScheduledTime)\n                .ToList()\n                .AsReadOnly();\n\n            return Task.FromResult<IReadOnlyList<ScheduledMessage>>(messages);\n        }\n    }\n\n    public Task<IReadOnlyList<ScheduledMessage>> GetPendingMessagesAsync(\n        DateTimeOffset cutoffTime,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            var pendingMessages = _scheduledMessages.Values\n                .Where(m => m.Status == ScheduledMessageStatus.Pending && m.ScheduledTime <= cutoffTime)\n                .OrderBy(m => m.ScheduledTime)\n                .ToList()\n                .AsReadOnly();\n\n            return Task.FromResult(pendingMessages);\n        }\n    }\n\n    public Task<OperationResult> MarkMessageAsSentAsync(\n        string scheduledMessageId,\n        MessageSendResult sendResult,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            if (!_scheduledMessages.TryGetValue(scheduledMessageId, out var message))\n            {\n                return Task.FromResult(new OperationResult(false, \"Scheduled message not found\"));\n            }\n\n            _scheduledMessages[scheduledMessageId] = message with \n            { \n                Status = ScheduledMessageStatus.Sent,\n                SendResult = sendResult\n            };\n\n            _logger?.LogInformation(\"Scheduled message {ScheduledMessageId} marked as sent\", scheduledMessageId);\n            \n            return Task.FromResult(new OperationResult(true, \"Message marked as sent\"));\n        }\n    }\n\n    public Task<OperationResult> MarkMessageAsFailedAsync(\n        string scheduledMessageId,\n        string errorMessage,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            if (!_scheduledMessages.TryGetValue(scheduledMessageId, out var message))\n            {\n                return Task.FromResult(new OperationResult(false, \"Scheduled message not found\"));\n            }\n\n            _scheduledMessages[scheduledMessageId] = message with \n            { \n                Status = ScheduledMessageStatus.Failed,\n                ErrorMessage = errorMessage,\n                RetryCount = message.RetryCount + 1\n            };\n\n            _logger?.LogWarning(\"Scheduled message {ScheduledMessageId} marked as failed: {ErrorMessage}\", \n                scheduledMessageId, errorMessage);\n            \n            return Task.FromResult(new OperationResult(true, \"Message marked as failed\"));\n        }\n    }\n\n    public Task<OperationResult> RescheduleMessageAsync(\n        string scheduledMessageId,\n        DateTimeOffset newScheduledTime,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            if (!_scheduledMessages.TryGetValue(scheduledMessageId, out var message))\n            {\n                return Task.FromResult(new OperationResult(false, \"Scheduled message not found\"));\n            }\n\n            if (message.Status != ScheduledMessageStatus.Pending)\n            {\n                return Task.FromResult(new OperationResult(false, $\"Cannot reschedule message with status {message.Status}\"));\n            }\n\n            _scheduledMessages[scheduledMessageId] = message with { ScheduledTime = newScheduledTime };\n            \n            _logger?.LogInformation(\"Scheduled message {ScheduledMessageId} rescheduled to {NewScheduledTime}\", \n                scheduledMessageId, newScheduledTime);\n            \n            return Task.FromResult(new OperationResult(true, \"Message rescheduled successfully\"));\n        }\n    }\n\n    public Task<ScheduledMessageStatus?> GetScheduledMessageStatusAsync(\n        string scheduledMessageId,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            if (_scheduledMessages.TryGetValue(scheduledMessageId, out var message))\n            {\n                return Task.FromResult<ScheduledMessageStatus?>(message.Status);\n            }\n\n            return Task.FromResult<ScheduledMessageStatus?>(null);\n        }\n    }\n}\n"}]}