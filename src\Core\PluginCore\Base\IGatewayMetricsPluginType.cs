using PluginCore.Models;

namespace PluginCore.Base;

/// <summary>
/// Provides methods to retrieve analytics, operational metrics, and logs from the gateway.
/// </summary>
/// <remarks>
/// This interface provides methods to retrieve analytics, operational metrics, and logs from the gateway.
/// </remarks>
public interface IGatewayMetricsPluginType
{
    /// <summary>
    /// Gets the current overall status of the gateway, including health, connectivity, and latency.
    /// </summary>
    /// <Param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A GatewayStatusReport object representing the current status of the gateway.</returns>
    Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves a list of recent message delivery results.
    /// </summary>
    /// <param name="maxItems">The maximum number of items to retrieve.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A list of DeliveryResult objects representing recent message delivery results.</returns>
    Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets usage metrics such as messages sent, failed, and cost estimates between two dates.
    /// </summary>
    /// <param name="from">The start date for the metrics.</param>
    /// <param name="to">The end date for the metrics.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A UsageMetrics object representing the usage metrics between the specified dates.</returns>
    Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves detailed error logs.
    /// </summary>
    /// <param name="from">The start date for the logs.</param>
    /// <param name="to">The end date for the logs.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A list of GatewayErrorEntry objects representing the error logs between the specified dates.</returns>
    Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves throughput data to monitor system load.
    /// </summary>
    /// <param name="resolution">The time resolution of the snapshot (e.g., 1 minute, 5 minutes).</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A PerformanceSnapshot object representing the system's throughput at the specified resolution.</returns>
    Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default);

    /// <summary>
    /// Provides SLA (Service Level Agreement) statistics.
    /// </summary>
    /// <param name="from">The start date for the SLA report.</param>
    /// <param name="to">The end date for the SLA report.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A SlaReport object representing the SLA statistics between the specified dates.</returns>
    Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves latency breakdowns for various gateway operations.
    /// </summary>
    /// <param name="from">The start date for the latency report.</param>
    /// <param name="to">The end date for the latency report.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A LatencyMetrics object representing the latency breakdowns between the specified dates.</returns>
    Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets daily or hourly trend reports for traffic analysis.
    /// </summary>
    /// <param name="granularity">The time granularity of the trend report (e.g., daily, hourly).</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A list of TrafficTrend objects representing the traffic trends at the specified granularity.</returns>
    Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = "daily", CancellationToken cancellationToken = default);

    /// <summary>
    /// Lists recent anomalies or spikes in usage, errors, or latency.
    /// </summary>
    /// <param name="from">The start date for the anomaly report.</param>
    /// <param name="to">The end date for the anomaly report.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A list of AnomalyDetectionResult objects representing recent anomalies.</returns>
    Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates and returns a downloadable metrics report in a specific format.
    /// </summary>
    /// <param name="options">The options for generating the report.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A GeneratedReport object representing the generated report.</returns>
    Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves a list of gateway-side retry attempts.
    /// </summary>
    /// <param name="from">The start date for the retry history.</param>
    /// <param name="to">The end date for the retry history.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A list of RetryAttemptInfo objects representing the retry attempts between the specified dates.</returns>
    Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);

    /// <summary>
    /// Returns historical configuration change events and their impact.
    /// </summary>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A list of ConfigurationImpactRecord objects representing the configuration change history.</returns>
    Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);
}
