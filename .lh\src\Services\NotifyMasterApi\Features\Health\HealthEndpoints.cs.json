{"sourceFile": "src/Services/NotifyMasterApi/Features/Health/HealthEndpoints.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751190722690, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751190722690, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing NotifyMasterApi.Interfaces;\r\nusing NotifyMasterApi.Documentation;\r\nusing System.Diagnostics;\r\n\r\nnamespace NotifyMasterApi.Features.Health;\r\n\r\npublic class GetHealthEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly IPluginManager _pluginManager;\r\n    private readonly ILogger<GetHealthEndpoint> _logger;\r\n\r\n    public GetHealthEndpoint(IPluginManager pluginManager, ILogger<GetHealthEndpoint> logger)\r\n    {\r\n        _pluginManager = pluginManager;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        this.ConfigureHealthEndpoint(\r\n            \"GET\",\r\n            \"/api/health\",\r\n            \"Service Health Check\",\r\n            \"Get comprehensive health status of the NotificationService including all plugins and system components.\\n\\n\" +\r\n            \"## 🎯 Health Monitoring\\n\" +\r\n            \"- **Service Status**: Overall application health\\n\" +\r\n            \"- **Plugin Health**: Individual plugin status and connectivity\\n\" +\r\n            \"- **Database Health**: Database connection and performance\\n\" +\r\n            \"- **External Dependencies**: Third-party service connectivity\\n\" +\r\n            \"- **System Resources**: Memory, CPU, and disk usage\\n\\n\" +\r\n            \"## 📊 Health Indicators\\n\" +\r\n            \"- **Healthy**: All systems operational\\n\" +\r\n            \"- **Degraded**: Some non-critical issues detected\\n\" +\r\n            \"- **Unhealthy**: Critical issues affecting functionality\\n\\n\" +\r\n            \"## 🔍 Detailed Checks\\n\" +\r\n            \"- Plugin connectivity and configuration\\n\" +\r\n            \"- Database query performance\\n\" +\r\n            \"- External API response times\\n\" +\r\n            \"- Queue depths and processing rates\\n\" +\r\n            \"- Memory usage and garbage collection\\n\\n\" +\r\n            \"## 📈 Metrics Included\\n\" +\r\n            \"- Response times for critical operations\\n\" +\r\n            \"- Error rates and success percentages\\n\" +\r\n            \"- Resource utilization statistics\\n\" +\r\n            \"- Plugin-specific health metrics\\n\\n\" +\r\n            \"## 🚨 Alerting\\n\" +\r\n            \"This endpoint is used by monitoring systems for:\\n\" +\r\n            \"- Load balancer health checks\\n- Kubernetes liveness probes\\n- External monitoring services\\n- Automated alerting systems\",\r\n            new[] { \"Monitoring\", \"System Status\", \"Diagnostics\" }\r\n        );\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var plugins = await _pluginManager.GetLoadedPluginsAsync();\r\n            var enabledPlugins = plugins.Where(p => p.IsEnabled).ToList();\r\n\r\n            var health = new\r\n            {\r\n                status = \"healthy\",\r\n                timestamp = DateTime.UtcNow,\r\n                version = \"2.0.0\",\r\n                services = new\r\n                {\r\n                    email = enabledPlugins.Any(p => p.Type.ToString() == \"Email\") ? \"available\" : \"unavailable\",\r\n                    sms = enabledPlugins.Any(p => p.Type.ToString() == \"Sms\") ? \"available\" : \"unavailable\",\r\n                    push = enabledPlugins.Any(p => p.Type.ToString() == \"PushNotification\") ? \"available\" : \"unavailable\",\r\n                    messaging = enabledPlugins.Any(p => p.Type.ToString() == \"Messaging\") ? \"available\" : \"unavailable\"\r\n                },\r\n                plugins = new\r\n                {\r\n                    total = plugins.Count(),\r\n                    enabled = enabledPlugins.Count,\r\n                    disabled = plugins.Count() - enabledPlugins.Count\r\n                }\r\n            };\r\n\r\n            await SendOkAsync(health, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Health check failed\");\r\n            await SendAsync(new { status = \"unhealthy\", error = ex.Message, timestamp = DateTime.UtcNow }, 500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetDetailedHealthEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly IPluginManager _pluginManager;\r\n    private readonly ILogger<GetDetailedHealthEndpoint> _logger;\r\n\r\n    public GetDetailedHealthEndpoint(IPluginManager pluginManager, ILogger<GetDetailedHealthEndpoint> logger)\r\n    {\r\n        _pluginManager = pluginManager;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/health/detailed\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get detailed health information\";\r\n            s.Description = \"Get comprehensive health information for all components\";\r\n            s.Responses[200] = \"Detailed health retrieved successfully\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Health\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var plugins = await _pluginManager.GetLoadedPluginsAsync();\r\n            var pluginHealthChecks = new List<object>();\r\n\r\n            foreach (var plugin in plugins.Where(p => p.IsEnabled))\r\n            {\r\n                try\r\n                {\r\n                    var healthStatus = await _pluginManager.GetPluginHealthStatusAsync(plugin.Name ?? \"\");\r\n                    pluginHealthChecks.Add(new\r\n                    {\r\n                        PluginName = plugin.Name,\r\n                        Provider = plugin.Provider,\r\n                        Type = plugin.Type,\r\n                        Status = healthStatus,\r\n                        IsEnabled = plugin.IsEnabled\r\n                    });\r\n                }\r\n                catch (Exception ex)\r\n                {\r\n                    pluginHealthChecks.Add(new\r\n                    {\r\n                        PluginName = plugin.Name,\r\n                        Provider = plugin.Provider,\r\n                        Type = plugin.Type,\r\n                        Status = new { healthy = false, error = ex.Message },\r\n                        IsEnabled = plugin.IsEnabled\r\n                    });\r\n                }\r\n            }\r\n\r\n            var detailedHealth = new\r\n            {\r\n                Service = new\r\n                {\r\n                    Status = \"Running\",\r\n                    Version = \"2.0.0\",\r\n                    Timestamp = DateTime.UtcNow,\r\n                    Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime\r\n                },\r\n                Plugins = pluginHealthChecks,\r\n                Infrastructure = new\r\n                {\r\n                    Database = new { Status = \"InMemory\", Type = \"EntityFramework\" },\r\n                    Logging = new { Status = \"Active\", Provider = \"Serilog\" },\r\n                    ApiDocumentation = new { Status = \"Active\", Provider = \"Scalar\" }\r\n                },\r\n                Features = new\r\n                {\r\n                    PluginManagement = true,\r\n                    MetricsTracking = true,\r\n                    HealthMonitoring = true,\r\n                    FeatureDetection = true\r\n                }\r\n            };\r\n\r\n            await SendOkAsync(detailedHealth, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting detailed health information\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetPluginHealthRequest\r\n{\r\n    public string PluginName { get; set; } = string.Empty;\r\n}\r\n\r\npublic class GetPluginHealthEndpoint : Endpoint<GetPluginHealthRequest, object>\r\n{\r\n    private readonly IPluginManager _pluginManager;\r\n    private readonly ILogger<GetPluginHealthEndpoint> _logger;\r\n\r\n    public GetPluginHealthEndpoint(IPluginManager pluginManager, ILogger<GetPluginHealthEndpoint> logger)\r\n    {\r\n        _pluginManager = pluginManager;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/health/plugins/{pluginName}\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get plugin health\";\r\n            s.Description = \"Get health status for a specific plugin\";\r\n            s.Responses[200] = \"Plugin health retrieved successfully\";\r\n            s.Responses[404] = \"Plugin not found\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Health\");\r\n    }\r\n\r\n    public override async Task HandleAsync(GetPluginHealthRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var plugin = await _pluginManager.GetPluginAsync(req.PluginName);\r\n            if (plugin == null)\r\n            {\r\n                await SendNotFoundAsync(ct);\r\n                return;\r\n            }\r\n\r\n            var healthStatus = await _pluginManager.GetPluginHealthStatusAsync(req.PluginName);\r\n            \r\n            var response = new\r\n            {\r\n                PluginName = req.PluginName,\r\n                Provider = plugin.Provider,\r\n                Type = plugin.Type,\r\n                IsEnabled = plugin.IsEnabled,\r\n                HealthStatus = healthStatus,\r\n                Timestamp = DateTime.UtcNow\r\n            };\r\n\r\n            await SendOkAsync(response, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting health status for plugin {PluginName}\", req.PluginName);\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n"}]}