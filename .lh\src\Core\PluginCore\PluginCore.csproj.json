{"sourceFile": "src/Core/PluginCore/PluginCore.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751213141907, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751213177819, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,6 +10,6 @@\n     <PackageReference Include=\"Microsoft.Extensions.Configuration.Binder\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Microsoft.Extensions.Logging\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Microsoft.Extensions.Hosting.Abstractions\" Version=\"9.0.0\" />\n     <PackageRefere/>\n-    </ItemIrou[p]\n+    </ItemIrou\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751218678010, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,9 @@\n     <PackageReference Include=\"Microsoft.Extensions.DependencyInjection\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Configuration\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Microsoft.Extensions.Configuration.Binder\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Microsoft.Extensions.Logging\" Version=\"9.0.0\" />\n-    <PackageReference Include=\"Microsoft.Extensions.Hosting.Abstractions\" Version=\"9.0.0\" />\n+    <PackageReference Include=\"Microsoft.Extensions.Hosting.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.2.1\" />\n     <PackageReference Include=\"Microsoft.IdentityModel.Tokens\" Version=\"8.12.1\" />\n   </ItemGroup>\n </Project>\n\\ No newline at end of file\n"}], "date": 1751213141907, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\n  <PropertyGroup>\n    <TargetFramework>net9.0</TargetFramework>\n    <ImplicitUsings>enable</ImplicitUsings>\n    <Nullable>enable</Nullable>\n  </PropertyGroup>\n  <ItemGroup>\n    <PackageReference Include=\"Microsoft.Extensions.DependencyInjection\" Version=\"9.0.0\" />\n    <PackageReference Include=\"Microsoft.Extensions.Configuration\" Version=\"9.0.0\" />\n    <PackageReference Include=\"Microsoft.Extensions.Configuration.Binder\" Version=\"9.0.0\" />\n    <PackageReference Include=\"Microsoft.Extensions.Logging\" Version=\"9.0.0\" />\n    <PackageReference Include=\"Microsoft.Extensions.Hosting.Abstractions\" Version=\"9.0.0\" />\n    <PackageRefere\n</Project>"}]}