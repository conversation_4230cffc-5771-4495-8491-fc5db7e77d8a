{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "favicon.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007352941176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=\""}, {"Name": "ETag", "Value": "W/\"bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "147"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:27:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI="}]}, {"Route": "favicon.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc="}]}, {"Route": "favicon.njhrp14sje.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007352941176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=\""}, {"Name": "ETag", "Value": "W/\"bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "njhrp14sje"}, {"Name": "integrity", "Value": "sha256-bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.njhrp14sje.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "147"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:27:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "njhrp14sje"}, {"Name": "integrity", "Value": "sha256-bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.njhrp14sje.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "njhrp14sje"}, {"Name": "integrity", "Value": "sha256-cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "index.90y8c1ti9j.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000937207123"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1066"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=\""}, {"Name": "ETag", "Value": "W/\"1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90y8c1ti9j"}, {"Name": "integrity", "Value": "sha256-1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.90y8c1ti9j.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 09:48:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90y8c1ti9j"}, {"Name": "integrity", "Value": "sha256-1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.90y8c1ti9j.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1066"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90y8c1ti9j"}, {"Name": "integrity", "Value": "sha256-ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000937207123"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1066"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=\""}, {"Name": "ETag", "Value": "W/\"1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 09:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1066"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8="}]}, {"Route": "logo.png", "AssetFile": "logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "132"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:27:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ="}]}, {"Route": "logo.psyieakcwl.png", "AssetFile": "logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "132"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:27:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "psyieakcwl"}, {"Name": "integrity", "Value": "sha256-qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ="}, {"Name": "label", "Value": "logo.png"}]}, {"Route": "logo.svg", "AssetFile": "logo.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000801924619"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=\""}, {"Name": "ETag", "Value": "W/\"91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM="}]}, {"Route": "logo.svg", "AssetFile": "logo.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3545"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:28:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM="}]}, {"Route": "logo.svg.gz", "AssetFile": "logo.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc="}]}, {"Route": "logo.z17xhknjbj.svg", "AssetFile": "logo.svg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000801924619"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=\""}, {"Name": "ETag", "Value": "W/\"91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z17xhknjbj"}, {"Name": "integrity", "Value": "sha256-91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM="}, {"Name": "label", "Value": "logo.svg"}]}, {"Route": "logo.z17xhknjbj.svg", "AssetFile": "logo.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3545"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:28:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z17xhknjbj"}, {"Name": "integrity", "Value": "sha256-91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM="}, {"Name": "label", "Value": "logo.svg"}]}, {"Route": "logo.z17xhknjbj.svg.gz", "AssetFile": "logo.svg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z17xhknjbj"}, {"Name": "integrity", "Value": "sha256-XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc="}, {"Name": "label", "Value": "logo.svg.gz"}]}, {"Route": "scalar-custom.css", "AssetFile": "scalar-custom.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476190476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2099"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=\""}, {"Name": "ETag", "Value": "W/\"PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk="}]}, {"Route": "scalar-custom.css", "AssetFile": "scalar-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:29:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk="}]}, {"Route": "scalar-custom.css.gz", "AssetFile": "scalar-custom.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2099"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8="}]}, {"Route": "scalar-custom.gflvcc7cd0.css", "AssetFile": "scalar-custom.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476190476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2099"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=\""}, {"Name": "ETag", "Value": "W/\"PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gflvcc7cd0"}, {"Name": "integrity", "Value": "sha256-PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk="}, {"Name": "label", "Value": "scalar-custom.css"}]}, {"Route": "scalar-custom.gflvcc7cd0.css", "AssetFile": "scalar-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:29:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gflvcc7cd0"}, {"Name": "integrity", "Value": "sha256-PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk="}, {"Name": "label", "Value": "scalar-custom.css"}]}, {"Route": "scalar-custom.gflvcc7cd0.css.gz", "AssetFile": "scalar-custom.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2099"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gflvcc7cd0"}, {"Name": "integrity", "Value": "sha256-wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8="}, {"Name": "label", "Value": "scalar-custom.css.gz"}]}]}