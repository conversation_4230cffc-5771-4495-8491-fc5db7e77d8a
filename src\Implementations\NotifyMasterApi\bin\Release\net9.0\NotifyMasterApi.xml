<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NotifyMasterApi</name>
    </assembly>
    <members>
        <member name="T:NotifyMasterApi.Services.InMemoryDatabaseService">
            <summary>
            In-memory database service for fallback when main database is not available
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.DatabaseFallbackExtensions">
            <summary>
            Extension methods for database fallback configuration
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.DatabaseFallbackActivationService">
            <summary>
            Service to activate database fallback on startup
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.PluginAutoLoaderService">
            <summary>
            Background service that automatically loads plugins at startup
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.PluginLoadContext">
            <summary>
            Custom AssemblyLoadContext for loading plugins in isolation
            Allows for proper unloading of plugin assemblies
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RuntimePluginManager">
            <summary>
            Runtime plugin manager that loads/unloads plugins dynamically without concrete references
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.SendNotificationAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            Send notification through a specific plugin using runtime invocation
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.GetPluginsHealthStatusAsync">
            <summary>
            Get health status of all loaded plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.GetPluginsByTypeAsync(System.String)">
            <summary>
            Get plugins by type (SMS, Email, Push)
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.InvokePluginMethodAsync(System.String,System.String,System.Object[])">
            <summary>
            Invoke any method on a plugin using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.LoadAllPluginsAsync">
            <summary>
            Load all plugins from the plugins directory
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.PluginManifest">
            <summary>
            Plugin manifest structure for runtime loading
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RedisConnectionService">
            <summary>
            Redis connection service with fallback to in-memory storage
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RedisConnectionServiceExtensions">
            <summary>
            Extension methods for Redis connection service registration
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.IQueueService">
            <summary>
            Redis-based queue service for handling notification messages
            Replaces RabbitMQ functionality with Redis pub/sub and lists
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.PublishAsync``1(System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Publish a message to a Redis list (queue)
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.ConsumeAsync``1(System.String,System.Threading.CancellationToken)">
            <summary>
            Consume a single message from a Redis list (queue)
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.ConsumeBatchAsync``1(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Consume multiple messages from a Redis list (queue) in batch
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.GetQueueLengthAsync(System.String)">
            <summary>
            Get the current length of a queue
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.DeleteQueueAsync(System.String)">
            <summary>
            Delete a queue and all its messages
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.SubscribeAsync``1(System.String,System.Func{``0,System.Threading.Tasks.Task},System.Threading.CancellationToken)">
            <summary>
            Subscribe to a Redis pub/sub channel for real-time events
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.PublishEventAsync``1(System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Publish an event to a Redis pub/sub channel
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.QueueNames">
            <summary>
            Queue names for different notification types
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.ChannelNames">
            <summary>
            Channel names for real-time events
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.QueuedNotification">
            <summary>
            Message models for queue operations
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.QueuedNotification.#ctor(System.String,System.String,System.Object,System.DateTime,System.Int32,System.Nullable{System.DateTime})">
            <summary>
            Message models for queue operations
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RuntimeNotificationService">
            <summary>
            Service that routes notifications to runtime-loaded plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.SendSmsAsync(SmsContract.Models.SendSmsRequest,System.String)">
            <summary>
            Send SMS using runtime-loaded SMS plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.SendEmailAsync(EmailContract.Models.SendEmailRequest,System.String)">
            <summary>
            Send Email using runtime-loaded Email plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.SendPushAsync(PushNotificationContract.Models.SendPushMessageRequest,System.String)">
            <summary>
            Send Push notification using runtime-loaded Push plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.GetAvailableProvidersAsync(System.String)">
            <summary>
            Get available providers for a specific notification type
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.GetPluginHealthStatusAsync">
            <summary>
            Get plugin health status
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.InvokePluginMethodAsync(System.String,System.String,System.Object[])">
            <summary>
            Invoke a specific method on a plugin
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RuntimePluginInstance">
            <summary>
            Represents a runtime-loaded plugin instance with no concrete type references
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.InitializeAsync(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Initialize the plugin using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.ValidateConfigurationAsync(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Validate plugin configuration using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.SendNotificationAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            Send notification using reflection (works for any notification type)
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.HealthCheckAsync">
            <summary>
            Perform health check using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.GetPluginInfo">
            <summary>
            Get plugin information using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.InvokeMethodAsync(System.String,System.Object[])">
            <summary>
            Invoke any method on the plugin using reflection
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Documentation.EndpointDocumentationExtensions">
            <summary>
            Extension methods for enhanced FastEndpoints documentation
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Documentation.EndpointDocumentationExtensions.ConfigureMessagingEndpoint``2(FastEndpoints.Endpoint{``0,``1},System.String,System.String,System.String,System.String,System.String,System.String[],System.Boolean,System.String,System.String)">
            <summary>
            Configure comprehensive documentation for messaging endpoints
            Note: This is a placeholder for documentation configuration.
            Actual endpoint configuration should be done in the endpoint's Configure() method.
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Documentation.EndpointDocumentationExtensions.ConfigureAdminEndpoint``2(FastEndpoints.Endpoint{``0,``1},System.String,System.String,System.String,System.String,System.String[])">
            <summary>
            Configure documentation for admin endpoints
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Documentation.EndpointDocumentationExtensions.ConfigurePluginEndpoint``2(FastEndpoints.Endpoint{``0,``1},System.String,System.String,System.String,System.String,System.String[])">
            <summary>
            Configure documentation for plugin endpoints
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Documentation.EndpointDocumentationExtensions.ConfigureHealthEndpoint``2(FastEndpoints.Endpoint{``0,``1},System.String,System.String,System.String,System.String,System.String[])">
            <summary>
            Configure documentation for health and monitoring endpoints
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Documentation.EndpointDocumentationExtensions.ConfigureNotificationEndpoint``2(FastEndpoints.Endpoint{``0,``1},System.String,System.String,System.String,System.String,System.String,System.String[])">
            <summary>
            Configure documentation for messaging endpoints (Email, SMS, Push)
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Documentation.DocumentationCategories">
            <summary>
            Documentation categories for endpoint organization
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Documentation.DocumentationExamples">
            <summary>
            Common example requests and responses for documentation
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Features.Email.SendEmailRequest">
            <summary>
            Request model for sending an email message
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.To">
            <summary>
            Recipient email address (required)
            </summary>
            <example><EMAIL></example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.From">
            <summary>
            Sender email address (optional - uses default if not specified)
            </summary>
            <example><EMAIL></example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.Subject">
            <summary>
            Email subject line (required)
            </summary>
            <example>Welcome to NotificationService</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.Body">
            <summary>
            Email body content (deprecated - use HtmlBody or PlainTextBody)
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.HtmlBody">
            <summary>
            HTML formatted email body
            </summary>
            <example>&lt;h1&gt;Welcome!&lt;/h1&gt;&lt;p&gt;Thank you for joining us.&lt;/p&gt;</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.PlainTextBody">
            <summary>
            Plain text email body (fallback for HTML)
            </summary>
            <example>Welcome! Thank you for joining us.</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.Cc">
            <summary>
            Carbon copy recipients (comma-separated)
            </summary>
            <example><EMAIL>,<EMAIL></example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.Bcc">
            <summary>
            Blind carbon copy recipients (comma-separated)
            </summary>
            <example><EMAIL>,<EMAIL></example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.Headers">
            <summary>
            Custom email headers
            </summary>
            <example>{"X-Priority": "1", "X-Custom-Header": "value"}</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailRequest.Category">
            <summary>
            Email category for tracking and analytics
            </summary>
            <example>welcome, notification, marketing</example>
        </member>
        <member name="T:NotifyMasterApi.Features.Email.SendEmailResponse">
            <summary>
            Response model for email sending operation
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailResponse.Success">
            <summary>
            Indicates whether the email was sent successfully
            </summary>
            <example>true</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailResponse.MessageId">
            <summary>
            Unique identifier for the sent message (available when successful)
            </summary>
            <example>msg_abc123def456</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailResponse.Error">
            <summary>
            Error message if the operation failed
            </summary>
            <example>Invalid email address format</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailResponse.Timestamp">
            <summary>
            Timestamp when the operation was completed
            </summary>
            <example>2024-01-15T10:30:00Z</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailResponse.Provider">
            <summary>
            Email provider used for sending
            </summary>
            <example>SendGrid, Mailgun, SMTP</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Email.SendEmailResponse.Metadata">
            <summary>
            Additional metadata about the operation
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Features.Plugins.GetPluginsRequest">
            <summary>
            Request model for retrieving plugins with optional filtering
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.Plugins.GetPluginsRequest.Type">
            <summary>
            Filter plugins by type (optional)
            </summary>
            <example>Email, SMS, Push, Messaging</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Plugins.GetPluginsRequest.IsEnabled">
            <summary>
            Filter plugins by enabled status (optional)
            </summary>
            <example>true</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Plugins.GetPluginsRequest.Provider">
            <summary>
            Filter plugins by provider (optional)
            </summary>
            <example>SendGrid, Twilio, FCM</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Plugins.GetPluginsRequest.IncludeDetails">
            <summary>
            Include detailed plugin information
            </summary>
            <example>false</example>
        </member>
        <member name="T:NotifyMasterApi.Features.Push.SendPushRequest">
            <summary>
            Request model for sending a push notification
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.DeviceToken">
            <summary>
            Device token for the target device (required)
            </summary>
            <example>device_token_here_**********abcdef</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.Title">
            <summary>
            Notification title (required)
            </summary>
            <example>New Message</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.Message">
            <summary>
            Notification message body (required)
            </summary>
            <example>You have a new notification</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.ImageUrl">
            <summary>
            URL to an image to display in the notification (optional)
            </summary>
            <example>https://example.com/image.png</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.Data">
            <summary>
            Custom data payload for the notification (optional)
            </summary>
            <example>{"action": "open_chat", "chatId": "chat_123"}</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.Platform">
            <summary>
            Target platform (auto-detected if not specified)
            </summary>
            <example>ios, android, web</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.Metadata">
            <summary>
            Additional metadata for tracking and analytics
            </summary>
            <example>{"campaign": "promotion", "userId": "12345"}</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.Priority">
            <summary>
            Notification priority level
            </summary>
            <example>high, normal, low</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.Sound">
            <summary>
            Sound to play with the notification
            </summary>
            <example>default, custom_sound.wav</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.Badge">
            <summary>
            Badge count for iOS notifications
            </summary>
            <example>1</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushRequest.TimeToLive">
            <summary>
            Time to live for the notification (in seconds)
            </summary>
            <example>3600</example>
        </member>
        <member name="T:NotifyMasterApi.Features.Push.SendPushResponse">
            <summary>
            Response model for push notification sending operation
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushResponse.Success">
            <summary>
            Indicates whether the push notification was sent successfully
            </summary>
            <example>true</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushResponse.MessageId">
            <summary>
            Unique identifier for the sent notification (available when successful)
            </summary>
            <example>push_def456ghi789</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushResponse.Error">
            <summary>
            Error message if the operation failed
            </summary>
            <example>Invalid device token</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushResponse.Timestamp">
            <summary>
            Timestamp when the operation was completed
            </summary>
            <example>2024-01-15T10:30:00Z</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushResponse.Provider">
            <summary>
            Push notification provider used for sending
            </summary>
            <example>FCM, APNS, OneSignal</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushResponse.Platform">
            <summary>
            Platform that received the notification
            </summary>
            <example>ios, android, web</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushResponse.DeliveryStatus">
            <summary>
            Delivery status information
            </summary>
            <example>queued, sent, delivered, failed</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Push.SendPushResponse.ProviderResponse">
            <summary>
            Additional response data from the provider
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Features.SMS.SendSmsRequest">
            <summary>
            Request model for sending an SMS message
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsRequest.PhoneNumber">
            <summary>
            Recipient phone number in international format (required)
            </summary>
            <example>+**********</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsRequest.Message">
            <summary>
            SMS message content (required)
            </summary>
            <example>Your verification code is: 123456</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsRequest.From">
            <summary>
            Sender ID or phone number (optional - uses default if not specified)
            </summary>
            <example>YourService</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsRequest.Metadata">
            <summary>
            Additional metadata for tracking and analytics
            </summary>
            <example>{"campaign": "verification", "userId": "12345"}</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsRequest.MessageType">
            <summary>
            Message type for provider-specific handling
            </summary>
            <example>transactional, promotional, otp</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsRequest.PreferredProvider">
            <summary>
            Preferred SMS provider (optional - auto-selected if not specified)
            </summary>
            <example>Twilio, Kavenegar, Nexmo</example>
        </member>
        <member name="T:NotifyMasterApi.Features.SMS.SendSmsResponse">
            <summary>
            Response model for SMS sending operation
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.Success">
            <summary>
            Indicates whether the SMS was sent successfully
            </summary>
            <example>true</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.MessageId">
            <summary>
            Unique identifier for the sent message (available when successful)
            </summary>
            <example>sms_xyz789abc123</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.Error">
            <summary>
            Error message if the operation failed
            </summary>
            <example>Invalid phone number format</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.Timestamp">
            <summary>
            Timestamp when the operation was completed
            </summary>
            <example>2024-01-15T10:30:00Z</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.Provider">
            <summary>
            SMS provider used for sending
            </summary>
            <example>Twilio, Kavenegar, Nexmo</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.DeliveryStatus">
            <summary>
            Delivery status information
            </summary>
            <example>queued, sent, delivered, failed</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.Cost">
            <summary>
            Cost information for the SMS (if available)
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.Currency">
            <summary>
            Currency for the cost (if available)
            </summary>
            <example>USD, EUR, IRR</example>
        </member>
        <member name="P:NotifyMasterApi.Features.SMS.SendSmsResponse.SegmentCount">
            <summary>
            Number of SMS segments used
            </summary>
            <example>1</example>
        </member>
        <member name="T:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest">
            <summary>
            Request model for queuing a webhook delivery
            </summary>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.Url">
            <summary>
            Target webhook URL (required)
            </summary>
            <example>https://api.example.com/webhooks/notifications</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.Method">
            <summary>
            HTTP method for the webhook request
            </summary>
            <example>POST</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.Headers">
            <summary>
            Custom headers to include with the webhook request
            </summary>
            <example>{"Authorization": "Bearer token", "Content-Type": "application/json"}</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.Payload">
            <summary>
            Payload data to send with the webhook
            </summary>
            <example>{"event": "message.sent", "messageId": "msg_123", "status": "delivered"}</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.MaxRetries">
            <summary>
            Maximum number of retry attempts for failed deliveries
            </summary>
            <example>3</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.TenantId">
            <summary>
            Tenant ID for multi-tenant webhook isolation (optional)
            </summary>
            <example>tenant_123</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.EventType">
            <summary>
            Webhook event type for categorization
            </summary>
            <example>message.sent, message.failed, plugin.status</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.DelaySeconds">
            <summary>
            Delay before first delivery attempt (in seconds)
            </summary>
            <example>0</example>
        </member>
        <member name="P:NotifyMasterApi.Features.Webhooks.QueueWebhookRequest.TimeoutSeconds">
            <summary>
            Webhook timeout in seconds
            </summary>
            <example>30</example>
        </member>
    </members>
</doc>
