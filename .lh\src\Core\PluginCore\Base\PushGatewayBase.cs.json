{"sourceFile": "src/Core/PluginCore/Base/PushGatewayBase.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751209595347, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751209595347, "name": "Commit-0", "content": "using PluginCore.Interfaces;\nusing PluginCore.Models;\nusing System.ComponentModel.DataAnnotations;\nusing System.Text.Json;\n\nnamespace PluginCore.Base;\n\n/// <summary>\n/// Abstract base class for Push Message gateway plugins.\n/// Provides common Push notification-specific functionality, validation, and helper methods.\n/// Implements all three gateway interfaces: Message, Admin, and Metrics.\n/// </summary>\npublic abstract class PushGatewayBase : IPushPlugin, IGatewayAdminPluginType, IGatewayMetricsPluginType\n{\n    protected readonly ILogger _logger;\n    protected readonly Dictionary<string, object> _configuration = new();\n\n    protected PushGatewayBase(ILogger logger)\n    {\n        _logger = logger ?? throw new ArgumentNullException(nameof(logger));\n    }\n\n    #region IGatewayMessagePluginType Implementation\n\n    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var validationResult = await ValidateMessageAsync(payload, cancellationToken);\n            if (!validationResult.IsValid)\n            {\n                return new MessageSendResult(\n                    MessageId: Guid.NewGuid().ToString(),\n                    Timestamp: DateTimeOffset.UtcNow,\n                    Status: \"Failed\",\n                    CorrelationId: payload.CorrelationId\n                );\n            }\n\n            var normalizedPayload = NormalizePushPayload(payload);\n            return await SendPushInternalAsync(normalizedPayload, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to send push notification to {Recipient}\", payload.Recipient);\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\",\n                CorrelationId: payload.CorrelationId\n            );\n        }\n    }\n\n    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)\n    {\n        var results = new List<MessageSendResult>();\n        var validPayloads = new List<MessagePayload>();\n\n        // Validate all payloads first\n        foreach (var payload in payloads)\n        {\n            var validationResult = await ValidateMessageAsync(payload, cancellationToken);\n            if (validationResult.IsValid)\n            {\n                validPayloads.Add(NormalizePushPayload(payload));\n            }\n            else\n            {\n                results.Add(new MessageSendResult(\n                    MessageId: Guid.NewGuid().ToString(),\n                    Timestamp: DateTimeOffset.UtcNow,\n                    Status: \"Failed\",\n                    CorrelationId: payload.CorrelationId\n                ));\n            }\n        }\n\n        if (validPayloads.Any())\n        {\n            var bulkResults = await SendBulkPushInternalAsync(validPayloads, cancellationToken);\n            results.AddRange(bulkResults);\n        }\n\n        return results.AsReadOnly();\n    }\n\n    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)\n    {\n        var template = await GetPushTemplateAsync(templateId, cancellationToken);\n        if (template == null)\n        {\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\"\n            );\n        }\n\n        var title = ProcessPushTemplate(template.Title, templateData);\n        var content = ProcessPushTemplate(template.Body, templateData);\n        \n        var payload = new MessagePayload(\n            Recipient: recipient.DeviceToken ?? recipient.Address,\n            Content: content,\n            Subject: title,\n            Headers: new Dictionary<string, string> \n            { \n                [\"TemplateId\"] = templateId,\n                [\"Platform\"] = template.Platform ?? \"all\"\n            }\n        );\n\n        return await SendMessageAsync(payload, cancellationToken);\n    }\n\n    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);\n\n    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);\n\n    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);\n\n    public virtual async Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        var errors = new List<string>();\n\n        // Validate device token or topic\n        if (!IsValidDeviceToken(payload.Recipient) && !IsValidTopic(payload.Recipient))\n        {\n            errors.Add(\"Invalid device token or topic format\");\n        }\n\n        // Validate message content\n        if (string.IsNullOrWhiteSpace(payload.Content))\n        {\n            errors.Add(\"Push notification body cannot be empty\");\n        }\n        else if (payload.Content.Length > GetMaxBodyLength())\n        {\n            errors.Add($\"Push notification body exceeds maximum length of {GetMaxBodyLength()} characters\");\n        }\n\n        // Validate title\n        if (!string.IsNullOrWhiteSpace(payload.Subject) && payload.Subject.Length > GetMaxTitleLength())\n        {\n            errors.Add($\"Push notification title exceeds maximum length of {GetMaxTitleLength()} characters\");\n        }\n\n        // Provider-specific validation\n        var providerErrors = await ValidatePushSpecificAsync(payload, cancellationToken);\n        errors.AddRange(providerErrors);\n\n        return new ValidationResult\n        {\n            IsValid = !errors.Any(),\n            ErrorMessage = errors.Any() ? string.Join(\"; \", errors) : null\n        };\n    }\n\n    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);\n\n    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);\n\n    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);\n\n    #endregion\n\n    #region IGatewayAdminPluginType Implementation\n\n    public virtual async Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default)\n    {\n        var configs = new List<GatewayConfiguration>();\n        \n        // Add Push-specific configuration items\n        configs.Add(new GatewayConfiguration(\"SupportedPlatforms\", \"Supported push platforms\", GetConfiguration<string>(\"SupportedPlatforms\") ?? \"android,ios,web\", \"Comma-separated list of supported platforms\", false, false));\n        configs.Add(new GatewayConfiguration(\"MaxTitleLength\", \"Maximum title length\", GetConfiguration<int>(\"MaxTitleLength\").ToString() ?? \"50\", \"Maximum length for push notification title\", false, false, \"int\"));\n        configs.Add(new GatewayConfiguration(\"MaxBodyLength\", \"Maximum body length\", GetConfiguration<int>(\"MaxBodyLength\").ToString() ?? \"240\", \"Maximum length for push notification body\", false, false, \"int\"));\n        configs.Add(new GatewayConfiguration(\"EnableBadgeCount\", \"Enable badge count\", GetConfiguration<bool>(\"EnableBadgeCount\").ToString() ?? \"true\", \"Enable or disable badge count for notifications\", false, false, \"bool\"));\n        configs.Add(new GatewayConfiguration(\"EnableSound\", \"Enable notification sound\", GetConfiguration<bool>(\"EnableSound\").ToString() ?? \"true\", \"Enable or disable notification sound\", false, false, \"bool\"));\n        \n        // Add provider-specific configurations\n        var providerConfigs = await GetPushProviderConfigurationsAsync(cancellationToken);\n        configs.AddRange(providerConfigs);\n        \n        return configs.AsReadOnly();\n    }\n\n    public virtual async Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            foreach (var setting in settingsToUpdate)\n            {\n                switch (setting.Key)\n                {\n                    case \"SupportedPlatforms\":\n                        if (setting.Value != null)\n                            SetConfiguration(\"SupportedPlatforms\", setting.Value);\n                        break;\n                    case \"MaxTitleLength\":\n                        if (int.TryParse(setting.Value, out var maxTitleLength))\n                            SetConfiguration(\"MaxTitleLength\", maxTitleLength);\n                        break;\n                    case \"MaxBodyLength\":\n                        if (int.TryParse(setting.Value, out var maxBodyLength))\n                            SetConfiguration(\"MaxBodyLength\", maxBodyLength);\n                        break;\n                    case \"EnableBadgeCount\":\n                        if (bool.TryParse(setting.Value, out var enableBadge))\n                            SetConfiguration(\"EnableBadgeCount\", enableBadge);\n                        break;\n                    case \"EnableSound\":\n                        if (bool.TryParse(setting.Value, out var enableSound))\n                            SetConfiguration(\"EnableSound\", enableSound);\n                        break;\n                    default:\n                        await UpdatePushProviderConfigurationAsync(setting, cancellationToken);\n                        break;\n                }\n            }\n\n            return new OperationResult(true, \"Configuration updated successfully\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to update Push gateway configuration\");\n            return new OperationResult(false, \"Failed to update configuration\", new[] { new ErrorDetail(\"ConfigurationError\", ex.Message) });\n        }\n    }\n\n    public virtual async Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Test basic Push functionality\n            var isAvailable = await IsAvailableAsync(cancellationToken);\n            if (!isAvailable)\n            {\n                return new OperationResult(false, \"Push gateway is not available\");\n            }\n\n            // Perform Push-specific configuration tests\n            var testResult = await TestPushConfigurationAsync(cancellationToken);\n            return testResult;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Push configuration test failed\");\n            return new OperationResult(false, \"Configuration test failed\", new[] { new ErrorDetail(\"TestError\", ex.Message) });\n        }\n    }\n\n    public virtual async Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetPushManifestAsync(cancellationToken);\n    }\n\n    #endregion\n\n    #region IGatewayMetricsPluginType Implementation\n\n    public virtual async Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetPushStatusReportAsync(cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default)\n    {\n        return await GetPushDeliveryReportsAsync(maxItems, cancellationToken);\n    }\n\n    public virtual async Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetPushUsageMetricsAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetPushErrorLogAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default)\n    {\n        return await GetPushPerformanceSnapshotAsync(resolution, cancellationToken);\n    }\n\n    public virtual async Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetPushSlaReportAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetPushLatencyMetricsAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = \"daily\", CancellationToken cancellationToken = default)\n    {\n        return await GetPushTrafficTrendsAsync(granularity, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetPushAnomalyReportAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default)\n    {\n        return await GeneratePushMetricsReportAsync(options, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)\n    {\n        return await GetPushRetryHistoryAsync(from, to, cancellationToken);\n    }\n\n    public virtual async Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default)\n    {\n        return await GetPushChangeImpactHistoryAsync(cancellationToken);\n    }\n\n    #endregion\n\n    #region Push-Specific Abstract Methods\n\n    /// <summary>\n    /// Sends a single push notification using the provider's API.\n    /// </summary>\n    protected abstract Task<MessageSendResult> SendPushInternalAsync(MessagePayload payload, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Sends multiple push notifications in a single operation.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkPushInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets a push notification template by ID.\n    /// </summary>\n    protected abstract Task<PushTemplate?> GetPushTemplateAsync(string templateId, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Performs provider-specific push notification validation.\n    /// </summary>\n    protected abstract Task<IEnumerable<string>> ValidatePushSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);\n\n    #endregion\n\n    #region Push-Specific Abstract Methods for Admin\n\n    /// <summary>\n    /// Gets push provider-specific configuration items.\n    /// </summary>\n    protected abstract Task<IEnumerable<GatewayConfiguration>> GetPushProviderConfigurationsAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Updates push provider-specific configuration.\n    /// </summary>\n    protected abstract Task UpdatePushProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Tests push provider-specific configuration.\n    /// </summary>\n    protected abstract Task<OperationResult> TestPushConfigurationAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets the push plugin manifest.\n    /// </summary>\n    protected abstract Task<PluginManifest> GetPushManifestAsync(CancellationToken cancellationToken);\n\n    #endregion\n\n    #region Push-Specific Abstract Methods for Metrics\n\n    /// <summary>\n    /// Gets push-specific status report.\n    /// </summary>\n    protected abstract Task<GatewayStatusReport> GetPushStatusReportAsync(CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push delivery reports.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<DeliveryResult>> GetPushDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push usage metrics.\n    /// </summary>\n    protected abstract Task<UsageMetrics> GetPushUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push error logs.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<GatewayErrorEntry>> GetPushErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push performance snapshot.\n    /// </summary>\n    protected abstract Task<PerformanceSnapshot> GetPushPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push SLA report.\n    /// </summary>\n    protected abstract Task<SlaReport> GetPushSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push latency metrics.\n    /// </summary>\n    protected abstract Task<LatencyMetrics> GetPushLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push traffic trends.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<TrafficTrend>> GetPushTrafficTrendsAsync(string granularity, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push anomaly report.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<AnomalyDetectionResult>> GetPushAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Generates push metrics report.\n    /// </summary>\n    protected abstract Task<GeneratedReport> GeneratePushMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push retry history.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<RetryAttemptInfo>> GetPushRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);\n\n    /// <summary>\n    /// Gets push configuration change impact history.\n    /// </summary>\n    protected abstract Task<IReadOnlyList<ConfigurationImpactRecord>> GetPushChangeImpactHistoryAsync(CancellationToken cancellationToken);\n\n    #endregion\n\n    #region Push-Specific Helper Methods\n\n    /// <summary>\n    /// Validates device token format for push notifications.\n    /// </summary>\n    protected virtual bool IsValidDeviceToken(string deviceToken)\n    {\n        if (string.IsNullOrWhiteSpace(deviceToken))\n            return false;\n\n        // Basic validation: should be a reasonable length and contain valid characters\n        return deviceToken.Length >= 10 && deviceToken.Length <= 4096 &&\n               deviceToken.All(c => char.IsLetterOrDigit(c) || c == '-' || c == '_' || c == ':');\n    }\n\n    /// <summary>\n    /// Validates topic format for push notifications.\n    /// </summary>\n    protected virtual bool IsValidTopic(string topic)\n    {\n        if (string.IsNullOrWhiteSpace(topic))\n            return false;\n\n        // Topic should start with /topics/ for FCM or be a valid topic name\n        return topic.StartsWith(\"/topics/\") ||\n               (topic.Length >= 3 && topic.Length <= 900 &&\n                topic.All(c => char.IsLetterOrDigit(c) || c == '-' || c == '_' || c == '.'));\n    }\n\n    /// <summary>\n    /// Gets the maximum title length for push notifications (default 50 characters).\n    /// </summary>\n    protected virtual int GetMaxTitleLength() => GetConfiguration<int>(\"MaxTitleLength\") > 0 ? GetConfiguration<int>(\"MaxTitleLength\") : 50;\n\n    /// <summary>\n    /// Gets the maximum body length for push notifications (default 240 characters).\n    /// </summary>\n    protected virtual int GetMaxBodyLength() => GetConfiguration<int>(\"MaxBodyLength\") > 0 ? GetConfiguration<int>(\"MaxBodyLength\") : 240;\n\n    /// <summary>\n    /// Normalizes push payload for consistent processing.\n    /// </summary>\n    protected virtual MessagePayload NormalizePushPayload(MessagePayload payload)\n    {\n        var normalizedRecipient = payload.Recipient.Trim();\n        var normalizedTitle = payload.Subject?.Trim() ?? \"\";\n        var normalizedContent = payload.Content.Trim();\n\n        return payload with\n        {\n            Recipient = normalizedRecipient,\n            Subject = normalizedTitle,\n            Content = normalizedContent\n        };\n    }\n\n    /// <summary>\n    /// Processes push template with data substitution.\n    /// </summary>\n    protected virtual string ProcessPushTemplate(string template, IDictionary<string, string> templateData)\n    {\n        var result = template;\n        foreach (var kvp in templateData)\n        {\n            result = result.Replace($\"{{{kvp.Key}}}\", kvp.Value);\n        }\n        return result;\n    }\n\n    /// <summary>\n    /// Gets supported platforms for push notifications.\n    /// </summary>\n    protected virtual string[] GetSupportedPlatforms()\n    {\n        var platforms = GetConfiguration<string>(\"SupportedPlatforms\") ?? \"android,ios,web\";\n        return platforms.Split(',', StringSplitOptions.RemoveEmptyEntries)\n                       .Select(p => p.Trim().ToLowerInvariant())\n                       .ToArray();\n    }\n\n    /// <summary>\n    /// Checks if badge count is enabled.\n    /// </summary>\n    protected virtual bool IsBadgeCountEnabled()\n    {\n        return GetConfiguration<bool>(\"EnableBadgeCount\");\n    }\n\n    /// <summary>\n    /// Checks if notification sound is enabled.\n    /// </summary>\n    protected virtual bool IsSoundEnabled()\n    {\n        return GetConfiguration<bool>(\"EnableSound\");\n    }\n\n    /// <summary>\n    /// Sets configuration value.\n    /// </summary>\n    protected virtual void SetConfiguration(string key, object value)\n    {\n        _configuration[key] = value;\n    }\n\n    /// <summary>\n    /// Gets configuration value.\n    /// </summary>\n    protected virtual T? GetConfiguration<T>(string key)\n    {\n        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)\n        {\n            return typedValue;\n        }\n        return default;\n    }\n\n    #endregion\n}\n\n/// <summary>\n/// Represents a push notification template with title, body, and platform.\n/// </summary>\npublic record PushTemplate(string Title, string Body, string? Platform = null);\n"}]}