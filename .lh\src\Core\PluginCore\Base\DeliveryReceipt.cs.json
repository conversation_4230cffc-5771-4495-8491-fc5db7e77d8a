{"sourceFile": "src/Core/PluginCore/Base/DeliveryReceipt.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751198964660, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751198964660, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic class DeliveryReceipt\r\n{\r\n    public string? MessageId { get; set; }\r\n    public string? ProviderMessageId { get; set; }\r\n    public string? Recipient { get; set; }\r\n    public string? Status { get; set; }\r\n    public string? Reason { get; set; }\r\n    public string? ProviderResponse { get; set; }\r\n    public string? ProviderErrorCode { get; set; }\r\n    public string? ProviderErrorMessage { get; set; }\r\n    public string? ProviderErrorDetails { get; set; }\r\n    public string? ProviderErrorResolution { get; set; }\r\n    public string? ProviderErrorDocumentation { get; set; }\r\n    public string? ProviderErrorSupport { get; set; }\r\n    public string? ProviderErrorContact { get; set; }\r\n    public string? ProviderErrorPhone { get; set; }\r\n    public string? ProviderErrorEmail { get; set; }\r\n    public string? ProviderErrorUrl { get; set; }\r\n    public string? ProviderErrorOther { get; set; }\r\n\r\n    public static explicit operator DeliveryReceipt(NotificationResponse response)\r\n        => new DeliveryReceipt\r\n        {\r\n            MessageId = response.MessageId,\r\n            ProviderMessageId = response.ProviderMessageId,\r\n            Recipient = response.Recipient,\r\n            Status = response.Status,\r\n            Reason = response.Reason,\r\n            ProviderResponse = response.ProviderResponse,\r\n            ProviderErrorCode = response.ProviderErrorCode,\r\n            ProviderErrorMessage = response.ProviderErrorMessage,\r\n            ProviderErrorDetails = response.ProviderErrorDetails,\r\n            ProviderErrorResolution = response.ProviderErrorResolution,\r\n            ProviderErrorDocumentation = response.ProviderErrorDocumentation,\r\n            ProviderErrorSupport = response.ProviderErrorSupport,\r\n            ProviderErrorContact = response.ProviderErrorContact,\r\n            ProviderErrorPhone = response.ProviderErrorPhone,\r\n            ProviderErrorEmail = response.ProviderErrorEmail,\r\n            ProviderErrorUrl = response.ProviderErrorUrl,\r\n            ProviderErrorOther = response.ProviderErrorOther\r\n        };\r\n\r\n\r\n\r\n}\r\n"}]}