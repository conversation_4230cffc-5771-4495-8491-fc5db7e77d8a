using NotificationPortal.Models;

namespace NotificationPortal.Services;

public interface IBreadcrumbService
{
    List<BreadcrumbItem> CurrentBreadcrumbs { get; }
    void SetBreadcrumbs(List<BreadcrumbItem> breadcrumbs);
    void SetBreadcrumbs(params BreadcrumbItem[] breadcrumbs);
    void AddBreadcrumb(BreadcrumbItem breadcrumb);
    void Clear();
    event Action OnBreadcrumbChanged;
}

public class BreadcrumbService : IBreadcrumbService
{
    private List<BreadcrumbItem> _breadcrumbs = new();

    public List<BreadcrumbItem> CurrentBreadcrumbs => _breadcrumbs;

    public event Action? OnBreadcrumbChanged;

    public void SetBreadcrumbs(List<BreadcrumbItem> breadcrumbs)
    {
        _breadcrumbs = breadcrumbs ?? new List<BreadcrumbItem>();
        OnBreadcrumbChanged?.Invoke();
    }

    public void SetBreadcrumbs(params BreadcrumbItem[] breadcrumbs)
    {
        SetBreadcrumbs(breadcrumbs.ToList());
    }

    public void AddBreadcrumb(BreadcrumbItem breadcrumb)
    {
        // Mark all existing breadcrumbs as inactive
        foreach (var item in _breadcrumbs)
        {
            item.IsActive = false;
        }

        // Add new breadcrumb as active
        breadcrumb.IsActive = true;
        _breadcrumbs.Add(breadcrumb);
        OnBreadcrumbChanged?.Invoke();
    }

    public void Clear()
    {
        _breadcrumbs.Clear();
        OnBreadcrumbChanged?.Invoke();
    }
}

public interface INotificationService
{
    Task<List<SystemEvent>> GetNotificationsAsync(bool unreadOnly = false);
    Task<int> GetUnreadCountAsync();
    Task MarkAsReadAsync(string notificationId);
    Task MarkAllAsReadAsync();
    Task<bool> SendNotificationAsync(SystemEvent notification);
    event Action<SystemEvent> OnNotificationReceived;
    event Action OnNotificationCountChanged;
}

public class NotificationService : INotificationService
{
    private readonly IApiService _apiService;
    private readonly List<SystemEvent> _notifications = new();

    public event Action<SystemEvent>? OnNotificationReceived;
    public event Action? OnNotificationCountChanged;

    public NotificationService(IApiService apiService)
    {
        _apiService = apiService;
        
        // Simulate some notifications
        _notifications.AddRange(new[]
        {
            new SystemEvent
            {
                Id = "1",
                Type = "system",
                Title = "Plugin Update Available",
                Message = "Twilio plugin v1.1.0 is available for update",
                Severity = "Info",
                Timestamp = DateTime.UtcNow.AddMinutes(-30),
                IsRead = false
            },
            new SystemEvent
            {
                Id = "2",
                Type = "security",
                Title = "New Login Detected",
                Message = "New login from Chrome on Windows",
                Severity = "Warning",
                Timestamp = DateTime.UtcNow.AddHours(-2),
                IsRead = false
            },
            new SystemEvent
            {
                Id = "3",
                Type = "system",
                Title = "Backup Completed",
                Message = "Daily backup completed successfully",
                Severity = "Info",
                Timestamp = DateTime.UtcNow.AddHours(-6),
                IsRead = true
            }
        });
    }

    public async Task<List<SystemEvent>> GetNotificationsAsync(bool unreadOnly = false)
    {
        if (unreadOnly)
        {
            return _notifications.Where(n => !n.IsRead).OrderByDescending(n => n.Timestamp).ToList();
        }
        return _notifications.OrderByDescending(n => n.Timestamp).ToList();
    }

    public async Task<int> GetUnreadCountAsync()
    {
        return _notifications.Count(n => !n.IsRead);
    }

    public async Task MarkAsReadAsync(string notificationId)
    {
        var notification = _notifications.FirstOrDefault(n => n.Id == notificationId);
        if (notification != null)
        {
            notification.IsRead = true;
            OnNotificationCountChanged?.Invoke();
        }
    }

    public async Task MarkAllAsReadAsync()
    {
        foreach (var notification in _notifications)
        {
            notification.IsRead = true;
        }
        OnNotificationCountChanged?.Invoke();
    }

    public async Task<bool> SendNotificationAsync(SystemEvent notification)
    {
        notification.Id = Guid.NewGuid().ToString();
        notification.Timestamp = DateTime.UtcNow;
        _notifications.Insert(0, notification);
        
        OnNotificationReceived?.Invoke(notification);
        OnNotificationCountChanged?.Invoke();
        
        return true;
    }
}
