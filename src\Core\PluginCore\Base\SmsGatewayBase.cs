using PluginCore.Interfaces;
using PluginCore.Models;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace PluginCore.Base;

/// <summary>
/// Abstract base class for SMS gateway plugins.
/// Provides common SMS-specific functionality, validation, and helper methods.
/// </summary>
public abstract class SmsGatewayBase : ISmsPlugin
{
    protected readonly ILogger _logger;
    protected readonly Dictionary<string, object> _configuration = new();

    protected SmsGatewayBase(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region IGatewayMessagePluginType Implementation

    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                );
            }

            var normalizedPayload = NormalizeSmsPayload(payload);
            return await SendSmsInternalAsync(normalizedPayload, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS message to {Recipient}", payload.Recipient);
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed",
                CorrelationId: payload.CorrelationId
            );
        }
    }

    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<MessageSendResult>();
        var validPayloads = new List<MessagePayload>();

        // Validate all payloads first
        foreach (var payload in payloads)
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (validationResult.IsValid)
            {
                validPayloads.Add(NormalizeSmsPayload(payload));
            }
            else
            {
                results.Add(new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                ));
            }
        }

        if (validPayloads.Any())
        {
            var bulkResults = await SendBulkSmsInternalAsync(validPayloads, cancellationToken);
            results.AddRange(bulkResults);
        }

        return results.AsReadOnly();
    }

    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)
    {
        var template = await GetSmsTemplateAsync(templateId, cancellationToken);
        if (template == null)
        {
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed"
            );
        }

        var content = ProcessSmsTemplate(template, templateData);
        var payload = new MessagePayload(
            Recipient: recipient.PhoneNumber ?? recipient.Address,
            Content: content,
            From: GetDefaultSender(),
            Headers: new Dictionary<string, string> { ["TemplateId"] = templateId }
        );

        return await SendMessageAsync(payload, cancellationToken);
    }

    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);

    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);

    public virtual async Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        // Validate phone number
        if (!IsValidPhoneNumber(payload.Recipient))
        {
            errors.Add("Invalid phone number format");
        }

        // Validate message content
        if (string.IsNullOrWhiteSpace(payload.Content))
        {
            errors.Add("Message content cannot be empty");
        }
        else if (payload.Content.Length > GetMaxMessageLength())
        {
            errors.Add($"Message content exceeds maximum length of {GetMaxMessageLength()} characters");
        }

        // Provider-specific validation
        var providerErrors = await ValidateSmsSpecificAsync(payload, cancellationToken);
        errors.AddRange(providerErrors);

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            ErrorMessage = errors.Any() ? string.Join("; ", errors) : null
        };
    }

    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);

    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);

    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    #endregion

    #region SMS-Specific Abstract Methods

    /// <summary>
    /// Sends a single SMS message using the provider's API.
    /// </summary>
    protected abstract Task<MessageSendResult> SendSmsInternalAsync(MessagePayload payload, CancellationToken cancellationToken);

    /// <summary>
    /// Sends multiple SMS messages in a single operation.
    /// </summary>
    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkSmsInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);

    /// <summary>
    /// Gets an SMS template by ID.
    /// </summary>
    protected abstract Task<string?> GetSmsTemplateAsync(string templateId, CancellationToken cancellationToken);

    /// <summary>
    /// Performs provider-specific SMS validation.
    /// </summary>
    protected abstract Task<IEnumerable<string>> ValidateSmsSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);

    #endregion

    #region SMS-Specific Helper Methods

    /// <summary>
    /// Validates phone number format for SMS.
    /// </summary>
    protected virtual bool IsValidPhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Remove common formatting characters
        var cleaned = Regex.Replace(phoneNumber, @"[\s\-\(\)\+]", "");
        
        // Basic validation: should be 10-15 digits
        return Regex.IsMatch(cleaned, @"^\d{10,15}$");
    }

    /// <summary>
    /// Gets the maximum message length for SMS (default 160 characters).
    /// </summary>
    protected virtual int GetMaxMessageLength() => 160;

    /// <summary>
    /// Normalizes SMS payload for consistent processing.
    /// </summary>
    protected virtual MessagePayload NormalizeSmsPayload(MessagePayload payload)
    {
        var normalizedRecipient = NormalizePhoneNumber(payload.Recipient);
        return payload with { Recipient = normalizedRecipient };
    }

    /// <summary>
    /// Normalizes phone number format.
    /// </summary>
    protected virtual string NormalizePhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return phoneNumber;

        // Remove formatting and ensure it starts with +
        var cleaned = Regex.Replace(phoneNumber, @"[\s\-\(\)]", "");
        if (!cleaned.StartsWith("+"))
        {
            cleaned = "+" + cleaned;
        }
        return cleaned;
    }

    /// <summary>
    /// Processes SMS template with data substitution.
    /// </summary>
    protected virtual string ProcessSmsTemplate(string template, IDictionary<string, string> templateData)
    {
        var result = template;
        foreach (var kvp in templateData)
        {
            result = result.Replace($"{{{kvp.Key}}}", kvp.Value);
        }
        return result;
    }

    /// <summary>
    /// Gets the default sender for SMS messages.
    /// </summary>
    protected virtual string? GetDefaultSender()
    {
        return _configuration.TryGetValue("DefaultSender", out var sender) ? sender?.ToString() : null;
    }

    /// <summary>
    /// Sets configuration value.
    /// </summary>
    protected virtual void SetConfiguration(string key, object value)
    {
        _configuration[key] = value;
    }

    /// <summary>
    /// Gets configuration value.
    /// </summary>
    protected virtual T? GetConfiguration<T>(string key)
    {
        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    #endregion
}
