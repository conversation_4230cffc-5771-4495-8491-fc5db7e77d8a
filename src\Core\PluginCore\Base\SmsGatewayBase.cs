using PluginCore.Interfaces;
using PluginCore.Models;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace PluginCore.Base;

/// <summary>
/// Abstract base class for SMS gateway plugins.
/// Provides common SMS-specific functionality, validation, and helper methods.
/// Implements all three gateway interfaces: Message, Admin, and Metrics.
/// </summary>
public abstract class SmsGatewayBase : ISmsPlugin, IGatewayAdminPluginType, IGatewayMetricsPluginType
{
    protected readonly ILogger _logger;
    protected readonly Dictionary<string, object> _configuration = new();

    protected SmsGatewayBase(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region IGatewayMessagePluginType Implementation

    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                );
            }

            var normalizedPayload = NormalizeSmsPayload(payload);
            return await SendSmsInternalAsync(normalizedPayload, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS message to {Recipient}", payload.Recipient);
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed",
                CorrelationId: payload.CorrelationId
            );
        }
    }

    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<MessageSendResult>();
        var validPayloads = new List<MessagePayload>();

        // Validate all payloads first
        foreach (var payload in payloads)
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (validationResult.IsValid)
            {
                validPayloads.Add(NormalizeSmsPayload(payload));
            }
            else
            {
                results.Add(new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                ));
            }
        }

        if (validPayloads.Any())
        {
            var bulkResults = await SendBulkSmsInternalAsync(validPayloads, cancellationToken);
            results.AddRange(bulkResults);
        }

        return results.AsReadOnly();
    }

    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)
    {
        var template = await GetSmsTemplateAsync(templateId, cancellationToken);
        if (template == null)
        {
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed"
            );
        }

        var content = ProcessSmsTemplate(template, templateData);
        var payload = new MessagePayload(
            Recipient: recipient.PhoneNumber ?? recipient.Address,
            Content: content,
            From: GetDefaultSender(),
            Headers: new Dictionary<string, string> { ["TemplateId"] = templateId }
        );

        return await SendMessageAsync(payload, cancellationToken);
    }

    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);

    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);

    public virtual async Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        // Validate phone number
        if (!IsValidPhoneNumber(payload.Recipient))
        {
            errors.Add("Invalid phone number format");
        }

        // Validate message content
        if (string.IsNullOrWhiteSpace(payload.Content))
        {
            errors.Add("Message content cannot be empty");
        }
        else if (payload.Content.Length > GetMaxMessageLength())
        {
            errors.Add($"Message content exceeds maximum length of {GetMaxMessageLength()} characters");
        }

        // Provider-specific validation
        var providerErrors = await ValidateSmsSpecificAsync(payload, cancellationToken);
        errors.AddRange(providerErrors);

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            ErrorMessage = errors.Any() ? string.Join("; ", errors) : null
        };
    }

    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);

    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);

    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    #endregion

    #region SMS-Specific Abstract Methods

    /// <summary>
    /// Sends a single SMS message using the provider's API.
    /// </summary>
    protected abstract Task<MessageSendResult> SendSmsInternalAsync(MessagePayload payload, CancellationToken cancellationToken);

    /// <summary>
    /// Sends multiple SMS messages in a single operation.
    /// </summary>
    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkSmsInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);

    /// <summary>
    /// Gets an SMS template by ID.
    /// </summary>
    protected abstract Task<string?> GetSmsTemplateAsync(string templateId, CancellationToken cancellationToken);

    /// <summary>
    /// Performs provider-specific SMS validation.
    /// </summary>
    protected abstract Task<IEnumerable<string>> ValidateSmsSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);

    #endregion

    #region SMS-Specific Helper Methods

    /// <summary>
    /// Validates phone number format for SMS.
    /// </summary>
    protected virtual bool IsValidPhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Remove common formatting characters
        var cleaned = Regex.Replace(phoneNumber, @"[\s\-\(\)\+]", "");
        
        // Basic validation: should be 10-15 digits
        return Regex.IsMatch(cleaned, @"^\d{10,15}$");
    }

    /// <summary>
    /// Gets the maximum message length for SMS (default 160 characters).
    /// </summary>
    protected virtual int GetMaxMessageLength() => 160;

    /// <summary>
    /// Normalizes SMS payload for consistent processing.
    /// </summary>
    protected virtual MessagePayload NormalizeSmsPayload(MessagePayload payload)
    {
        var normalizedRecipient = NormalizePhoneNumber(payload.Recipient);
        return payload with { Recipient = normalizedRecipient };
    }

    /// <summary>
    /// Normalizes phone number format.
    /// </summary>
    protected virtual string NormalizePhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return phoneNumber;

        // Remove formatting and ensure it starts with +
        var cleaned = Regex.Replace(phoneNumber, @"[\s\-\(\)]", "");
        if (!cleaned.StartsWith("+"))
        {
            cleaned = "+" + cleaned;
        }
        return cleaned;
    }

    /// <summary>
    /// Processes SMS template with data substitution.
    /// </summary>
    protected virtual string ProcessSmsTemplate(string template, IDictionary<string, string> templateData)
    {
        var result = template;
        foreach (var kvp in templateData)
        {
            result = result.Replace($"{{{kvp.Key}}}", kvp.Value);
        }
        return result;
    }

    /// <summary>
    /// Gets the default sender for SMS messages.
    /// </summary>
    protected virtual string? GetDefaultSender()
    {
        return _configuration.TryGetValue("DefaultSender", out var sender) ? sender?.ToString() : null;
    }

    /// <summary>
    /// Sets configuration value.
    /// </summary>
    protected virtual void SetConfiguration(string key, object value)
    {
        _configuration[key] = value;
    }

    /// <summary>
    /// Gets configuration value.
    /// </summary>
    protected virtual T? GetConfiguration<T>(string key)
    {
        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    #endregion

    #region IGatewayAdminPluginType Implementation

    public virtual async Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        var configs = new List<GatewayConfiguration>();

        // Add SMS-specific configuration items
        configs.Add(new GatewayConfiguration("DefaultSender", "Default SMS sender ID", GetConfiguration<string>("DefaultSender") ?? "", "Default SMS sender ID or phone number", false, false));
        configs.Add(new GatewayConfiguration("MaxMessageLength", "Maximum SMS message length", GetConfiguration<int>("MaxMessageLength").ToString() ?? "160", "Maximum length for SMS messages", false, false, "int"));
        configs.Add(new GatewayConfiguration("EnableDeliveryReports", "Enable SMS delivery reports", GetConfiguration<bool>("EnableDeliveryReports").ToString() ?? "true", "Enable or disable SMS delivery reports", false, false, "bool"));

        // Add provider-specific configurations
        var providerConfigs = await GetSmsProviderConfigurationsAsync(cancellationToken);
        configs.AddRange(providerConfigs);

        return configs.AsReadOnly();
    }

    public virtual async Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default)
    {
        try
        {
            foreach (var setting in settingsToUpdate)
            {
                switch (setting.Key)
                {
                    case "DefaultSender":
                        if (setting.Value != null)
                            SetConfiguration("DefaultSender", setting.Value);
                        break;
                    case "MaxMessageLength":
                        if (int.TryParse(setting.Value, out var maxLength))
                            SetConfiguration("MaxMessageLength", maxLength);
                        break;
                    case "EnableDeliveryReports":
                        if (bool.TryParse(setting.Value, out var enableReports))
                            SetConfiguration("EnableDeliveryReports", enableReports);
                        break;
                    default:
                        await UpdateSmsProviderConfigurationAsync(setting, cancellationToken);
                        break;
                }
            }

            return new OperationResult(true, "Configuration updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update SMS gateway configuration");
            return new OperationResult(false, "Failed to update configuration", new[] { new ErrorDetail("ConfigurationError", ex.Message) });
        }
    }

    public virtual async Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Test basic SMS functionality
            var isAvailable = await IsAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                return new OperationResult(false, "SMS gateway is not available");
            }

            // Perform SMS-specific configuration tests
            var testResult = await TestSmsConfigurationAsync(cancellationToken);
            return testResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SMS configuration test failed");
            return new OperationResult(false, "Configuration test failed", new[] { new ErrorDetail("TestError", ex.Message) });
        }
    }

    public virtual async Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default)
    {
        return await GetSmsManifestAsync(cancellationToken);
    }

    #endregion

    #region IGatewayMetricsPluginType Implementation

    public virtual async Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default)
    {
        return await GetSmsStatusReportAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default)
    {
        return await GetSmsDeliveryReportsAsync(maxItems, cancellationToken);
    }

    public virtual async Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetSmsUsageMetricsAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetSmsErrorLogAsync(from, to, cancellationToken);
    }

    public virtual async Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default)
    {
        return await GetSmsPerformanceSnapshotAsync(resolution, cancellationToken);
    }

    public virtual async Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetSmsSlaReportAsync(from, to, cancellationToken);
    }

    public virtual async Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetSmsLatencyMetricsAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = "daily", CancellationToken cancellationToken = default)
    {
        return await GetSmsTrafficTrendsAsync(granularity, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetSmsAnomalyReportAsync(from, to, cancellationToken);
    }

    public virtual async Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default)
    {
        return await GenerateSmsMetricsReportAsync(options, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetSmsRetryHistoryAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default)
    {
        return await GetSmsChangeImpactHistoryAsync(cancellationToken);
    }

    #endregion

    #region SMS-Specific Abstract Methods for Admin

    /// <summary>
    /// Gets SMS provider-specific configuration items.
    /// </summary>
    protected abstract Task<IEnumerable<GatewayConfiguration>> GetSmsProviderConfigurationsAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Updates SMS provider-specific configuration.
    /// </summary>
    protected abstract Task UpdateSmsProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken);

    /// <summary>
    /// Tests SMS provider-specific configuration.
    /// </summary>
    protected abstract Task<OperationResult> TestSmsConfigurationAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Gets the SMS plugin manifest.
    /// </summary>
    protected abstract Task<PluginManifest> GetSmsManifestAsync(CancellationToken cancellationToken);

    #endregion

    #region SMS-Specific Abstract Methods for Metrics

    /// <summary>
    /// Gets SMS-specific status report.
    /// </summary>
    protected abstract Task<GatewayStatusReport> GetSmsStatusReportAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS delivery reports.
    /// </summary>
    protected abstract Task<IReadOnlyList<DeliveryResult>> GetSmsDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS usage metrics.
    /// </summary>
    protected abstract Task<UsageMetrics> GetSmsUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS error logs.
    /// </summary>
    protected abstract Task<IReadOnlyList<GatewayErrorEntry>> GetSmsErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS performance snapshot.
    /// </summary>
    protected abstract Task<PerformanceSnapshot> GetSmsPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS SLA report.
    /// </summary>
    protected abstract Task<SlaReport> GetSmsSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS latency metrics.
    /// </summary>
    protected abstract Task<LatencyMetrics> GetSmsLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS traffic trends.
    /// </summary>
    protected abstract Task<IReadOnlyList<TrafficTrend>> GetSmsTrafficTrendsAsync(string granularity, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS anomaly report.
    /// </summary>
    protected abstract Task<IReadOnlyList<AnomalyDetectionResult>> GetSmsAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Generates SMS metrics report.
    /// </summary>
    protected abstract Task<GeneratedReport> GenerateSmsMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS retry history.
    /// </summary>
    protected abstract Task<IReadOnlyList<RetryAttemptInfo>> GetSmsRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets SMS configuration change impact history.
    /// </summary>
    protected abstract Task<IReadOnlyList<ConfigurationImpactRecord>> GetSmsChangeImpactHistoryAsync(CancellationToken cancellationToken);

    #endregion
}
