{"sourceFile": "src/Plugins/Plugin.Sms.Twilio/Plugin.Sms.Twilio.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751221137421, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751222780990, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,11 +13,8 @@\n \r\n \r\n \r\n \r\n-\r\n-\r\n-\r\n <Project Sdk=\"Microsoft.NET.Sdk\">\r\n \r\n     <PropertyGroup>\r\n         <TargetFramework>net9.0</TargetFramework>\r\n@@ -26,9 +23,9 @@\n         <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>\r\n     </PropertyGroup>\r\n \r\n     <ItemGroup>\r\n-        <PackageReference Include=\"Twilio\" Version=\"7.6.0\" />\r\n+        <PackageReference Include=\"Twilio\" Version=\"7.11.3\" />\r\n         <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.Extensions.Options\" Version=\"9.0.6\" />\r\n     </ItemGroup>\r\n \r\n"}], "date": 1751221137421, "name": "Commit-0", "content": "\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<Project Sdk=\"Microsoft.NET.Sdk\">\r\n\r\n    <PropertyGroup>\r\n        <TargetFramework>net9.0</TargetFramework>\r\n        <Nullable>enable</Nullable>\r\n        <ImplicitUsings>enable</ImplicitUsings>\r\n        <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>\r\n    </PropertyGroup>\r\n\r\n    <ItemGroup>\r\n        <PackageReference Include=\"Twilio\" Version=\"7.6.0\" />\r\n        <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\r\n        <PackageReference Include=\"Microsoft.Extensions.Options\" Version=\"9.0.6\" />\r\n    </ItemGroup>\r\n\r\n    <ItemGroup>\r\n        <ProjectReference Include=\"..\\..\\Contracts\\PluginContract\\PluginContract.csproj\" />\r\n        <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\r\n    </ItemGroup>\r\n\r\n    <ItemGroup>\r\n        <EmbeddedResource Include=\"manifest.json\" />\r\n    </ItemGroup>\r\n\r\n</Project>\r\n"}]}