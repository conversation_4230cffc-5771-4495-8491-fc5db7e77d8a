{"sourceFile": "src/Core/PluginCore/Base/MessageStorageService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751214161798, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751214161798, "name": "Commit-0", "content": "using PluginCore.Models;\nusing Microsoft.Extensions.Logging;\n\nnamespace PluginCore.Base;\n\n/// <summary>\n/// Service interface for storing and retrieving messages for resending capabilities.\n/// </summary>\npublic interface IMessageStorageService\n{\n    /// <summary>\n    /// Stores a message for potential resending.\n    /// </summary>\n    Task<OperationResult> StoreMessageAsync(\n        string messageId,\n        string gatewayId,\n        MessagePayload payload,\n        MessageSendResult sendResult,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Retrieves a stored message by ID.\n    /// </summary>\n    Task<StoredMessage?> GetStoredMessageAsync(\n        string messageId,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Gets all stored messages for a gateway.\n    /// </summary>\n    Task<IReadOnlyList<StoredMessage>> GetStoredMessagesAsync(\n        string gatewayId,\n        DateTimeOffset? fromDate = null,\n        DateTimeOffset? toDate = null,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Updates the send result for a stored message.\n    /// </summary>\n    Task<OperationResult> UpdateSendResultAsync(\n        string messageId,\n        MessageSendResult sendResult,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Deletes old stored messages based on retention policy.\n    /// </summary>\n    Task<OperationResult> CleanupOldMessagesAsync(\n        TimeSpan retentionPeriod,\n        CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Gets message statistics for a gateway.\n    /// </summary>\n    Task<MessageStatistics> GetMessageStatisticsAsync(\n        string gatewayId,\n        DateTimeOffset fromDate,\n        DateTimeOffset toDate,\n        CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Represents a stored message with its send history.\n/// </summary>\npublic record StoredMessage(\n    string MessageId,\n    string GatewayId,\n    MessagePayload Payload,\n    MessageSendResult OriginalSendResult,\n    DateTimeOffset StoredAt,\n    List<MessageSendResult> ResendHistory);\n\n/// <summary>\n/// Message statistics for a gateway.\n/// </summary>\npublic record MessageStatistics(\n    int TotalMessages,\n    int SuccessfulMessages,\n    int FailedMessages,\n    int ResentMessages,\n    double SuccessRate);\n\n/// <summary>\n/// Default implementation of the message storage service.\n/// Uses in-memory storage by default, but can be extended for persistent storage.\n/// </summary>\npublic class DefaultMessageStorageService : IMessageStorageService\n{\n    private readonly Dictionary<string, StoredMessage> _storedMessages = new();\n    private readonly object _lock = new();\n    private readonly ILogger? _logger;\n\n    public DefaultMessageStorageService(ILogger? logger = null)\n    {\n        _logger = logger;\n    }\n\n    public Task<OperationResult> StoreMessageAsync(\n        string messageId,\n        string gatewayId,\n        MessagePayload payload,\n        MessageSendResult sendResult,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            var storedMessage = new StoredMessage(\n                MessageId: messageId,\n                GatewayId: gatewayId,\n                Payload: payload,\n                OriginalSendResult: sendResult,\n                StoredAt: DateTimeOffset.UtcNow,\n                ResendHistory: new List<MessageSendResult>()\n            );\n\n            _storedMessages[messageId] = storedMessage;\n            \n            _logger?.LogDebug(\"Message {MessageId} stored for gateway {GatewayId}\", messageId, gatewayId);\n            \n            return Task.FromResult(new OperationResult(true, \"Message stored successfully\"));\n        }\n    }\n\n    public Task<StoredMessage?> GetStoredMessageAsync(\n        string messageId,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            _storedMessages.TryGetValue(messageId, out var message);\n            return Task.FromResult(message);\n        }\n    }\n\n    public Task<IReadOnlyList<StoredMessage>> GetStoredMessagesAsync(\n        string gatewayId,\n        DateTimeOffset? fromDate = null,\n        DateTimeOffset? toDate = null,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            var query = _storedMessages.Values.Where(m => m.GatewayId == gatewayId);\n\n            if (fromDate.HasValue)\n                query = query.Where(m => m.StoredAt >= fromDate.Value);\n\n            if (toDate.HasValue)\n                query = query.Where(m => m.StoredAt <= toDate.Value);\n\n            var messages = query.OrderByDescending(m => m.StoredAt)\n                               .ToList()\n                               .AsReadOnly();\n\n            return Task.FromResult<IReadOnlyList<StoredMessage>>(messages);\n        }\n    }\n\n    public Task<OperationResult> UpdateSendResultAsync(\n        string messageId,\n        MessageSendResult sendResult,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            if (!_storedMessages.TryGetValue(messageId, out var storedMessage))\n            {\n                return Task.FromResult(new OperationResult(false, \"Stored message not found\"));\n            }\n\n            // Add to resend history\n            storedMessage.ResendHistory.Add(sendResult);\n            \n            _logger?.LogDebug(\"Send result updated for message {MessageId}\", messageId);\n            \n            return Task.FromResult(new OperationResult(true, \"Send result updated\"));\n        }\n    }\n\n    public Task<OperationResult> CleanupOldMessagesAsync(\n        TimeSpan retentionPeriod,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            var cutoffDate = DateTimeOffset.UtcNow - retentionPeriod;\n            var messagesToRemove = _storedMessages.Values\n                .Where(m => m.StoredAt < cutoffDate)\n                .Select(m => m.MessageId)\n                .ToList();\n\n            foreach (var messageId in messagesToRemove)\n            {\n                _storedMessages.Remove(messageId);\n            }\n\n            _logger?.LogInformation(\"Cleaned up {Count} old messages\", messagesToRemove.Count);\n            \n            return Task.FromResult(new OperationResult(true, $\"Cleaned up {messagesToRemove.Count} old messages\"));\n        }\n    }\n\n    public Task<MessageStatistics> GetMessageStatisticsAsync(\n        string gatewayId,\n        DateTimeOffset fromDate,\n        DateTimeOffset toDate,\n        CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            var messages = _storedMessages.Values\n                .Where(m => m.GatewayId == gatewayId && \n                           m.StoredAt >= fromDate && \n                           m.StoredAt <= toDate)\n                .ToList();\n\n            var totalMessages = messages.Count;\n            var successfulMessages = messages.Count(m => m.OriginalSendResult.Status == \"Sent\" || m.OriginalSendResult.Status == \"Delivered\");\n            var failedMessages = messages.Count(m => m.OriginalSendResult.Status == \"Failed\");\n            var resentMessages = messages.Count(m => m.ResendHistory.Any());\n            var successRate = totalMessages > 0 ? (double)successfulMessages / totalMessages * 100 : 0;\n\n            return Task.FromResult(new MessageStatistics(\n                TotalMessages: totalMessages,\n                SuccessfulMessages: successfulMessages,\n                FailedMessages: failedMessages,\n                ResentMessages: resentMessages,\n                SuccessRate: successRate\n            ));\n        }\n    }\n}\n\n/// <summary>\n/// Base class that provides scheduling and storage capabilities to gateway implementations.\n/// </summary>\npublic abstract class GatewayWithSchedulingBase\n{\n    protected readonly IMessageSchedulingService _schedulingService;\n    protected readonly IMessageStorageService _storageService;\n    protected readonly ILogger? _logger;\n    protected readonly string _gatewayId;\n\n    protected GatewayWithSchedulingBase(\n        ILogger? logger = null,\n        IMessageSchedulingService? schedulingService = null,\n        IMessageStorageService? storageService = null)\n    {\n        _logger = logger;\n        _schedulingService = schedulingService ?? new DefaultMessageSchedulingService(logger);\n        _storageService = storageService ?? new DefaultMessageStorageService(logger);\n        _gatewayId = GetType().Name;\n    }\n\n    /// <summary>\n    /// Implements message scheduling using the scheduling service.\n    /// </summary>\n    protected virtual async Task<MessageScheduleResult> ScheduleMessageWithServiceAsync(\n        MessagePayload payload,\n        DateTimeOffset scheduledTime,\n        CancellationToken cancellationToken = default)\n    {\n        return await _schedulingService.ScheduleMessageAsync(_gatewayId, payload, scheduledTime, cancellationToken);\n    }\n\n    /// <summary>\n    /// Implements message cancellation using the scheduling service.\n    /// </summary>\n    protected virtual async Task<OperationResult> CancelScheduledMessageWithServiceAsync(\n        string scheduledMessageId,\n        CancellationToken cancellationToken = default)\n    {\n        return await _schedulingService.CancelScheduledMessageAsync(scheduledMessageId, cancellationToken);\n    }\n\n    /// <summary>\n    /// Implements message resending using the storage service.\n    /// </summary>\n    protected virtual async Task<MessageSendResult> ResendMessageWithServiceAsync(\n        string originalMessageId,\n        CancellationToken cancellationToken = default)\n    {\n        var storedMessage = await _storageService.GetStoredMessageAsync(originalMessageId, cancellationToken);\n        if (storedMessage == null)\n        {\n            return new MessageSendResult(\n                MessageId: Guid.NewGuid().ToString(),\n                Timestamp: DateTimeOffset.UtcNow,\n                Status: \"Failed\",\n                CorrelationId: null\n            );\n        }\n\n        // Create a new correlation ID for the resend\n        var resendPayload = storedMessage.Payload with \n        { \n            CorrelationId = $\"resend_{originalMessageId}_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}\"\n        };\n\n        // Call the actual send method (must be implemented by derived class)\n        var sendResult = await SendMessageInternalAsync(resendPayload, cancellationToken);\n        \n        // Update the storage with the resend result\n        await _storageService.UpdateSendResultAsync(originalMessageId, sendResult, cancellationToken);\n        \n        return sendResult;\n    }\n\n    /// <summary>\n    /// Stores a message after sending for potential resending.\n    /// </summary>\n    protected virtual async Task StoreMessageAsync(\n        string messageId,\n        MessagePayload payload,\n        MessageSendResult sendResult,\n        CancellationToken cancellationToken = default)\n    {\n        await _storageService.StoreMessageAsync(messageId, _gatewayId, payload, sendResult, cancellationToken);\n    }\n\n    /// <summary>\n    /// Abstract method that derived classes must implement for actual message sending.\n    /// </summary>\n    protected abstract Task<MessageSendResult> SendMessageInternalAsync(\n        MessagePayload payload,\n        CancellationToken cancellationToken);\n}\n"}]}