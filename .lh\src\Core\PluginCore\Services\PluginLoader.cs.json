{"sourceFile": "src/Core/PluginCore/Services/PluginLoader.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751213842537, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751213842537, "name": "Commit-0", "content": "using Microsoft.Extensions.DependencyInjection;\nusing Microsoft.Extensions.Hosting;\nusing Microsoft.Extensions.Logging;\nusing Microsoft.Extensions.Options;\nusing PluginCore.Base;\nusing PluginCore.Extensions;\nusing PluginCore.Interfaces;\n\nnamespace PluginCore.Services;\n\n/// <summary>\n/// Background service that loads plugins on application startup.\n/// </summary>\npublic class PluginLoaderService : BackgroundService\n{\n    private readonly IServiceProvider _serviceProvider;\n    private readonly ILogger<PluginLoaderService> _logger;\n    private readonly GatewayPluginSystemOptions _options;\n\n    public PluginLoaderService(\n        IServiceProvider serviceProvider,\n        ILogger<PluginLoaderService> logger,\n        IOptions<GatewayPluginSystemOptions> options)\n    {\n        _serviceProvider = serviceProvider;\n        _logger = logger;\n        _options = options.Value;\n    }\n\n    protected override async Task ExecuteAsync(CancellationToken stoppingToken)\n    {\n        if (!_options.AutoLoadPlugins)\n        {\n            _logger.LogInformation(\"Auto-loading plugins is disabled\");\n            return;\n        }\n\n        try\n        {\n            _logger.LogInformation(\"Starting plugin loading from directory: {PluginDirectory}\", _options.PluginDirectory);\n\n            var pluginManager = _serviceProvider.GetRequiredService<IPluginManager>();\n            var result = await pluginManager.LoadPluginsAsync(_options.PluginDirectory, stoppingToken);\n\n            if (result.IsSuccess)\n            {\n                _logger.LogInformation(\"Plugin loading completed successfully: {Message}\", result.Message);\n                \n                // Log loaded plugins\n                var manifests = pluginManager.GetPluginManifests();\n                foreach (var manifest in manifests)\n                {\n                    _logger.LogInformation(\"Loaded plugin: {Name} v{Version} ({Type})\", \n                        manifest.Name, manifest.Version, manifest.Type);\n                }\n            }\n            else\n            {\n                _logger.LogError(\"Plugin loading failed: {Message}\", result.Message);\n            }\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to load plugins during startup\");\n        }\n    }\n}\n\n/// <summary>\n/// Static helper class for loading plugins manually.\n/// </summary>\npublic static class PluginLoader\n{\n    /// <summary>\n    /// Loads plugins from the specified directory using the provided service provider.\n    /// </summary>\n    public static async Task<bool> LoadPluginsAsync(\n        IServiceProvider serviceProvider, \n        string pluginDirectory,\n        ILogger? logger = null)\n    {\n        try\n        {\n            logger?.LogInformation(\"Loading plugins from directory: {PluginDirectory}\", pluginDirectory);\n\n            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\n            var result = await pluginManager.LoadPluginsAsync(pluginDirectory);\n\n            if (result.IsSuccess)\n            {\n                logger?.LogInformation(\"Plugins loaded successfully: {Message}\", result.Message);\n                \n                // Log loaded plugins\n                var manifests = pluginManager.GetPluginManifests();\n                foreach (var manifest in manifests)\n                {\n                    logger?.LogInformation(\"Loaded plugin: {Name} v{Version} ({Type})\", \n                        manifest.Name, manifest.Version, manifest.Type);\n                }\n                \n                return true;\n            }\n            else\n            {\n                logger?.LogError(\"Failed to load plugins: {Message}\", result.Message);\n                return false;\n            }\n        }\n        catch (Exception ex)\n        {\n            logger?.LogError(ex, \"Exception occurred while loading plugins\");\n            return false;\n        }\n    }\n\n    /// <summary>\n    /// Gets all loaded plugins of a specific type.\n    /// </summary>\n    public static IReadOnlyList<T> GetPlugins<T>(IServiceProvider serviceProvider) where T : class, IPluginType\n    {\n        try\n        {\n            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\n            return pluginManager.GetPlugins<T>();\n        }\n        catch (Exception)\n        {\n            return new List<T>().AsReadOnly();\n        }\n    }\n\n    /// <summary>\n    /// Gets a specific plugin by name and type.\n    /// </summary>\n    public static T? GetPlugin<T>(IServiceProvider serviceProvider, string pluginName) where T : class, IPluginType\n    {\n        try\n        {\n            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\n            return pluginManager.GetPlugin<T>(pluginName);\n        }\n        catch (Exception)\n        {\n            return null;\n        }\n    }\n\n    /// <summary>\n    /// Gets the status of all loaded plugins.\n    /// </summary>\n    public static async Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(\n        IServiceProvider serviceProvider,\n        CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\n            return await pluginManager.GetPluginStatusesAsync(cancellationToken);\n        }\n        catch (Exception)\n        {\n            return new List<PluginStatus>().AsReadOnly();\n        }\n    }\n\n    /// <summary>\n    /// Validates a plugin file before loading.\n    /// </summary>\n    public static async Task<bool> ValidatePluginAsync(\n        IServiceProvider serviceProvider,\n        string pluginPath,\n        ILogger? logger = null,\n        CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\n            var result = await pluginManager.ValidatePluginAsync(pluginPath, cancellationToken);\n\n            if (result.IsValid)\n            {\n                logger?.LogInformation(\"Plugin validation successful: {PluginPath}\", pluginPath);\n                return true;\n            }\n            else\n            {\n                logger?.LogWarning(\"Plugin validation failed for {PluginPath}: {ErrorMessage}\", \n                    pluginPath, result.ErrorMessage);\n                return false;\n            }\n        }\n        catch (Exception ex)\n        {\n            logger?.LogError(ex, \"Exception occurred while validating plugin: {PluginPath}\", pluginPath);\n            return false;\n        }\n    }\n}\n\n/// <summary>\n/// Extension methods for easier plugin loading in startup.\n/// </summary>\npublic static class PluginLoaderExtensions\n{\n    /// <summary>\n    /// Adds the plugin loader service to run on startup.\n    /// </summary>\n    public static IServiceCollection AddPluginLoader(this IServiceCollection services)\n    {\n        services.AddHostedService<PluginLoaderService>();\n        return services;\n    }\n\n    /// <summary>\n    /// Loads plugins immediately during application startup.\n    /// </summary>\n    public static async Task<IServiceProvider> LoadPluginsAsync(\n        this IServiceProvider serviceProvider,\n        string pluginDirectory,\n        ILogger? logger = null)\n    {\n        await PluginLoader.LoadPluginsAsync(serviceProvider, pluginDirectory, logger);\n        return serviceProvider;\n    }\n\n    /// <summary>\n    /// Configures and loads the complete gateway plugin system.\n    /// </summary>\n    public static IServiceCollection AddCompleteGatewayPluginSystem(\n        this IServiceCollection services,\n        string pluginDirectory = \"plugins\",\n        bool autoLoad = true)\n    {\n        // Add the core plugin system\n        services.AddGatewayPluginSystem();\n        \n        // Configure options\n        services.ConfigureGatewayPluginSystem(options =>\n        {\n            options.PluginDirectory = pluginDirectory;\n            options.AutoLoadPlugins = autoLoad;\n        });\n\n        // Add plugin loader if auto-load is enabled\n        if (autoLoad)\n        {\n            services.AddPluginLoader();\n        }\n\n        return services;\n    }\n}\n\n/// <summary>\n/// Simple plugin discovery helper.\n/// </summary>\npublic static class PluginDiscovery\n{\n    /// <summary>\n    /// Discovers all plugin files in a directory.\n    /// </summary>\n    public static IEnumerable<string> DiscoverPluginFiles(string directory)\n    {\n        if (!Directory.Exists(directory))\n        {\n            return Enumerable.Empty<string>();\n        }\n\n        return Directory.GetFiles(directory, \"*.dll\", SearchOption.AllDirectories)\n            .Where(file => !Path.GetFileName(file).StartsWith(\"System.\") &&\n                          !Path.GetFileName(file).StartsWith(\"Microsoft.\") &&\n                          !Path.GetFileName(file).StartsWith(\"Newtonsoft.\") &&\n                          Path.GetFileName(file).Contains(\"Plugin\"));\n    }\n\n    /// <summary>\n    /// Gets plugin information without loading the assembly.\n    /// </summary>\n    public static PluginFileInfo? GetPluginFileInfo(string pluginPath)\n    {\n        try\n        {\n            if (!File.Exists(pluginPath))\n                return null;\n\n            var fileInfo = new FileInfo(pluginPath);\n            return new PluginFileInfo(\n                Path: pluginPath,\n                FileName: fileInfo.Name,\n                Size: fileInfo.Length,\n                LastModified: fileInfo.LastWriteTime,\n                Directory: fileInfo.DirectoryName ?? \"\"\n            );\n        }\n        catch\n        {\n            return null;\n        }\n    }\n}\n\n/// <summary>\n/// Information about a plugin file.\n/// </summary>\npublic record PluginFileInfo(\n    string Path,\n    string FileName,\n    long Size,\n    DateTime LastModified,\n    string Directory);\n"}]}