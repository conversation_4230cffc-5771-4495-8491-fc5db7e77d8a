{"sourceFile": "src/Core/PluginCore/Services/TenantContext.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751215140212, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751215140212, "name": "Commit-0", "content": "using PluginCore.Models;\nusing PluginCore.Interfaces;\n\nnamespace PluginCore.Services;\n\n/// <summary>\n/// Interface for tenant context service\n/// </summary>\npublic interface ITenantContext\n{\n    /// <summary>\n    /// Gets the current tenant ID\n    /// </summary>\n    string? TenantId { get; }\n    \n    /// <summary>\n    /// Gets the current tenant\n    /// </summary>\n    Tenant? CurrentTenant { get; }\n    \n    /// <summary>\n    /// Gets the current user ID\n    /// </summary>\n    string? UserId { get; }\n    \n    /// <summary>\n    /// Gets the current user\n    /// </summary>\n    User? CurrentUser { get; }\n    \n    /// <summary>\n    /// Sets the current tenant context\n    /// </summary>\n    void SetTenant(string tenantId, Tenant? tenant = null);\n    \n    /// <summary>\n    /// Sets the current user context\n    /// </summary>\n    void SetUser(string userId, User? user = null);\n    \n    /// <summary>\n    /// Clears the current context\n    /// </summary>\n    void Clear();\n    \n    /// <summary>\n    /// Checks if the current user has a specific permission\n    /// </summary>\n    Task<bool> HasPermissionAsync(string resource, string action);\n    \n    /// <summary>\n    /// Checks if the current user has a specific role\n    /// </summary>\n    Task<bool> HasRoleAsync(string roleName);\n    \n    /// <summary>\n    /// Gets all permissions for the current user\n    /// </summary>\n    Task<IReadOnlyList<Permission>> GetUserPermissionsAsync();\n    \n    /// <summary>\n    /// Gets all roles for the current user\n    /// </summary>\n    Task<IReadOnlyList<Role>> GetUserRolesAsync();\n}\n\n/// <summary>\n/// Implementation of tenant context service\n/// </summary>\npublic class TenantContext : ITenantContext\n{\n    private readonly ILogger<TenantContext> _logger;\n    private readonly ITenantService _tenantService;\n    private readonly IUserService _userService;\n    \n    private string? _tenantId;\n    private Tenant? _currentTenant;\n    private string? _userId;\n    private User? _currentUser;\n\n    public TenantContext(\n        ILogger<TenantContext> logger,\n        ITenantService tenantService,\n        IUserService userService)\n    {\n        _logger = logger;\n        _tenantService = tenantService;\n        _userService = userService;\n    }\n\n    public string? TenantId => _tenantId;\n    public Tenant? CurrentTenant => _currentTenant;\n    public string? UserId => _userId;\n    public User? CurrentUser => _currentUser;\n\n    public void SetTenant(string tenantId, Tenant? tenant = null)\n    {\n        _tenantId = tenantId;\n        _currentTenant = tenant;\n        \n        _logger.LogDebug(\"Tenant context set to: {TenantId}\", tenantId);\n    }\n\n    public void SetUser(string userId, User? user = null)\n    {\n        _userId = userId;\n        _currentUser = user;\n        \n        _logger.LogDebug(\"User context set to: {UserId}\", userId);\n    }\n\n    public void Clear()\n    {\n        _tenantId = null;\n        _currentTenant = null;\n        _userId = null;\n        _currentUser = null;\n        \n        _logger.LogDebug(\"Tenant context cleared\");\n    }\n\n    public async Task<bool> HasPermissionAsync(string resource, string action)\n    {\n        if (string.IsNullOrEmpty(_userId) || string.IsNullOrEmpty(_tenantId))\n        {\n            return false;\n        }\n\n        try\n        {\n            return await _userService.HasPermissionAsync(_userId, _tenantId, resource, action);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking permission {Resource}:{Action} for user {UserId}\", \n                resource, action, _userId);\n            return false;\n        }\n    }\n\n    public async Task<bool> HasRoleAsync(string roleName)\n    {\n        if (string.IsNullOrEmpty(_userId) || string.IsNullOrEmpty(_tenantId))\n        {\n            return false;\n        }\n\n        try\n        {\n            return await _userService.HasRoleAsync(_userId, _tenantId, roleName);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking role {RoleName} for user {UserId}\", \n                roleName, _userId);\n            return false;\n        }\n    }\n\n    public async Task<IReadOnlyList<Permission>> GetUserPermissionsAsync()\n    {\n        if (string.IsNullOrEmpty(_userId) || string.IsNullOrEmpty(_tenantId))\n        {\n            return Array.Empty<Permission>();\n        }\n\n        try\n        {\n            return await _userService.GetUserPermissionsAsync(_userId, _tenantId);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting permissions for user {UserId}\", _userId);\n            return Array.Empty<Permission>();\n        }\n    }\n\n    public async Task<IReadOnlyList<Role>> GetUserRolesAsync()\n    {\n        if (string.IsNullOrEmpty(_userId) || string.IsNullOrEmpty(_tenantId))\n        {\n            return Array.Empty<Role>();\n        }\n\n        try\n        {\n            return await _userService.GetUserRolesAsync(_userId, _tenantId);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting roles for user {UserId}\", _userId);\n            return Array.Empty<Role>();\n        }\n    }\n}\n\n/// <summary>\n/// Tenant context middleware for HTTP requests\n/// </summary>\npublic class TenantContextMiddleware\n{\n    private readonly RequestDelegate _next;\n    private readonly ILogger<TenantContextMiddleware> _logger;\n\n    public TenantContextMiddleware(RequestDelegate next, ILogger<TenantContextMiddleware> logger)\n    {\n        _next = next;\n        _logger = logger;\n    }\n\n    public async Task InvokeAsync(HttpContext context, ITenantContext tenantContext, ITenantService tenantService)\n    {\n        try\n        {\n            // Extract tenant information from request\n            var tenantId = ExtractTenantId(context);\n            var userId = ExtractUserId(context);\n\n            if (!string.IsNullOrEmpty(tenantId))\n            {\n                // Load tenant information\n                var tenant = await tenantService.GetTenantAsync(tenantId);\n                tenantContext.SetTenant(tenantId, tenant);\n            }\n\n            if (!string.IsNullOrEmpty(userId))\n            {\n                tenantContext.SetUser(userId);\n            }\n\n            await _next(context);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error in tenant context middleware\");\n            throw;\n        }\n        finally\n        {\n            // Clear context after request\n            tenantContext.Clear();\n        }\n    }\n\n    private string? ExtractTenantId(HttpContext context)\n    {\n        // Try to get tenant ID from various sources\n        \n        // 1. From JWT claims\n        var tenantClaim = context.User?.FindFirst(\"tenant_id\")?.Value;\n        if (!string.IsNullOrEmpty(tenantClaim))\n        {\n            return tenantClaim;\n        }\n\n        // 2. From custom header\n        if (context.Request.Headers.TryGetValue(\"X-Tenant-Id\", out var tenantHeader))\n        {\n            return tenantHeader.FirstOrDefault();\n        }\n\n        // 3. From subdomain (e.g., tenant1.notifymaster.com)\n        var host = context.Request.Host.Host;\n        if (host.Contains('.'))\n        {\n            var subdomain = host.Split('.')[0];\n            if (!string.IsNullOrEmpty(subdomain) && subdomain != \"www\" && subdomain != \"api\")\n            {\n                return subdomain;\n            }\n        }\n\n        // 4. From query parameter\n        if (context.Request.Query.TryGetValue(\"tenant\", out var tenantQuery))\n        {\n            return tenantQuery.FirstOrDefault();\n        }\n\n        return null;\n    }\n\n    private string? ExtractUserId(HttpContext context)\n    {\n        // Extract user ID from JWT claims\n        return context.User?.FindFirst(\"sub\")?.Value ?? \n               context.User?.FindFirst(\"user_id\")?.Value;\n    }\n}\n\n/// <summary>\n/// Extension methods for tenant context\n/// </summary>\npublic static class TenantContextExtensions\n{\n    /// <summary>\n    /// Adds tenant context services to the service collection\n    /// </summary>\n    public static IServiceCollection AddTenantContext(this IServiceCollection services)\n    {\n        services.AddScoped<ITenantContext, TenantContext>();\n        return services;\n    }\n\n    /// <summary>\n    /// Adds tenant context middleware to the application pipeline\n    /// </summary>\n    public static IApplicationBuilder UseTenantContext(this IApplicationBuilder app)\n    {\n        return app.UseMiddleware<TenantContextMiddleware>();\n    }\n}\n"}]}