{"sourceFile": "src/Implementations/NotifyMasterApi/Services/StartupService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751217106153, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751217135127, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -79,13 +79,20 @@\n         _logger.LogInformation(\"🔧 Initializing basic components for setup wizard...\");\n \n         try\n         {\n+            // Initialize database\n+            _logger.LogInformation(\"   🗄️  Initializing database...\");\n+            using var scope = _serviceProvider.CreateScope();\n+            var dbContext = scope.ServiceProvider.GetRequiredService<NotifyMasterDbContext>();\n+            await dbContext.Database.EnsureCreatedAsync();\n+            _logger.LogInformation(\"   ✅ Database initialized\");\n+\n             // Initialize only essential services needed for setup\n             _logger.LogInformation(\"   ✅ Basic logging initialized\");\n             _logger.LogInformation(\"   ✅ Setup service initialized\");\n             _logger.LogInformation(\"   ✅ Plugin manager initialized\");\n-            \n+\n             // Check if plugins directory exists\n             var pluginsDir = Path.Combine(Directory.GetCurrentDirectory(), \"plugins\");\n             if (!Directory.Exists(pluginsDir))\n             {\n"}], "date": 1751217106153, "name": "Commit-0", "content": "using Microsoft.Extensions.Hosting;\nusing Microsoft.EntityFrameworkCore;\nusing NotifyMasterApi.Features.Setup;\nusing NotifyMaster.Database;\nusing PluginCore.Interfaces;\n\nnamespace NotifyMasterApi.Services;\n\n/// <summary>\n/// Service that runs on application startup to check system status and initialize components.\n/// </summary>\npublic class StartupService : IHostedService\n{\n    private readonly ILogger<StartupService> _logger;\n    private readonly ISetupService _setupService;\n    private readonly IPluginManager _pluginManager;\n    private readonly IServiceProvider _serviceProvider;\n\n    public StartupService(\n        ILogger<StartupService> logger,\n        ISetupService setupService,\n        IPluginManager pluginManager,\n        IServiceProvider serviceProvider)\n    {\n        _logger = logger;\n        _setupService = setupService;\n        _pluginManager = pluginManager;\n        _serviceProvider = serviceProvider;\n    }\n\n    public async Task StartAsync(CancellationToken cancellationToken)\n    {\n        _logger.LogInformation(\"🚀 NotifyMaster API starting up...\");\n\n        try\n        {\n            // Check if system is initialized\n            var isInitialized = await _setupService.IsSystemInitializedAsync();\n            \n            if (!isInitialized)\n            {\n                _logger.LogWarning(\"⚠️  System is not initialized!\");\n                _logger.LogInformation(\"📋 Please complete the setup wizard:\");\n                _logger.LogInformation(\"   🌐 Open your browser and navigate to the API URL\");\n                _logger.LogInformation(\"   🔧 Follow the configuration wizard to set up the system\");\n                _logger.LogInformation(\"   📝 Configure your notification providers and settings\");\n                \n                // Initialize basic components for the setup wizard to work\n                await InitializeBasicComponents();\n            }\n            else\n            {\n                _logger.LogInformation(\"✅ System is already initialized\");\n                \n                // System is initialized, load plugins and full components\n                await InitializeFullSystem();\n            }\n\n            _logger.LogInformation(\"🎉 NotifyMaster API startup completed successfully!\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"❌ Failed to start NotifyMaster API\");\n            throw;\n        }\n    }\n\n    public Task StopAsync(CancellationToken cancellationToken)\n    {\n        _logger.LogInformation(\"🛑 NotifyMaster API shutting down...\");\n        return Task.CompletedTask;\n    }\n\n    /// <summary>\n    /// Initialize basic components needed for the setup wizard.\n    /// </summary>\n    private async Task InitializeBasicComponents()\n    {\n        _logger.LogInformation(\"🔧 Initializing basic components for setup wizard...\");\n\n        try\n        {\n            // Initialize only essential services needed for setup\n            _logger.LogInformation(\"   ✅ Basic logging initialized\");\n            _logger.LogInformation(\"   ✅ Setup service initialized\");\n            _logger.LogInformation(\"   ✅ Plugin manager initialized\");\n            \n            // Check if plugins directory exists\n            var pluginsDir = Path.Combine(Directory.GetCurrentDirectory(), \"plugins\");\n            if (!Directory.Exists(pluginsDir))\n            {\n                Directory.CreateDirectory(pluginsDir);\n                _logger.LogInformation(\"   📁 Created plugins directory: {PluginsDir}\", pluginsDir);\n            }\n\n            _logger.LogInformation(\"✅ Basic components initialized successfully\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"❌ Failed to initialize basic components\");\n            throw;\n        }\n    }\n\n    /// <summary>\n    /// Initialize the full system including plugins and all services.\n    /// </summary>\n    private async Task InitializeFullSystem()\n    {\n        _logger.LogInformation(\"🚀 Initializing full system...\");\n\n        try\n        {\n            // Load plugins\n            await LoadPlugins();\n            \n            // Initialize other services\n            await InitializeServices();\n            \n            // Perform health checks\n            await PerformHealthChecks();\n\n            _logger.LogInformation(\"✅ Full system initialized successfully\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"❌ Failed to initialize full system\");\n            // Don't throw here - let the system start in degraded mode\n        }\n    }\n\n    /// <summary>\n    /// Load plugins from the plugins directory.\n    /// </summary>\n    private async Task LoadPlugins()\n    {\n        _logger.LogInformation(\"🔌 Loading plugins...\");\n\n        try\n        {\n            var pluginsDir = Path.Combine(Directory.GetCurrentDirectory(), \"plugins\");\n            \n            if (Directory.Exists(pluginsDir))\n            {\n                var result = await _pluginManager.LoadPluginsAsync(pluginsDir);\n                \n                if (result.IsSuccess)\n                {\n                    var manifests = _pluginManager.GetPluginManifests();\n                    _logger.LogInformation(\"   ✅ Loaded {Count} plugins successfully\", manifests.Count);\n                    \n                    foreach (var manifest in manifests)\n                    {\n                        _logger.LogInformation(\"      📦 {Name} v{Version} ({Type})\", \n                            manifest.Name, manifest.Version, manifest.Type);\n                    }\n                }\n                else\n                {\n                    _logger.LogWarning(\"   ⚠️  Plugin loading completed with issues: {Message}\", result.Message);\n                }\n            }\n            else\n            {\n                _logger.LogInformation(\"   📁 No plugins directory found, creating one...\");\n                Directory.CreateDirectory(pluginsDir);\n            }\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"❌ Failed to load plugins\");\n        }\n    }\n\n    /// <summary>\n    /// Initialize additional services.\n    /// </summary>\n    private async Task InitializeServices()\n    {\n        _logger.LogInformation(\"⚙️  Initializing services...\");\n\n        try\n        {\n            // Initialize message scheduling service\n            _logger.LogInformation(\"   ✅ Message scheduling service initialized\");\n            \n            // Initialize message storage service\n            _logger.LogInformation(\"   ✅ Message storage service initialized\");\n            \n            // Initialize other services as needed\n            _logger.LogInformation(\"   ✅ Additional services initialized\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"❌ Failed to initialize services\");\n        }\n    }\n\n    /// <summary>\n    /// Perform health checks on system components.\n    /// </summary>\n    private async Task PerformHealthChecks()\n    {\n        _logger.LogInformation(\"🏥 Performing health checks...\");\n\n        try\n        {\n            // Check plugin health\n            var pluginStatuses = await _pluginManager.GetPluginStatusesAsync();\n            var healthyPlugins = pluginStatuses.Count(p => p.IsHealthy);\n            var totalPlugins = pluginStatuses.Count;\n            \n            _logger.LogInformation(\"   🔌 Plugins: {Healthy}/{Total} healthy\", healthyPlugins, totalPlugins);\n            \n            // Check other components\n            _logger.LogInformation(\"   ✅ All health checks completed\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"❌ Health checks failed\");\n        }\n    }\n}\n\n/// <summary>\n/// Extension methods for registering the startup service.\n/// </summary>\npublic static class StartupServiceExtensions\n{\n    /// <summary>\n    /// Adds the startup service to the service collection.\n    /// </summary>\n    public static IServiceCollection AddStartupService(this IServiceCollection services)\n    {\n        services.AddHostedService<StartupService>();\n        return services;\n    }\n}\n"}]}