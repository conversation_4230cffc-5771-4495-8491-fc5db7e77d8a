@inherits LayoutView
@inject IAppStateService AppState
@inject IAuthService AuthService
@inject IBreadcrumbService BreadcrumbService
@inject IJSRuntime JSRuntime
@implements IDisposable

<MudThemeProvider @ref="@_mudThemeProvider" @bind-IsDarkMode="@_isDarkMode" Theme="@_currentTheme" />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    @if (_isAuthenticated)
    {
        <MudAppBar Elevation="1" Class="app-bar" Fixed="true">
            <MudIconButton Icon="Icons.Material.Filled.Menu"
                          Color="Color.Inherit"
                          Edge="Edge.Start"
                          OnClick="@ToggleDrawer"
                          Class="d-lg-none" />
            <TopBar />
        </MudAppBar>

        <MudDrawer @bind-Open="_drawerOpen"
                   Elevation="2"
                   Variant="@_drawerVariant"
                   OpenMiniOnHover="@_openMiniOnHover"
                   ClipMode="DrawerClipMode.Always"
                   Class="sidebar-drawer">
            <Sidebar />
        </MudDrawer>

        <MudMainContent Class="main-content">
            <div class="content-wrapper">
                <!-- Breadcrumb Navigation -->
                <MudContainer MaxWidth="MaxWidth.False" Class="breadcrumb-container">
                    <BreadcrumbNavigation />
                </MudContainer>

                <!-- Page Content -->
                <MudContainer MaxWidth="MaxWidth.False" Class="page-content">
                    <div class="page-transition @_pageTransitionClass">
                        @Body
                    </div>
                </MudContainer>
            </div>
        </MudMainContent>
    }
    else
    {
        <MudMainContent>
            @Body
        </MudMainContent>
    }
</MudLayout>

@code {
    private MudThemeProvider? _mudThemeProvider;
    private bool _isDarkMode = false;
    private bool _drawerOpen = true;
    private bool _isAuthenticated = false;
    private DrawerVariant _drawerVariant = DrawerVariant.Mini;
    private bool _openMiniOnHover = true;
    private string _pageTransitionClass = "fade-in";
    private MudTheme _currentTheme = new();

    protected override async Task OnInitializedAsync()
    {
        await AppState.LoadStateAsync();
        _isDarkMode = AppState.IsDarkMode;
        _isAuthenticated = await AuthService.IsAuthenticatedAsync();

        // Set up responsive drawer behavior
        await SetupResponsiveDrawer();

        // Configure theme
        ConfigureTheme();

        AppState.OnStateChanged += StateHasChanged;
        AuthService.OnAuthStateChanged += OnAuthStateChanged;
        BreadcrumbService.OnBreadcrumbChanged += StateHasChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            if (_mudThemeProvider != null)
            {
                _isDarkMode = await _mudThemeProvider.GetSystemPreference();
                await AppState.SetThemeAsync(_isDarkMode);
            }

            // Set up window resize listener for responsive behavior
            await JSRuntime.InvokeVoidAsync("window.addEventListener", "resize",
                DotNetObjectReference.Create(this), "onWindowResize");

            StateHasChanged();
        }
    }

    private async Task SetupResponsiveDrawer()
    {
        try
        {
            var windowWidth = await JSRuntime.InvokeAsync<int>("window.innerWidth");
            if (windowWidth < 1200) // lg breakpoint
            {
                _drawerVariant = DrawerVariant.Temporary;
                _drawerOpen = false;
                _openMiniOnHover = false;
            }
            else
            {
                _drawerVariant = DrawerVariant.Mini;
                _drawerOpen = true;
                _openMiniOnHover = true;
            }
        }
        catch
        {
            // Fallback for SSR
            _drawerVariant = DrawerVariant.Mini;
            _drawerOpen = true;
        }
    }

    private void ConfigureTheme()
    {
        _currentTheme = new MudTheme()
        {
            Palette = new PaletteLight()
            {
                Primary = AppState.CurrentTenant?.Whitelabel.PrimaryColor ?? "#1976d2",
                Secondary = AppState.CurrentTenant?.Whitelabel.SecondaryColor ?? "#dc004e",
                AppbarBackground = AppState.CurrentTenant?.Whitelabel.PrimaryColor ?? "#1976d2",
            },
            PaletteDark = new PaletteDark()
            {
                Primary = AppState.CurrentTenant?.Whitelabel.PrimaryColor ?? "#90caf9",
                Secondary = AppState.CurrentTenant?.Whitelabel.SecondaryColor ?? "#f48fb1",
            }
        };
    }

    private void ToggleDrawer()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void OnAuthStateChanged(User? user)
    {
        _isAuthenticated = user != null;
        StateHasChanged();
    }

    [JSInvokable]
    public async Task OnWindowResize()
    {
        await SetupResponsiveDrawer();
        StateHasChanged();
    }

    public void Dispose()
    {
        AppState.OnStateChanged -= StateHasChanged;
        AuthService.OnAuthStateChanged -= OnAuthStateChanged;
        BreadcrumbService.OnBreadcrumbChanged -= StateHasChanged;
    }
}

<style>
    .app-bar {
        background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-secondary) 100%);
    }

    .main-content {
        background-color: var(--mud-palette-background-grey);
        min-height: 100vh;
    }

    .mud-drawer-mini .mud-nav-link {
        justify-content: center;
    }

    .mud-drawer-mini .mud-nav-link .mud-nav-link-text {
        display: none;
    }

    .mud-drawer:not(.mud-drawer-mini) .mud-nav-link {
        justify-content: flex-start;
    }

    .mud-drawer:not(.mud-drawer-mini) .mud-nav-link .mud-nav-link-text {
        display: block;
        margin-left: 16px;
    }
</style>
