<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
    <PackageReference Include="System.Reflection.MetadataLoadContext" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Contracts\PluginContract\PluginContract.csproj" />
    <ProjectReference Include="..\..\Contracts\EmailContract\EmailContract.csproj" />
    <ProjectReference Include="..\..\Contracts\SmsContract\SmsContract.csproj" />
    <ProjectReference Include="..\..\Contracts\PushNotificationContract\PushNotificationContract.csproj" />
  </ItemGroup>
</Project>