{"sourceFile": "src/Services/NotifyMasterApi/Features/Email/SendEmail.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751190651995, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751192262730, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -169,9 +169,9 @@\n             {\r\n                 To = req.To,\r\n                 From = req.From,\r\n                 Subject = req.Subject,\r\n-                Body = req.Body ?? req.PlainTextBody ?? req.HtmlBody ?? \"\",\r\n+                Body = req.PlainTextBody ?? req.HtmlBody ?? \"\",\r\n                 HtmlBody = req.HtmlBody,\r\n                 PlainTextBody = req.PlainTextBody,\r\n                 Cc = string.IsNullOrEmpty(req.Cc) ? null : new List<string> { req.Cc },\r\n                 Bcc = string.IsNullOrEmpty(req.Bcc) ? null : new List<string> { req.Bcc },\r\n"}], "date": 1751190651995, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing NotifyMasterApi.Gateways;\r\nusing NotificationContract.Models;\r\nusing NotifyMasterApi.Documentation;\r\nusing System.ComponentModel.DataAnnotations;\r\n\r\nnamespace NotifyMasterApi.Features.Email;\r\n\r\n/// <summary>\r\n/// Request model for sending an email message\r\n/// </summary>\r\npublic class SendEmailRequest\r\n{\r\n    /// <summary>\r\n    /// Recipient email address (required)\r\n    /// </summary>\r\n    /// <example><EMAIL></example>\r\n    [Required(ErrorMessage = \"Recipient email address is required\")]\r\n    [EmailAddress(ErrorMessage = \"Invalid email address format\")]\r\n    public string To { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Sender email address (optional - uses default if not specified)\r\n    /// </summary>\r\n    /// <example><EMAIL></example>\r\n    [EmailAddress(ErrorMessage = \"Invalid sender email address format\")]\r\n    public string From { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Email subject line (required)\r\n    /// </summary>\r\n    /// <example>Welcome to NotificationService</example>\r\n    [Required(ErrorMessage = \"Email subject is required\")]\r\n    [StringLength(200, ErrorMessage = \"Subject cannot exceed 200 characters\")]\r\n    public string Subject { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Email body content (deprecated - use HtmlBody or PlainTextBody)\r\n    /// </summary>\r\n    [Obsolete(\"Use HtmlBody or PlainTextBody instead\")]\r\n    public string? Body { get; set; }\r\n\r\n    /// <summary>\r\n    /// HTML formatted email body\r\n    /// </summary>\r\n    /// <example>&lt;h1&gt;Welcome!&lt;/h1&gt;&lt;p&gt;Thank you for joining us.&lt;/p&gt;</example>\r\n    public string? HtmlBody { get; set; }\r\n\r\n    /// <summary>\r\n    /// Plain text email body (fallback for HTML)\r\n    /// </summary>\r\n    /// <example>Welcome! Thank you for joining us.</example>\r\n    public string? PlainTextBody { get; set; }\r\n\r\n    /// <summary>\r\n    /// Carbon copy recipients (comma-separated)\r\n    /// </summary>\r\n    /// <example><EMAIL>,<EMAIL></example>\r\n    public string? Cc { get; set; }\r\n\r\n    /// <summary>\r\n    /// Blind carbon copy recipients (comma-separated)\r\n    /// </summary>\r\n    /// <example><EMAIL>,<EMAIL></example>\r\n    public string? Bcc { get; set; }\r\n\r\n    /// <summary>\r\n    /// Custom email headers\r\n    /// </summary>\r\n    /// <example>{\"X-Priority\": \"1\", \"X-Custom-Header\": \"value\"}</example>\r\n    public Dictionary<string, string>? Headers { get; set; }\r\n\r\n    /// <summary>\r\n    /// Email category for tracking and analytics\r\n    /// </summary>\r\n    /// <example>welcome, notification, marketing</example>\r\n    [StringLength(50, ErrorMessage = \"Category cannot exceed 50 characters\")]\r\n    public string? Category { get; set; }\r\n}\r\n\r\n/// <summary>\r\n/// Response model for email sending operation\r\n/// </summary>\r\npublic class SendEmailResponse\r\n{\r\n    /// <summary>\r\n    /// Indicates whether the email was sent successfully\r\n    /// </summary>\r\n    /// <example>true</example>\r\n    public bool Success { get; set; }\r\n\r\n    /// <summary>\r\n    /// Unique identifier for the sent message (available when successful)\r\n    /// </summary>\r\n    /// <example>msg_abc123def456</example>\r\n    public string? MessageId { get; set; }\r\n\r\n    /// <summary>\r\n    /// Error message if the operation failed\r\n    /// </summary>\r\n    /// <example>Invalid email address format</example>\r\n    public string? Error { get; set; }\r\n\r\n    /// <summary>\r\n    /// Timestamp when the operation was completed\r\n    /// </summary>\r\n    /// <example>2024-01-15T10:30:00Z</example>\r\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n\r\n    /// <summary>\r\n    /// Email provider used for sending\r\n    /// </summary>\r\n    /// <example>SendGrid, Mailgun, SMTP</example>\r\n    public string? Provider { get; set; }\r\n\r\n    /// <summary>\r\n    /// Additional metadata about the operation\r\n    /// </summary>\r\n    public Dictionary<string, object>? Metadata { get; set; }\r\n}\r\n\r\npublic class SendEmailEndpoint : Endpoint<SendEmailRequest, SendEmailResponse>\r\n{\r\n    private readonly IEmailGateway _emailGateway;\r\n    private readonly ILogger<SendEmailEndpoint> _logger;\r\n\r\n    public SendEmailEndpoint(IEmailGateway emailGateway, ILogger<SendEmailEndpoint> logger)\r\n    {\r\n        _emailGateway = emailGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        this.ConfigureNotificationEndpoint(\r\n            \"POST\",\r\n            \"/api/email/send\",\r\n            \"Send Email Message\",\r\n            \"Send an email message through the configured email providers.\\n\\n\" +\r\n            \"## 🎯 Features\\n\" +\r\n            \"- **Multi-Provider Support**: Automatically routes through available email providers\\n\" +\r\n            \"- **HTML & Plain Text**: Support for both HTML and plain text content\\n\" +\r\n            \"- **Advanced Recipients**: CC, BCC, and custom headers support\\n\" +\r\n            \"- **Tracking & Analytics**: Message categorization and delivery tracking\\n\" +\r\n            \"- **Validation**: Comprehensive input validation and error handling\\n\\n\" +\r\n            \"## 📋 Provider Support\\n\" +\r\n            \"- SendGrid\\n- Mailgun\\n- Amazon SES\\n- SMTP (Generic)\\n- Custom Email Plugins\\n\\n\" +\r\n            \"## ⚡ Rate Limits\\n\" +\r\n            \"- **Default**: 100 requests/minute per API key\\n\" +\r\n            \"- **Burst**: Up to 1000 requests in 10 seconds\\n\" +\r\n            \"- **Daily**: 10,000 emails per day (configurable)\\n\\n\" +\r\n            \"## 🔒 Security\\n\" +\r\n            \"- Input sanitization for XSS prevention\\n\" +\r\n            \"- Email address validation\\n\" +\r\n            \"- Content filtering for spam prevention\\n\" +\r\n            \"- Rate limiting and abuse protection\",\r\n            \"Email\",\r\n            new[] { \"Core Messaging\", \"Communication\" }\r\n        );\r\n    }\r\n\r\n    public override async Task HandleAsync(SendEmailRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            _logger.LogInformation(\"Sending email to {To}\", req.To);\r\n\r\n            var emailRequest = new EmailMessageRequest\r\n            {\r\n                To = req.To,\r\n                From = req.From,\r\n                Subject = req.Subject,\r\n                Body = req.Body ?? req.PlainTextBody ?? req.HtmlBody ?? \"\",\r\n                HtmlBody = req.HtmlBody,\r\n                PlainTextBody = req.PlainTextBody,\r\n                Cc = string.IsNullOrEmpty(req.Cc) ? null : new List<string> { req.Cc },\r\n                Bcc = string.IsNullOrEmpty(req.Bcc) ? null : new List<string> { req.Bcc },\r\n                Headers = req.Headers,\r\n                Category = req.Category\r\n            };\r\n\r\n            var result = await _emailGateway.SendAsync(emailRequest);\r\n\r\n            if (result.IsSuccess)\r\n            {\r\n                await SendOkAsync(new SendEmailResponse\r\n                {\r\n                    Success = true,\r\n                    MessageId = result.MessageId\r\n                }, ct);\r\n            }\r\n            else\r\n            {\r\n                await SendAsync(new SendEmailResponse\r\n                {\r\n                    Success = false,\r\n                    Error = result.ErrorMessage\r\n                }, 400, ct);\r\n            }\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error sending email\");\r\n            await SendAsync(new SendEmailResponse\r\n            {\r\n                Success = false,\r\n                Error = \"Internal server error\"\r\n            }, 500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetEmailProvidersEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly IEmailGateway _emailGateway;\r\n    private readonly ILogger<GetEmailProvidersEndpoint> _logger;\r\n\r\n    public GetEmailProvidersEndpoint(IEmailGateway emailGateway, ILogger<GetEmailProvidersEndpoint> logger)\r\n    {\r\n        _emailGateway = emailGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/email/providers\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get email providers\";\r\n            s.Description = \"Get list of available email providers\";\r\n            s.Responses[200] = \"Providers retrieved successfully\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Email\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var providers = await _emailGateway.GetProvidersAsync();\r\n            await SendOkAsync(providers, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting email providers\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class TestEmailProviderRequest\r\n{\r\n    public string Provider { get; set; } = string.Empty;\r\n    public string? TestEmail { get; set; }\r\n}\r\n\r\npublic class TestEmailProviderEndpoint : Endpoint<TestEmailProviderRequest, object>\r\n{\r\n    private readonly IEmailGateway _emailGateway;\r\n    private readonly ILogger<TestEmailProviderEndpoint> _logger;\r\n\r\n    public TestEmailProviderEndpoint(IEmailGateway emailGateway, ILogger<TestEmailProviderEndpoint> logger)\r\n    {\r\n        _emailGateway = emailGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/email/providers/{provider}/test\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Test email provider\";\r\n            s.Description = \"Test a specific email provider\";\r\n            s.Responses[200] = \"Provider test completed\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Email\");\r\n    }\r\n\r\n    public override async Task HandleAsync(TestEmailProviderRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var result = await _emailGateway.TestProviderAsync(req.Provider, req.TestEmail);\r\n            await SendOkAsync(result, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error testing email provider {Provider}\", req.Provider);\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n"}]}