{"sourceFile": "src/Core/PluginCore/Base/RawGatewayResponse.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751198953762, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751198953762, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic class RawGatewayResponse\r\n{\r\n    public bool IsSuccess { get; set; }\r\n    public string? Response { get; set; }\r\n    public string? ErrorMessage { get; set; }\r\n    public Dictionary<string, object>? ResponseData { get; set; }\r\n    public Dictionary<string, object>? Metadata { get; set; }\r\n    public Dictionary<string, object>? Headers { get; set; }\r\n    public Dictionary<string, object>? QueryParameters { get; set; }\r\n    public Dictionary<string, object>? FormParameters { get; set; }\r\n    public Dictionary<string, object>? Cookies { get; set; }\r\n    public Dictionary<string, object>? Files { get; set; }\r\n    public Dictionary<string, object>? Session { get; set; }\r\n    public Dictionary<string, object>? ServerVariables { get; set; }\r\n    public Dictionary<string, object>? ClientVariables { get; set; }\r\n    public Dictionary<string, object>? CustomVariables { get; set; }\r\n    public Dictionary<string, object>? RequestData { get; set; }\r\n    public Dictionary<string, object>? ErrorData { get; set; }\r\n    public Dictionary<string, object>? LogData { get; set; }\r\n    public Dictionary<string, object>? CustomData { get; set; }\r\n\r\n    public static explicit operator RawGatewayResponse(NotificationResponse response)\r\n        => new RawGatewayResponse\r\n        {\r\n            IsSuccess = response.IsSuccess,\r\n            Response = response.Response,\r\n            ErrorMessage = response.ErrorMessage,\r\n            ResponseData = response.ResponseData\r\n        };\r\n}\r\n"}]}