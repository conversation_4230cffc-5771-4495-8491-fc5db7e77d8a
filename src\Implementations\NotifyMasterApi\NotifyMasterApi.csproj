<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <LangVersion>latest</LangVersion>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
        <AssemblyName>NotifyMasterApi</AssemblyName>
        <RootNamespace>NotifyMasterApi</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Hangfire.AspNetCore" Version="1.8.14" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.1" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
        <PackageReference Include="Scalar.AspNetCore" Version="1.2.42" />
        <PackageReference Include="FastEndpoints" Version="5.30.0" />
        <PackageReference Include="Hangfire.Core" Version="1.8.14" />
        <PackageReference Include="Hangfire.SqlServer" Version="1.8.14" />
        <PackageReference Include="Hangfire.InMemory" Version="0.10.4" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
        <PackageReference Include="System.Text.Json" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.0" />
        <PackageReference Include="Azure.Storage.Blobs" Version="12.21.2" />
        <PackageReference Include="AWSSDK.S3" Version="3.7.402.8" />
        <PackageReference Include="MediatR" Version="12.4.1" />
    </ItemGroup>



    <ItemGroup>
      <ProjectReference Include="..\..\Core\PluginCore\PluginCore.csproj" />
      <ProjectReference Include="..\..\Core\NotifyMaster.Database\NotifyMaster.Database.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Presentation" />
    </ItemGroup>

</Project>
