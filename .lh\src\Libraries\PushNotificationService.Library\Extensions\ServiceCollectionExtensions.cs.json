{"sourceFile": "src/Libraries/PushNotificationService.Library/Extensions/ServiceCollectionExtensions.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751193564188, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751193564186, "name": "Commit-0", "content": "using PushNotificationService.Library.Interfaces;\r\nusing PushNotificationService.Library.Services;\r\nusing Microsoft.Extensions.DependencyInjection;\r\n\r\nnamespace PushNotificationService.Library.Extensions;\r\n\r\npublic static class ServiceCollectionExtensions\r\n{\r\n    \r\n}\r\n"}]}