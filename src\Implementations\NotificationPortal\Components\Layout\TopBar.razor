@inject IAppStateService AppState
@inject IAuthService AuthService
@inject ITenantService TenantService
@inject INotificationService NotificationService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

<!-- App Logo/Title -->
<div class="d-flex align-center">
    @if (!string.IsNullOrEmpty(_currentTenant?.Whitelabel.LogoUrl))
    {
        <MudImage Src="@_currentTenant.Whitelabel.LogoUrl"
                  Alt="@_currentTenant.Whitelabel.AppTitle"
                  Height="32"
                  Class="mr-3" />
    }
    else
    {
        <MudIcon Icon="@Icons.Material.Filled.Notifications"
                 Size="Size.Large"
                 Color="Color.Inherit"
                 Class="mr-2" />
    }

    <MudText Typo="Typo.h6" Color="Color.Inherit" Class="app-title">
        @(_currentTenant?.Whitelabel.AppTitle ?? "NotificationService")
    </MudText>
</div>

<MudSpacer />

<!-- Tenant Switcher (only show if user has access to multiple tenants) -->
@if (_tenants.Count > 1)
{
    <MudMenu Icon="@Icons.Material.Filled.Business"
             Color="Color.Inherit"
             Direction="Direction.Bottom"
             OffsetY="true"
             Class="tenant-switcher">
        <ActivatorContent>
            <MudButton Variant="Variant.Text"
                       Color="Color.Inherit"
                       StartIcon="@Icons.Material.Filled.Business"
                       Class="tenant-button">
                <div class="d-flex flex-column align-start">
                    <MudText Typo="Typo.body2">@(_currentTenant?.Name ?? "Select Tenant")</MudText>
                    <MudText Typo="Typo.caption" Class="tenant-id">@(_currentTenant?.Id ?? "")</MudText>
                </div>
                <MudIcon Icon="@Icons.Material.Filled.ArrowDropDown" Class="ml-1" />
            </MudButton>
        </ActivatorContent>
        <ChildContent>
            <div class="tenant-menu-header pa-3">
                <MudText Typo="Typo.subtitle2">Switch Tenant</MudText>
                <MudText Typo="Typo.caption" Class="mud-text-secondary">
                    Select a tenant to manage
                </MudText>
            </div>
            <MudDivider />
            @foreach (var tenant in _tenants)
            {
                <MudMenuItem OnClick="@(() => SwitchTenant(tenant.Id))" Class="tenant-menu-item">
                    <div class="d-flex align-center w-100">
                        <MudAvatar Size="Size.Small" Class="mr-3">
                            @if (!string.IsNullOrEmpty(tenant.Whitelabel.LogoUrl))
                            {
                                <MudImage Src="@tenant.Whitelabel.LogoUrl" />
                            }
                            else
                            {
                                <MudIcon Icon="@Icons.Material.Filled.Business" />
                            }
                        </MudAvatar>
                        <div class="flex-grow-1">
                            <MudText Typo="Typo.body2">@tenant.Name</MudText>
                            <MudText Typo="Typo.caption" Class="mud-text-secondary">@tenant.Id</MudText>
                        </div>
                        @if (tenant.Id == AppState.CurrentTenantId)
                        {
                            <MudIcon Icon="@Icons.Material.Filled.Check" Color="Color.Success" Size="Size.Small" />
                        }
                    </div>
                </MudMenuItem>
            }
        </ChildContent>
    </MudMenu>
}

<!-- Theme Toggle -->
<MudTooltip Text="@(_isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode")">
    <MudIconButton Icon="@(_isDarkMode ? Icons.Material.Filled.LightMode : Icons.Material.Filled.DarkMode)"
                   Color="Color.Inherit"
                   OnClick="ToggleTheme"
                   Class="theme-toggle" />
</MudTooltip>

<!-- Notifications Bell -->
<MudMenu Icon="@Icons.Material.Filled.Notifications"
         Color="Color.Inherit"
         Direction="Direction.Bottom"
         OffsetY="true"
         Class="notification-menu">
    <ActivatorContent>
        <MudBadge Content="_unreadNotifications"
                  Color="Color.Error"
                  Overlap="true"
                  Visible="_unreadNotifications > 0"
                  Class="notification-badge">
            <MudIconButton Icon="@Icons.Material.Filled.Notifications"
                          Color="Color.Inherit"
                          Class="notification-button" />
        </MudBadge>
    </ActivatorContent>
    <ChildContent>
        <div class="notification-menu-header pa-3">
            <div class="d-flex justify-space-between align-center">
                <MudText Typo="Typo.subtitle2">Notifications</MudText>
                @if (_unreadNotifications > 0)
                {
                    <MudButton Size="Size.Small"
                              Variant="Variant.Text"
                              Color="Color.Primary"
                              OnClick="MarkAllAsRead">
                        Mark all read
                    </MudButton>
                }
            </div>
        </div>
        <MudDivider />
        <div class="notification-list" style="max-height: 400px; overflow-y: auto;">
            @if (_notifications.Any())
            {
                @foreach (var notification in _notifications.Take(10))
                {
                    <MudMenuItem OnClick="@(() => HandleNotificationClick(notification))"
                                Class="notification-item @(notification.IsRead ? "read" : "unread")">
                        <div class="d-flex align-start w-100">
                            <MudIcon Icon="@GetNotificationIcon(notification.Type)"
                                    Color="@GetNotificationColor(notification.Severity)"
                                    Class="mr-3 mt-1"
                                    Size="Size.Small" />
                            <div class="flex-grow-1">
                                <MudText Typo="Typo.body2" Class="notification-title">
                                    @notification.Title
                                </MudText>
                                <MudText Typo="Typo.caption" Class="mud-text-secondary notification-message">
                                    @notification.Message
                                </MudText>
                                <MudText Typo="Typo.caption" Class="mud-text-secondary notification-time">
                                    @FormatNotificationTime(notification.Timestamp)
                                </MudText>
                            </div>
                            @if (!notification.IsRead)
                            {
                                <div class="unread-indicator"></div>
                            }
                        </div>
                    </MudMenuItem>
                }
            }
            else
            {
                <div class="pa-4 text-center">
                    <MudIcon Icon="@Icons.Material.Filled.NotificationsNone"
                            Size="Size.Large"
                            Class="mud-text-secondary mb-2" />
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">
                        No notifications
                    </MudText>
                </div>
            }
        </div>
        @if (_notifications.Count > 10)
        {
            <MudDivider />
            <div class="pa-2 text-center">
                <MudButton Variant="Variant.Text"
                          Color="Color.Primary"
                          FullWidth="true"
                          OnClick="@(() => Navigation.NavigateTo("/events"))">
                    View All Notifications
                </MudButton>
            </div>
        }
    </ChildContent>
</MudMenu>

<!-- User Menu -->
<MudMenu Icon="@Icons.Material.Filled.AccountCircle" Color="Color.Inherit" Direction="Direction.Bottom" OffsetY="true">
    <ActivatorContent>
        <MudButton Variant="Variant.Text" Color="Color.Inherit" StartIcon="@Icons.Material.Filled.AccountCircle">
            @(_currentUser?.FullName ?? "User")
        </MudButton>
    </ActivatorContent>
    <ChildContent>
        <div class="pa-4" style="min-width: 200px;">
            <MudText Typo="Typo.subtitle2">@_currentUser?.FullName</MudText>
            <MudText Typo="Typo.body2" Class="mud-text-secondary">@_currentUser?.Email</MudText>
            <MudDivider Class="my-2" />
        </div>
        <MudMenuItem Icon="@Icons.Material.Filled.Person" OnClick="@(() => Navigation.NavigateTo("/profile"))">
            Profile
        </MudMenuItem>
        <MudMenuItem Icon="@Icons.Material.Filled.Settings" OnClick="@(() => Navigation.NavigateTo("/settings"))">
            Settings
        </MudMenuItem>
        <MudDivider />
        <MudMenuItem Icon="@Icons.Material.Filled.Logout" OnClick="Logout">
            Logout
        </MudMenuItem>
    </ChildContent>
</MudMenu>

@code {
    private User? _currentUser;
    private Tenant? _currentTenant;
    private List<Tenant> _tenants = new();
    private bool _isDarkMode;
    private int _unreadNotifications = 3; // Simulated

    protected override async Task OnInitializedAsync()
    {
        _currentUser = await AuthService.GetCurrentUserAsync();
        _currentTenant = AppState.CurrentTenant;
        _isDarkMode = AppState.IsDarkMode;
        
        await LoadTenants();

        AppState.OnStateChanged += OnStateChanged;
        AuthService.OnAuthStateChanged += OnAuthStateChanged;
    }

    private async Task LoadTenants()
    {
        try
        {
            _tenants = await TenantService.GetTenantsAsync();
        }
        catch (Exception ex)
        {
            // Handle error - could show a snackbar
            Console.WriteLine($"Failed to load tenants: {ex.Message}");
        }
    }

    private async Task SwitchTenant(string tenantId)
    {
        await AppState.SetTenantAsync(tenantId);
        _currentTenant = AppState.CurrentTenant;
        StateHasChanged();
    }

    private async Task ToggleTheme()
    {
        _isDarkMode = !_isDarkMode;
        await AppState.SetThemeAsync(_isDarkMode);
    }

    private void ShowNotifications()
    {
        Navigation.NavigateTo("/events");
    }

    private async Task Logout()
    {
        await AuthService.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    private void OnStateChanged()
    {
        _currentTenant = AppState.CurrentTenant;
        _isDarkMode = AppState.IsDarkMode;
        StateHasChanged();
    }

    private void OnAuthStateChanged(User? user)
    {
        _currentUser = user;
        StateHasChanged();
    }

    public void Dispose()
    {
        AppState.OnStateChanged -= OnStateChanged;
        AuthService.OnAuthStateChanged -= OnAuthStateChanged;
    }
}
