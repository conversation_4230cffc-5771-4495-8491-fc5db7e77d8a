{"sourceFile": "src/Core/PluginCore/Base/MessageScheduleResult.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751199077251, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751199077251, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic class MessageScheduleResult\r\n{\r\n    public string? ScheduledMessageId { get; set; }\r\n    public string? ScheduledTime { get; set; }\r\n    public string? CronExpression { get; set; }\r\n    public string? Status { get; set; }\r\n    public string? ErrorMessage { get; set; }\r\n    public Dictionary<string, object>? ResponseData { get; set; }\r\n    public Dictionary<string, object>? Metadata { get; set; }\r\n    public Dictionary<string, object>? Headers { get; set; }\r\n    public Dictionary<string, object>? QueryParameters { get; set; }\r\n    public Dictionary<string, object>? FormParameters { get; set; }\r\n    public Dictionary<string, object>? Cookies { get; set; }\r\n    public Dictionary<string, object>? Files { get; set; }\r\n    public Dictionary<string, object>? Session { get; set; }\r\n\r\n    public static explicit operator MessageScheduleResult(NotificationResponse response)\r\n        => new MessageScheduleResult\r\n        {\r\n            ScheduledMessageId = response.MessageId,\r\n            Status = response.Status,\r\n            ErrorMessage = response.ErrorMessage,\r\n            ResponseData = response.ResponseData\r\n        };\r\n\r\n        public static explicit operator MessageScheduleResult(OperationResult operationResult)\r\n        => new MessageScheduleResult\r\n        {\r\n            Status = operationResult.IsSuccess ? \"scheduled\" : \"failed\",\r\n            ErrorMessage = operationResult.ErrorMessage\r\n        };\r\n\r\n\r\n\r\n}\r\n"}]}