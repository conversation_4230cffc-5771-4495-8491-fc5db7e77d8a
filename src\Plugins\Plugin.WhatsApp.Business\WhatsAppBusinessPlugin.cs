using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Base;
using System.Text.Json;
using System.Text;
using System.Diagnostics;

namespace Plugin.WhatsApp.Business;

public class WhatsAppBusinessPlugin : BaseNotificationPlugin
{
    private readonly ILogger<WhatsAppBusinessPlugin> _logger;
    private readonly HttpClient _httpClient;
    private bool _isInitialized = false;
    private string? _accessToken;
    private string? _phoneNumberId;
    private string? _businessAccountId;

    public WhatsAppBusinessPlugin(ILogger<WhatsAppBusinessPlugin> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    public override PluginInfo PluginInfo => new(
        Name: "WhatsApp Business",
        Version: "1.0.0",
        Description: "WhatsApp Business API plugin for sending messages via Meta's WhatsApp Business Platform",
        Type: PluginContract.Enums.PluginType.Messaging,
        Author: "NotificationService Team",
        IsEnabled: true
    );

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);

        _accessToken = configuration["WhatsApp:AccessToken"];
        _phoneNumberId = configuration["WhatsApp:PhoneNumberId"];
        _businessAccountId = configuration["WhatsApp:BusinessAccountId"];

        if (string.IsNullOrEmpty(_accessToken) || string.IsNullOrEmpty(_phoneNumberId))
        {
            throw new InvalidOperationException("WhatsApp Access Token and Phone Number ID are required");
        }

        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_accessToken}");
        _httpClient.DefaultRequestHeaders.Add("Content-Type", "application/json");

        _isInitialized = true;
        _logger.LogInformation("WhatsApp Business plugin initialized successfully");
    }

    public override async Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (!_isInitialized)
                throw new InvalidOperationException("Plugin not initialized");

            if (request is not WhatsAppRequest whatsAppRequest)
                throw new ArgumentException("Invalid request type for WhatsApp plugin");

            var messagePayload = CreateMessagePayload(whatsAppRequest);
            var json = JsonSerializer.Serialize(messagePayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var url = $"https://graph.facebook.com/v18.0/{_phoneNumberId}/messages";
            var response = await _httpClient.PostAsync(url, content, cancellationToken);

            stopwatch.Stop();
            var responseTimeMs = stopwatch.Elapsed.TotalMilliseconds;

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
                var messageId = responseData.GetProperty("messages")[0].GetProperty("id").GetString();

                TrackRequest(true, responseTimeMs, json.Length);
                _logger.LogInformation("WhatsApp message sent successfully. MessageId: {MessageId}", messageId);

                return new WhatsAppResponse(true, MessageId: messageId);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                TrackRequest(false, responseTimeMs, 0, response.StatusCode.ToString());
                _logger.LogError("WhatsApp API error: {StatusCode} - {Error}", response.StatusCode, errorContent);

                return new WhatsAppResponse(false, ErrorMessage: $"WhatsApp API error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            TrackRequest(false, stopwatch.Elapsed.TotalMilliseconds, 0, ex.GetType().Name);
            _logger.LogError(ex, "Error sending WhatsApp message");
            return new WhatsAppResponse(false, ErrorMessage: ex.Message);
        }
    }

    public override async Task<bool> HealthCheckAsync()
    {
        try
        {
            if (!_isInitialized) return false;

            var url = $"https://graph.facebook.com/v18.0/{_businessAccountId}";
            var response = await _httpClient.GetAsync(url);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    protected override string GetProviderName() => "Meta WhatsApp Business";

    protected override List<ConfigurationField> GetConfigurationFields()
    {
        return new List<ConfigurationField>
        {
            new("AccessToken", "string", "WhatsApp Business API Access Token", true, true),
            new("PhoneNumberId", "string", "WhatsApp Business Phone Number ID", true, false),
            new("BusinessAccountId", "string", "WhatsApp Business Account ID", true, false),
            new("WebhookVerifyToken", "string", "Webhook verification token", false, true)
        };
    }

    protected override Dictionary<string, object> GetCustomMetrics()
    {
        return new Dictionary<string, object>
        {
            ["provider"] = "Meta WhatsApp Business",
            ["api_version"] = "v18.0",
            ["phone_number_id"] = _phoneNumberId ?? "not_configured",
            ["last_health_check"] = DateTime.UtcNow
        };
    }

    private object CreateMessagePayload(WhatsAppRequest request)
    {
        var payload = new
        {
            messaging_product = "whatsapp",
            to = request.PhoneNumber,
            type = request.MessageType.ToLower(),
            text = request.MessageType.ToLower() == "text" ? new { body = request.Message } : null,
            template = request.MessageType.ToLower() == "template" ? new
            {
                name = request.TemplateName,
                language = new { code = request.LanguageCode ?? "en" },
                components = request.TemplateParameters
            } : null
        };

        return payload;
    }
}

// WhatsApp-specific request and response models
public record WhatsAppRequest(
    string PhoneNumber,
    string Message,
    string MessageType = "text",
    string? TemplateName = null,
    string? LanguageCode = null,
    object[]? TemplateParameters = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Message, Metadata);

public record WhatsAppResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
