{"sourceFile": "src/Core/PluginCore/Base/IGatewayMessagePlugin.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751202246457, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751202254486, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,9 +6,9 @@\n \r\n /// <summary>\r\n /// Handles the core functionality of sending messages through a gateway.\r\n /// </summary>\r\n-public interface IGatewayMessagePluginType : IPluginType\r\n+public interface IGatewayMessagePluginType\r\n {\r\n     /// <summary>\r\n     /// Sends a single outbound message through the gateway.\r\n     /// </summary>\r\n"}], "date": 1751202246457, "name": "Commit-0", "content": "using PluginCore.Models;\r\nusing NotificationService.Core.PluginCore.Base;\r\nusing System.ComponentModel.DataAnnotations;\r\n\r\nnamespace PluginCore.Base;\r\n\r\n/// <summary>\r\n/// Handles the core functionality of sending messages through a gateway.\r\n/// </summary>\r\npublic interface IGatewayMessagePluginType : IPluginType\r\n{\r\n    /// <summary>\r\n    /// Sends a single outbound message through the gateway.\r\n    /// </summary>\r\n    Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Sends multiple messages in a single, optimized operation.\r\n    /// </summary>\r\n    Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Sends a message using a registered template and dynamic data.\r\n    /// </summary>\r\n    Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Schedules a message for future delivery.\r\n    /// </summary>\r\n    Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Cancels a previously scheduled message.\r\n    /// </summary>\r\n    Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves the real-time status of a specific message.\r\n    /// </summary>\r\n    Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves a provider-issued delivery receipt if available.\r\n    /// </summary>\r\n    Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Validates a message payload without sending it.\r\n    /// </summary>\r\n    Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);\r\n}"}]}