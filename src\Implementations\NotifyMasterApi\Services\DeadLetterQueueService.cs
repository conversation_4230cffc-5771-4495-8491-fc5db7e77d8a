using Microsoft.Extensions.Logging;
using NotifyMasterApi.Infrastructure;
using System.Text.Json;

namespace NotifyMasterApi.Services;

public interface IDeadLetterQueueService
{
    Task AddToDeadLetterQueueAsync(DeadLetterMessage message);
    Task<List<DeadLetterMessage>> GetDeadLetterMessagesAsync(string? tenantId = null, string? channel = null);
    Task<DeadLetterMessage?> GetDeadLetterMessageAsync(string messageId);
    Task<bool> ReprocessMessageAsync(string messageId);
    Task<bool> ArchiveMessageAsync(string messageId);
    Task<bool> DeleteMessageAsync(string messageId);
    Task<int> GetDeadLetterCountAsync(string? tenantId = null);
}

public class DeadLetterQueueService : IDeadLetterQueueService
{
    private readonly ILogger<DeadLetterQueueService> _logger;
    private readonly IEventStreamService _eventStreamService;
    private readonly Dictionary<string, DeadLetterMessage> _deadLetterStore = new();

    public DeadLetterQueueService(
        ILogger<DeadLetterQueueService> logger,
        IEventStreamService eventStreamService)
    {
        _logger = logger;
        _eventStreamService = eventStreamService;
    }

    public async Task AddToDeadLetterQueueAsync(DeadLetterMessage message)
    {
        message.Id = Guid.NewGuid().ToString();
        message.FirstFailedAt = DateTime.UtcNow;
        message.LastFailedAt = DateTime.UtcNow;
        message.Status = "Failed";

        _deadLetterStore[message.Id] = message;

        _logger.LogWarning("Added message {MessageId} to dead letter queue for tenant {TenantId}, channel {Channel}", 
            message.OriginalMessageId, message.TenantId, message.Channel);

        // Publish event
        await _eventStreamService.PublishEventAsync(new SystemEvent
        {
            TenantId = message.TenantId,
            Type = "dead_letter",
            Channel = message.Channel,
            MessageId = message.OriginalMessageId,
            Status = "failed_permanently",
            Error = message.LastError,
            Data = new Dictionary<string, object>
            {
                ["failure_count"] = message.FailureCount,
                ["dead_letter_id"] = message.Id
            }
        });
    }

    public async Task<List<DeadLetterMessage>> GetDeadLetterMessagesAsync(string? tenantId = null, string? channel = null)
    {
        var messages = _deadLetterStore.Values.AsEnumerable();

        if (!string.IsNullOrEmpty(tenantId))
        {
            messages = messages.Where(m => m.TenantId == tenantId);
        }

        if (!string.IsNullOrEmpty(channel))
        {
            messages = messages.Where(m => m.Channel.Equals(channel, StringComparison.OrdinalIgnoreCase));
        }

        return messages.OrderByDescending(m => m.LastFailedAt).ToList();
    }

    public async Task<DeadLetterMessage?> GetDeadLetterMessageAsync(string messageId)
    {
        _deadLetterStore.TryGetValue(messageId, out var message);
        return message;
    }

    public async Task<bool> ReprocessMessageAsync(string messageId)
    {
        if (!_deadLetterStore.TryGetValue(messageId, out var message))
        {
            return false;
        }

        if (message.Status != "Failed")
        {
            return false; // Already being processed or archived
        }

        message.Status = "Reprocessing";
        
        _logger.LogInformation("Reprocessing dead letter message {MessageId} for tenant {TenantId}", 
            messageId, message.TenantId);

        try
        {
            // In a real implementation, this would re-queue the message for processing
            // For now, we'll simulate successful reprocessing
            await Task.Delay(100); // Simulate processing time

            // Remove from dead letter queue on successful reprocessing
            _deadLetterStore.Remove(messageId);

            // Publish success event
            await _eventStreamService.PublishEventAsync(new SystemEvent
            {
                TenantId = message.TenantId,
                Type = "reprocessed",
                Channel = message.Channel,
                MessageId = message.OriginalMessageId,
                Status = "reprocessed_successfully",
                Data = new Dictionary<string, object>
                {
                    ["dead_letter_id"] = messageId,
                    ["reprocessed_at"] = DateTime.UtcNow
                }
            });

            return true;
        }
        catch (Exception ex)
        {
            message.Status = "Failed";
            message.LastError = ex.Message;
            message.FailureCount++;
            message.LastFailedAt = DateTime.UtcNow;

            _logger.LogError(ex, "Failed to reprocess dead letter message {MessageId}", messageId);
            return false;
        }
    }

    public async Task<bool> ArchiveMessageAsync(string messageId)
    {
        if (!_deadLetterStore.TryGetValue(messageId, out var message))
        {
            return false;
        }

        message.Status = "Archived";
        
        _logger.LogInformation("Archived dead letter message {MessageId} for tenant {TenantId}", 
            messageId, message.TenantId);

        // Publish archive event
        await _eventStreamService.PublishEventAsync(new SystemEvent
        {
            TenantId = message.TenantId,
            Type = "archived",
            Channel = message.Channel,
            MessageId = message.OriginalMessageId,
            Status = "archived",
            Data = new Dictionary<string, object>
            {
                ["dead_letter_id"] = messageId,
                ["archived_at"] = DateTime.UtcNow
            }
        });

        return true;
    }

    public async Task<bool> DeleteMessageAsync(string messageId)
    {
        if (!_deadLetterStore.ContainsKey(messageId))
        {
            return false;
        }

        var message = _deadLetterStore[messageId];
        _deadLetterStore.Remove(messageId);

        _logger.LogInformation("Deleted dead letter message {MessageId} for tenant {TenantId}", 
            messageId, message.TenantId);

        // Publish deletion event
        await _eventStreamService.PublishEventAsync(new SystemEvent
        {
            TenantId = message.TenantId,
            Type = "deleted",
            Channel = message.Channel,
            MessageId = message.OriginalMessageId,
            Status = "deleted",
            Data = new Dictionary<string, object>
            {
                ["dead_letter_id"] = messageId,
                ["deleted_at"] = DateTime.UtcNow
            }
        });

        return true;
    }

    public async Task<int> GetDeadLetterCountAsync(string? tenantId = null)
    {
        var messages = _deadLetterStore.Values.AsEnumerable();

        if (!string.IsNullOrEmpty(tenantId))
        {
            messages = messages.Where(m => m.TenantId == tenantId);
        }

        return messages.Count(m => m.Status == "Failed");
    }
}

public interface IValidationService
{
    Task<bool> ValidateMessageAsync(string tenantId, string channel, object message);
    Task<List<ValidationRule>> GetValidationRulesAsync(string tenantId, string? channel = null);
    Task<ValidationRule?> CreateValidationRuleAsync(ValidationRule rule);
    Task<ValidationRule?> UpdateValidationRuleAsync(string ruleId, ValidationRule rule);
    Task<bool> DeleteValidationRuleAsync(string ruleId);
    Task<bool> TestValidationRuleAsync(string ruleId, object testMessage);
}

public class ValidationService : IValidationService
{
    private readonly ILogger<ValidationService> _logger;
    private readonly Dictionary<string, ValidationRule> _rulesStore = new();

    public ValidationService(ILogger<ValidationService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> ValidateMessageAsync(string tenantId, string channel, object message)
    {
        var rules = await GetValidationRulesAsync(tenantId, channel);
        var activeRules = rules.Where(r => r.IsActive).OrderBy(r => r.Priority);

        foreach (var rule in activeRules)
        {
            var isValid = await ApplyValidationRuleAsync(rule, message);
            if (!isValid)
            {
                _logger.LogWarning("Message validation failed for rule {RuleId} ({RuleName}) in tenant {TenantId}", 
                    rule.Id, rule.Name, tenantId);
                return false;
            }
        }

        return true;
    }

    public async Task<List<ValidationRule>> GetValidationRulesAsync(string tenantId, string? channel = null)
    {
        var rules = _rulesStore.Values.Where(r => r.TenantId == tenantId);

        if (!string.IsNullOrEmpty(channel))
        {
            rules = rules.Where(r => r.Channel.Equals(channel, StringComparison.OrdinalIgnoreCase) || 
                                   r.Channel.Equals("*", StringComparison.OrdinalIgnoreCase));
        }

        return rules.OrderBy(r => r.Priority).ToList();
    }

    public async Task<ValidationRule?> CreateValidationRuleAsync(ValidationRule rule)
    {
        rule.Id = Guid.NewGuid().ToString();
        rule.CreatedAt = DateTime.UtcNow;

        _rulesStore[rule.Id] = rule;

        _logger.LogInformation("Created validation rule {RuleId} ({RuleName}) for tenant {TenantId}", 
            rule.Id, rule.Name, rule.TenantId);

        return rule;
    }

    public async Task<ValidationRule?> UpdateValidationRuleAsync(string ruleId, ValidationRule rule)
    {
        if (!_rulesStore.ContainsKey(ruleId))
        {
            return null;
        }

        rule.Id = ruleId;
        _rulesStore[ruleId] = rule;

        _logger.LogInformation("Updated validation rule {RuleId} for tenant {TenantId}", 
            ruleId, rule.TenantId);

        return rule;
    }

    public async Task<bool> DeleteValidationRuleAsync(string ruleId)
    {
        if (!_rulesStore.ContainsKey(ruleId))
        {
            return false;
        }

        var rule = _rulesStore[ruleId];
        _rulesStore.Remove(ruleId);

        _logger.LogInformation("Deleted validation rule {RuleId} for tenant {TenantId}", 
            ruleId, rule.TenantId);

        return true;
    }

    public async Task<bool> TestValidationRuleAsync(string ruleId, object testMessage)
    {
        if (!_rulesStore.TryGetValue(ruleId, out var rule))
        {
            return false;
        }

        return await ApplyValidationRuleAsync(rule, testMessage);
    }

    private async Task<bool> ApplyValidationRuleAsync(ValidationRule rule, object message)
    {
        try
        {
            switch (rule.RuleType.ToLower())
            {
                case "schema":
                    return await ValidateJsonSchemaAsync(rule.RuleDefinition, message);
                
                case "regex":
                    return await ValidateRegexAsync(rule.RuleDefinition, message);
                
                case "custom":
                    return await ValidateCustomRuleAsync(rule.RuleDefinition, message);
                
                default:
                    _logger.LogWarning("Unknown validation rule type: {RuleType}", rule.RuleType);
                    return true; // Default to valid for unknown rule types
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying validation rule {RuleId}", rule.Id);
            return false;
        }
    }

    private async Task<bool> ValidateJsonSchemaAsync(string schema, object message)
    {
        // In a real implementation, this would use a JSON schema validator
        // For now, we'll do basic validation
        try
        {
            var messageJson = JsonSerializer.Serialize(message);
            return !string.IsNullOrEmpty(messageJson);
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> ValidateRegexAsync(string pattern, object message)
    {
        try
        {
            var messageString = message.ToString() ?? "";
            return System.Text.RegularExpressions.Regex.IsMatch(messageString, pattern);
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> ValidateCustomRuleAsync(string ruleDefinition, object message)
    {
        // In a real implementation, this would execute custom validation logic
        // For now, we'll always return true
        return true;
    }
}
