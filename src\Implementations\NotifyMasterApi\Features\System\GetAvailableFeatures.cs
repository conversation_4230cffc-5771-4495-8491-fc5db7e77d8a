using FastEndpoints;
using NotifyMasterApi.Services;
using PluginContract.Enums;

namespace NotifyMasterApi.Features.System;

public class GetAvailableFeaturesRequest
{
    // Empty request - no parameters needed
}

public class GetAvailableFeaturesResponse
{
    public List<string> AvailableFeatures { get; set; } = new();
    public Dictionary<string, List<string>> PluginsByType { get; set; } = new();
    public FeatureFlags FeatureFlags { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class FeatureFlags
{
    public bool EmailEnabled { get; set; }
    public bool SmsEnabled { get; set; }
    public bool PushEnabled { get; set; }
    public bool MessagingEnabled { get; set; }
    public bool PluginManagementEnabled { get; set; } = true;
    public bool HealthMonitoringEnabled { get; set; } = true;
}

public class GetAvailableFeaturesEndpoint : Endpoint<GetAvailableFeaturesRequest, GetAvailableFeaturesResponse>
{
    private readonly IPluginDetectionService _pluginDetectionService;
    private readonly ILogger<GetAvailableFeaturesEndpoint> _logger;

    public GetAvailableFeaturesEndpoint(
        IPluginDetectionService pluginDetectionService, 
        ILogger<GetAvailableFeaturesEndpoint> logger)
    {
        _pluginDetectionService = pluginDetectionService;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/system/features");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get available features";
            s.Description = "Retrieve a list of available features based on loaded and enabled plugins";
            s.Responses[200] = "Available features retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("System");
    }

    public override async Task HandleAsync(GetAvailableFeaturesRequest req, CancellationToken ct)
    {
        try
        {
            var availableFeatures = await _pluginDetectionService.GetAvailableFeaturesAsync();
            var pluginsByType = await _pluginDetectionService.GetAvailablePluginsByTypeAsync();

            // Convert to string dictionary for response
            var pluginsByTypeString = pluginsByType.ToDictionary(
                kvp => kvp.Key.ToString(),
                kvp => kvp.Value
            );

            var featureFlags = new FeatureFlags
            {
                EmailEnabled = await _pluginDetectionService.IsFeatureAvailableAsync(PluginType.Email),
                SmsEnabled = await _pluginDetectionService.IsFeatureAvailableAsync(PluginType.Sms),
                PushEnabled = await _pluginDetectionService.IsFeatureAvailableAsync(PluginType.PushNotification),
                MessagingEnabled = await _pluginDetectionService.IsFeatureAvailableAsync(PluginType.Messaging)
            };

            await SendOkAsync(new GetAvailableFeaturesResponse
            {
                AvailableFeatures = availableFeatures,
                PluginsByType = pluginsByTypeString,
                FeatureFlags = featureFlags
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving available features");
            await SendErrorsAsync(500, ct);
        }
    }
}
