















<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Twilio" Version="7.11.3" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.6" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Contracts\PluginContract\PluginContract.csproj" />
        <ProjectReference Include="..\..\Core\PluginCore\PluginCore.csproj" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="manifest.json" />
    </ItemGroup>

</Project>
