using NotifyMasterApi.Interfaces;
using PluginContract.Enums;

namespace NotifyMasterApi.Services;

public interface IPluginDetectionService
{
    Task<bool> IsFeatureAvailableAsync(PluginType pluginType);
    Task<List<string>> GetAvailableFeaturesAsync();
    Task<Dictionary<PluginType, List<string>>> GetAvailablePluginsByTypeAsync();
    Task<bool> HasAnyPluginsAsync();
}

public class PluginDetectionService : IPluginDetectionService
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<PluginDetectionService> _logger;

    public PluginDetectionService(IPluginManager pluginManager, ILogger<PluginDetectionService> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<bool> IsFeatureAvailableAsync(PluginType pluginType)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            return plugins.Any(p =>
                p.IsEnabled &&
                p.Type == pluginType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking feature availability for {PluginType}", pluginType);
            return false;
        }
    }

    public async Task<List<string>> GetAvailableFeaturesAsync()
    {
        try
        {
            var features = new List<string>();
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var enabledPlugins = plugins.Where(p => p.IsEnabled);

            foreach (var pluginType in Enum.GetValues<PluginType>())
            {
                if (enabledPlugins.Any(p => p.Type == pluginType))
                {
                    features.Add(pluginType.ToString());
                }
            }

            return features;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available features");
            return new List<string>();
        }
    }

    public async Task<Dictionary<PluginType, List<string>>> GetAvailablePluginsByTypeAsync()
    {
        try
        {
            var result = new Dictionary<PluginType, List<string>>();
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var enabledPlugins = plugins.Where(p => p.IsEnabled);

            foreach (var plugin in enabledPlugins)
            {
                var pluginType = plugin.Type;
                if (!result.ContainsKey(pluginType))
                {
                    result[pluginType] = new List<string>();
                }
                result[pluginType].Add(plugin.Name ?? "Unknown");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available plugins by type");
            return new Dictionary<PluginType, List<string>>();
        }
    }

    public async Task<bool> HasAnyPluginsAsync()
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            return plugins.Any(p => p.IsEnabled);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if any plugins are available");
            return false;
        }
    }
}
