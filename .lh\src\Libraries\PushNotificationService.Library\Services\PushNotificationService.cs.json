{"sourceFile": "src/Libraries/PushNotificationService.Library/Services/PushNotificationService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1751191694416, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751191726745, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -96,45 +96,45 @@\n         return Task.FromResult(new PushResponse(false, errorMessage: \"Resend not implemented\"));\r\n     }\r\n \r\n     // Admin functionality implementations\r\n-    public async Task<object> GetServiceStatusAsync()\r\n+    public Task<object> GetServiceStatusAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Status = \"Running\",\r\n             Platforms = new[] { \"iOS\", \"Android\", \"Web\" },\r\n             LastCheck = DateTime.UtcNow,\r\n             IsHealthy = true\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetPlatformsAsync()\r\n+    public Task<object> GetPlatformsAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Platforms = new object[]\r\n             {\r\n                 new { Name = \"iOS\", Enabled = true, CertificateExpiry = DateTime.UtcNow.AddMonths(6) },\r\n                 new { Name = \"Android\", Enabled = true, KeyExpiry = DateTime.UtcNow.AddYears(1) },\r\n                 new { Name = \"Web\", Enabled = true, KeyExpiry = DateTime.UtcNow.AddYears(1) }\r\n             }\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<ServiceResult> ConfigurePlatformAsync(string platform, object configuration)\r\n+    public Task<ServiceResult> ConfigurePlatformAsync(string platform, object configuration)\r\n     {\r\n-        return new ServiceResult { Success = true };\r\n+        return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n-    public async Task<object> TestPlatformAsync(string platform, string? testToken = null)\r\n+    public Task<object> TestPlatformAsync(string platform, string? testToken = null)\r\n     {\r\n-        return new { Success = true, Message = \"Test push notification sent successfully\" };\r\n+        return Task.FromResult<object>(new { Success = true, Message = \"Test push notification sent successfully\" });\r\n     }\r\n \r\n-    public async Task<ServiceResult> UpdatePlatformStatusAsync(string platform, bool enabled)\r\n+    public Task<ServiceResult> UpdatePlatformStatusAsync(string platform, bool enabled)\r\n     {\r\n-        return new ServiceResult { Success = true };\r\n+        return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n     public async Task<object> GetConfigurationAsync()\r\n     {\r\n"}, {"date": 1751191757938, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -135,42 +135,43 @@\n     {\r\n         return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n-    public async Task<object> GetConfigurationAsync()\r\n+    public Task<object> GetConfigurationAsync()\r\n     {\r\n-        return new { DefaultProvider = \"FCM\", BatchSize = 100 };\r\n+        return Task.FromResult<object>(new { DefaultProvider = \"FCM\", BatchSize = 100 });\r\n     }\r\n \r\n-    public async Task<ServiceResult> UpdateConfigurationAsync(object configuration)\r\n+    public Task<ServiceResult> UpdateConfigurationAsync(object configuration)\r\n     {\r\n-        return new ServiceResult { Success = true };\r\n+        return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n-    public async Task ClearCacheAsync()\r\n+    public Task ClearCacheAsync()\r\n     {\r\n         // Clear any cached data\r\n+        return Task.CompletedTask;\r\n     }\r\n \r\n-    public async Task<object> GetQueueStatusAsync()\r\n+    public Task<object> GetQueueStatusAsync()\r\n     {\r\n-        return new { QueueLength = 0, ProcessingCount = 0 };\r\n+        return Task.FromResult<object>(new { QueueLength = 0, ProcessingCount = 0 });\r\n     }\r\n \r\n-    public async Task<ServiceResult> PurgeQueueAsync()\r\n+    public Task<ServiceResult> PurgeQueueAsync()\r\n     {\r\n-        return new ServiceResult { Success = true, PurgedCount = 0 };\r\n+        return Task.FromResult(new ServiceResult { Success = true, PurgedCount = 0 });\r\n     }\r\n \r\n-    public async Task<object> CleanupDeviceTokensAsync(string? platform = null, bool dryRun = true)\r\n+    public Task<object> CleanupDeviceTokensAsync(string? platform = null, bool dryRun = true)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Platform = platform,\r\n             DryRun = dryRun,\r\n             TokensRemoved = 0,\r\n             InvalidTokens = new string[0]\r\n-        };\r\n+        });\r\n     }\r\n \r\n     public async Task<object> GetIosCertificateStatusAsync()\r\n     {\r\n"}, {"date": 1751191786900, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -172,46 +172,46 @@\n             InvalidTokens = new string[0]\r\n         });\r\n     }\r\n \r\n-    public async Task<object> GetIosCertificateStatusAsync()\r\n+    public Task<object> GetIosCertificateStatusAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             IsValid = true,\r\n             ExpiryDate = DateTime.UtcNow.AddMonths(6),\r\n             Subject = \"Apple Push Services\",\r\n             Issuer = \"Apple Inc.\"\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<ServiceResult> UpdateIosCertificateAsync(IFormFile certificate)\r\n+    public Task<ServiceResult> UpdateIosCertificateAsync(IFormFile certificate)\r\n     {\r\n-        return new ServiceResult { Success = true };\r\n+        return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n-    public async Task<object> GetDeliveryRateMetricsAsync(string? platform = null)\r\n+    public Task<object> GetDeliveryRateMetricsAsync(string? platform = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Platform = platform,\r\n             DeliveryRate = 95.5,\r\n             TotalSent = 1000,\r\n             Delivered = 955,\r\n             Failed = 45\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetDeviceTokenMetricsAsync(string? platform = null)\r\n+    public Task<object> GetDeviceTokenMetricsAsync(string? platform = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Platform = platform,\r\n             TotalTokens = 1000,\r\n             ActiveTokens = 850,\r\n             InvalidTokens = 50,\r\n             LastUpdated = DateTime.UtcNow\r\n-        };\r\n+        });\r\n     }\r\n \r\n     // Metrics functionality implementations\r\n     public async Task<object> GetSummaryMetricsAsync()\r\n"}, {"date": 1751191818424, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -213,75 +213,75 @@\n         });\r\n     }\r\n \r\n     // Metrics functionality implementations\r\n-    public async Task<object> GetSummaryMetricsAsync()\r\n+    public Task<object> GetSummaryMetricsAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             TotalSent = 0,\r\n             SuccessRate = 100.0,\r\n             LastHour = 0,\r\n             LastDay = 0\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? platform = null)\r\n+    public Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? platform = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Period = new { Start = startDate, End = endDate },\r\n             Platform = platform,\r\n             Metrics = new { Sent = 0, Delivered = 0, Failed = 0 }\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)\r\n+    public Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Period = new { Start = startDate, End = endDate },\r\n             Errors = new object[0]\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetMonthlyMetricsAsync(int months = 12)\r\n+    public Task<object> GetMonthlyMetricsAsync(int months = 12)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Months = months,\r\n             Data = new object[0]\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetPerformanceMetricsAsync()\r\n+    public Task<object> GetPerformanceMetricsAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             AverageDeliveryTime = TimeSpan.FromSeconds(30),\r\n             ThroughputPerHour = 1000\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetEngagementMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? platform = null)\r\n+    public Task<object> GetEngagementMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? platform = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Period = new { Start = startDate, End = endDate },\r\n             Platform = platform,\r\n             OpenRate = 15.5,\r\n             ClickRate = 3.2,\r\n             ConversionRate = 1.1\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetMonthlyStatisticsAsync()\r\n+    public Task<object> GetMonthlyStatisticsAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             TotalSent = 0,\r\n             TotalDelivered = 0,\r\n             TotalFailed = 0,\r\n             MonthlyData = new object[0]\r\n-        };\r\n+        });\r\n     }\r\n }\r\n"}], "date": 1751191694416, "name": "Commit-0", "content": "using PushNotificationService.Library.Interfaces;\r\nusing Microsoft.Extensions.Logging;\r\nusing NotificationContract.Models;\r\nusing FirebaseAdmin.Messaging;\r\nusing Microsoft.AspNetCore.Http;\r\n\r\nnamespace PushNotificationService.Library.Services;\r\n\r\npublic sealed class PushNotificationServiceImplementation : IPushNotificationService\r\n{\r\n    private readonly ILogger<PushNotificationServiceImplementation> _logger;\r\n\r\n    public PushNotificationServiceImplementation(ILogger<PushNotificationServiceImplementation> logger)\r\n    {\r\n        _logger = logger;\r\n    }\r\n\r\n    public async Task<PushResponse> SendAsync(PushMessageRequest request)\r\n    {\r\n        try\r\n        {\r\n            if (request is null)\r\n                throw new ArgumentNullException(nameof(request));\r\n\r\n            if (string.IsNullOrWhiteSpace(request.DeviceToken))\r\n                throw new ArgumentException(\"Device token cannot be null\");\r\n                \r\n            if (string.IsNullOrWhiteSpace(request.Body))\r\n                throw new ArgumentException(\"Message body cannot be null\");\r\n\r\n            _logger.LogInformation(\"Sending push notification to device {DeviceToken}\", request.DeviceToken);\r\n\r\n            var message = new Message()\r\n            {\r\n                Token = request.DeviceToken,\r\n                Notification = new Notification()\r\n                {\r\n                    Title = request.Title,\r\n                    Body = request.Body\r\n                },\r\n                Data = request.Data\r\n            };\r\n\r\n            var response = await FirebaseMessaging.DefaultInstance.SendAsync(message);\r\n\r\n            _logger.LogInformation(\"Push notification sent successfully to device {DeviceToken}, MessageId: {MessageId}\", \r\n                request.DeviceToken, response);\r\n\r\n            return new PushResponse(true, messageId: response);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Failed to send push notification to device {DeviceToken}\", request.DeviceToken);\r\n            return new PushResponse(false, errorMessage: ex.Message);\r\n        }\r\n    }\r\n\r\n    public async Task<BulkPushResponse> SendBulkAsync(BulkPushRequest request)\r\n    {\r\n        var results = new List<PushResponse>();\r\n        \r\n        foreach (var push in request.Messages)\r\n        {\r\n            var result = await SendAsync(push);\r\n            results.Add(result);\r\n        }\r\n\r\n        var successCount = results.Count(r => r.IsSuccess);\r\n        var response = new BulkPushResponse(successCount == results.Count);\r\n        response.Results = results;\r\n        return response;\r\n    }\r\n\r\n    public Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)\r\n    {\r\n        return Task.FromResult(new MessageStatusResponse\r\n        {\r\n            IsSuccess = true,\r\n            MessageId = messageId,\r\n            Status = \"Delivered\"\r\n        });\r\n    }\r\n\r\n    public Task<MessageHistoryResponse> GetMessageHistoryAsync(string deviceToken)\r\n    {\r\n        return Task.FromResult(new MessageHistoryResponse\r\n        {\r\n            IsSuccess = true,\r\n            Messages = new List<MessageHistoryItem>(),\r\n            TotalCount = 0\r\n        });\r\n    }\r\n\r\n    public Task<PushResponse> ResendMessageAsync(string messageId)\r\n    {\r\n        return Task.FromResult(new PushResponse(false, errorMessage: \"Resend not implemented\"));\r\n    }\r\n\r\n    // Admin functionality implementations\r\n    public async Task<object> GetServiceStatusAsync()\r\n    {\r\n        return new\r\n        {\r\n            Status = \"Running\",\r\n            Platforms = new[] { \"iOS\", \"Android\", \"Web\" },\r\n            LastCheck = DateTime.UtcNow,\r\n            IsHealthy = true\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetPlatformsAsync()\r\n    {\r\n        return new\r\n        {\r\n            Platforms = new object[]\r\n            {\r\n                new { Name = \"iOS\", Enabled = true, CertificateExpiry = DateTime.UtcNow.AddMonths(6) },\r\n                new { Name = \"Android\", Enabled = true, KeyExpiry = DateTime.UtcNow.AddYears(1) },\r\n                new { Name = \"Web\", Enabled = true, KeyExpiry = DateTime.UtcNow.AddYears(1) }\r\n            }\r\n        };\r\n    }\r\n\r\n    public async Task<ServiceResult> ConfigurePlatformAsync(string platform, object configuration)\r\n    {\r\n        return new ServiceResult { Success = true };\r\n    }\r\n\r\n    public async Task<object> TestPlatformAsync(string platform, string? testToken = null)\r\n    {\r\n        return new { Success = true, Message = \"Test push notification sent successfully\" };\r\n    }\r\n\r\n    public async Task<ServiceResult> UpdatePlatformStatusAsync(string platform, bool enabled)\r\n    {\r\n        return new ServiceResult { Success = true };\r\n    }\r\n\r\n    public async Task<object> GetConfigurationAsync()\r\n    {\r\n        return new { DefaultProvider = \"FCM\", BatchSize = 100 };\r\n    }\r\n\r\n    public async Task<ServiceResult> UpdateConfigurationAsync(object configuration)\r\n    {\r\n        return new ServiceResult { Success = true };\r\n    }\r\n\r\n    public async Task ClearCacheAsync()\r\n    {\r\n        // Clear any cached data\r\n    }\r\n\r\n    public async Task<object> GetQueueStatusAsync()\r\n    {\r\n        return new { QueueLength = 0, ProcessingCount = 0 };\r\n    }\r\n\r\n    public async Task<ServiceResult> PurgeQueueAsync()\r\n    {\r\n        return new ServiceResult { Success = true, PurgedCount = 0 };\r\n    }\r\n\r\n    public async Task<object> CleanupDeviceTokensAsync(string? platform = null, bool dryRun = true)\r\n    {\r\n        return new\r\n        {\r\n            Platform = platform,\r\n            DryRun = dryRun,\r\n            TokensRemoved = 0,\r\n            InvalidTokens = new string[0]\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetIosCertificateStatusAsync()\r\n    {\r\n        return new\r\n        {\r\n            IsValid = true,\r\n            ExpiryDate = DateTime.UtcNow.AddMonths(6),\r\n            Subject = \"Apple Push Services\",\r\n            Issuer = \"Apple Inc.\"\r\n        };\r\n    }\r\n\r\n    public async Task<ServiceResult> UpdateIosCertificateAsync(IFormFile certificate)\r\n    {\r\n        return new ServiceResult { Success = true };\r\n    }\r\n\r\n    public async Task<object> GetDeliveryRateMetricsAsync(string? platform = null)\r\n    {\r\n        return new\r\n        {\r\n            Platform = platform,\r\n            DeliveryRate = 95.5,\r\n            TotalSent = 1000,\r\n            Delivered = 955,\r\n            Failed = 45\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetDeviceTokenMetricsAsync(string? platform = null)\r\n    {\r\n        return new\r\n        {\r\n            Platform = platform,\r\n            TotalTokens = 1000,\r\n            ActiveTokens = 850,\r\n            InvalidTokens = 50,\r\n            LastUpdated = DateTime.UtcNow\r\n        };\r\n    }\r\n\r\n    // Metrics functionality implementations\r\n    public async Task<object> GetSummaryMetricsAsync()\r\n    {\r\n        return new\r\n        {\r\n            TotalSent = 0,\r\n            SuccessRate = 100.0,\r\n            LastHour = 0,\r\n            LastDay = 0\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? platform = null)\r\n    {\r\n        return new\r\n        {\r\n            Period = new { Start = startDate, End = endDate },\r\n            Platform = platform,\r\n            Metrics = new { Sent = 0, Delivered = 0, Failed = 0 }\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)\r\n    {\r\n        return new\r\n        {\r\n            Period = new { Start = startDate, End = endDate },\r\n            Errors = new object[0]\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetMonthlyMetricsAsync(int months = 12)\r\n    {\r\n        return new\r\n        {\r\n            Months = months,\r\n            Data = new object[0]\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetPerformanceMetricsAsync()\r\n    {\r\n        return new\r\n        {\r\n            AverageDeliveryTime = TimeSpan.FromSeconds(30),\r\n            ThroughputPerHour = 1000\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetEngagementMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? platform = null)\r\n    {\r\n        return new\r\n        {\r\n            Period = new { Start = startDate, End = endDate },\r\n            Platform = platform,\r\n            OpenRate = 15.5,\r\n            ClickRate = 3.2,\r\n            ConversionRate = 1.1\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetMonthlyStatisticsAsync()\r\n    {\r\n        return new\r\n        {\r\n            TotalSent = 0,\r\n            TotalDelivered = 0,\r\n            TotalFailed = 0,\r\n            MonthlyData = new object[0]\r\n        };\r\n    }\r\n}\r\n"}]}