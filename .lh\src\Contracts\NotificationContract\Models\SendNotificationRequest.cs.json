{"sourceFile": "src/Contracts/NotificationContract/Models/SendNotificationRequest.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751191301327, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751191301327, "name": "Commit-0", "content": "using EmailContract.Models;\r\nusing NotificationContract.Enums;\r\nusing PushNotificationContract.Models;\r\nusing SmsContract.Models;\r\n\r\nnamespace NotificationContract.Models;\r\n\r\npublic sealed record SendNotificationRequest\r\n{\r\n    public required string Id { get; set; }\r\n    public required NotificationType[] NotificationTypes { get; set; }\r\n    public string? Title { get;  set; }\r\n    public string? ReceptorName { get;  set; }\r\n    public required string Message { get; set; }\r\n    public string? Email { get; set;}\r\n    public string? PhoneNumber { get; set; }\r\n    public string? DeviceToken { get; set;}\r\n    \r\n    public static explicit operator SendEmailRequest(SendNotificationRequest notification)\r\n    {\r\n        if (!notification.NotificationTypes.Contains(NotificationType.Email))\r\n            throw new Exception(\"This type can't be converted to emailRequest\");\r\n        \r\n        if (string.IsNullOrWhiteSpace(notification.Email))\r\n            throw new ArgumentNullException(nameof(notification.Email),message:\"For sending an email, email field shouldn't be null\");\r\n        \r\n        if (string.IsNullOrWhiteSpace(notification.Message))\r\n            throw new ArgumentNullException(nameof(notification.Message),message:\"For sending an email, message field shouldn't be null\");\r\n        \r\n        if (string.IsNullOrWhiteSpace(notification.ReceptorName))\r\n            throw new ArgumentNullException(nameof(notification.ReceptorName),message:\"For sending an email, receptorName field shouldn't be null\");\r\n        \r\n        if (string.IsNullOrWhiteSpace(notification.Title))\r\n            throw new ArgumentNullException(nameof(notification.Title),message:\"For sending an email, title field shouldn't be null\");\r\n\r\n        SendEmailRequest emailRequest = new(notification.Email,notification.ReceptorName, notification.Title, notification.Message);\r\n        return emailRequest;\r\n    }\r\n    public static explicit operator SendSmsRequest(SendNotificationRequest notification)\r\n    {\r\n        if (!notification.NotificationTypes.Contains(NotificationType.Sms))\r\n            throw new Exception(\"This type can't be converted to smsRequest\");\r\n        \r\n        if (string.IsNullOrWhiteSpace(notification.PhoneNumber))\r\n            throw new ArgumentNullException(nameof(notification.PhoneNumber),message:\"For sending a sms, phoneNumber field shouldn't be null\");\r\n    \r\n        if (string.IsNullOrWhiteSpace(notification.Message))\r\n            throw new ArgumentNullException(nameof(notification.Message),message:\"For sending a sms, message field shouldn't be null\");\r\n\r\n        SendSmsRequest smsRequest = new(notification.PhoneNumber, notification.Message);\r\n        return smsRequest;\r\n    }\r\n    public static explicit operator SendPushMessageRequest(SendNotificationRequest notification)\r\n    {\r\n        if (!notification.NotificationTypes.Contains(NotificationType.PushMessage))\r\n            throw new Exception(\"This type can't be converted to pushMessageRequest\");\r\n        \r\n        if (string.IsNullOrWhiteSpace(notification.DeviceToken))\r\n            throw new ArgumentNullException(nameof(notification.DeviceToken),message:\"For sending a push message, deviceToken field shouldn't be null\");\r\n    \r\n        if (string.IsNullOrWhiteSpace(notification.Title))\r\n            throw new ArgumentNullException(nameof(notification.Title),message:\"For sending a push message, title field shouldn't be null\");\r\n        \r\n        if (string.IsNullOrWhiteSpace(notification.Message))\r\n            throw new ArgumentNullException(nameof(notification.Message),message:\"For sending a push message, message field shouldn't be null\");\r\n\r\n        SendPushMessageRequest pushMessageRequestRequest = new(notification.DeviceToken, notification.Title,notification.Message);\r\n        return pushMessageRequestRequest;\r\n    }\r\n    \r\n}"}]}