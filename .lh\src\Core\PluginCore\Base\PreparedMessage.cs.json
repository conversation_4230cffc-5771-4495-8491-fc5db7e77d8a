{"sourceFile": "src/Core/PluginCore/Base/PreparedMessage.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751198940565, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751198940565, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic class PreparedMessage\r\n{\r\n    public string? MessageId { get; set; }\r\n    public string? Content { get; set; }\r\n    public string? Metadata { get; set; }\r\n\r\n    public static explicit operator PreparedMessage(MessagePayload payload)\r\n        => new PreparedMessage\r\n        {\r\n            MessageId = payload.MessageId,\r\n            Content = payload.Content,\r\n            Metadata = payload.TemplateData?.ToString()\r\n        };\r\n    \r\n}\r\n"}]}