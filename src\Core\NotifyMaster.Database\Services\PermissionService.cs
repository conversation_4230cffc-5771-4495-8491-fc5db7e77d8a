using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PluginCore.Interfaces;
using PluginCore.Models;

namespace NotifyMaster.Database.Services;

/// <summary>
/// Implementation of permission management service using Entity Framework
/// </summary>
public class PermissionService : IPermissionService
{
    private readonly NotifyMasterDbContext _context;
    private readonly ILogger<PermissionService> _logger;

    public PermissionService(NotifyMasterDbContext context, ILogger<PermissionService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Permissions
                .Include(p => p.Roles).ThenInclude(rp => rp.Role)
                .Include(p => p.Users).ThenInclude(up => up.User)
                .FirstOrDefaultAsync(p => p.Id == permissionId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permission {PermissionId}", permissionId);
            return null;
        }
    }

    public async Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Permissions
                .FirstOrDefaultAsync(p => p.Resource == resource && p.Action == action, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permission {Resource}:{Action}", resource, action);
            return null;
        }
    }

    public async Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.Permissions.AsQueryable();

            if (!string.IsNullOrEmpty(resource))
            {
                query = query.Where(p => p.Resource == resource);
            }

            return await query
                .OrderBy(p => p.Resource).ThenBy(p => p.Action)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions");
            return Array.Empty<Permission>();
        }
    }

    public async Task<OperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if permission already exists
            var existingPermission = await _context.Permissions
                .FirstOrDefaultAsync(p => p.Resource == request.Resource && p.Action == request.Action, cancellationToken);

            if (existingPermission != null)
            {
                return OperationResult<Permission>.Failure("A permission with this resource and action already exists");
            }

            var permission = new Permission
            {
                Id = Guid.NewGuid().ToString(),
                Name = request.Name,
                Description = request.Description,
                Resource = request.Resource,
                Action = request.Action,
                IsSystemPermission = request.IsSystemPermission
            };

            _context.Permissions.Add(permission);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created permission {PermissionId} - {Resource}:{Action}", permission.Id, permission.Resource, permission.Action);
            return OperationResult<Permission>.Success(permission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating permission {Resource}:{Action}", request.Resource, request.Action);
            return OperationResult<Permission>.Failure($"Failed to create permission: {ex.Message}");
        }
    }

    public async Task<OperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return OperationResult<Permission>.Failure("Permission not found");
            }

            if (permission.IsSystemPermission)
            {
                return OperationResult<Permission>.Failure("Cannot update system permissions");
            }

            // Update properties if provided
            if (!string.IsNullOrEmpty(request.Name))
                permission.Name = request.Name;

            if (!string.IsNullOrEmpty(request.Description))
                permission.Description = request.Description;

            if (!string.IsNullOrEmpty(request.Resource) || !string.IsNullOrEmpty(request.Action))
            {
                var newResource = request.Resource ?? permission.Resource;
                var newAction = request.Action ?? permission.Action;

                // Check if new resource:action combination already exists
                var existingPermission = await _context.Permissions
                    .FirstOrDefaultAsync(p => p.Resource == newResource && p.Action == newAction && p.Id != permissionId, cancellationToken);

                if (existingPermission != null)
                {
                    return OperationResult<Permission>.Failure("A permission with this resource and action already exists");
                }

                permission.Resource = newResource;
                permission.Action = newAction;
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated permission {PermissionId}", permissionId);
            return OperationResult<Permission>.Success(permission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating permission {PermissionId}", permissionId);
            return OperationResult<Permission>.Failure($"Failed to update permission: {ex.Message}");
        }
    }

    public async Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return OperationResult.Failure("Permission not found");
            }

            if (permission.IsSystemPermission)
            {
                return OperationResult.Failure("Cannot delete system permissions");
            }

            _context.Permissions.Remove(permission);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted permission {PermissionId}", permissionId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting permission {PermissionId}", permissionId);
            return OperationResult.Failure($"Failed to delete permission: {ex.Message}");
        }
    }

    public async Task<IReadOnlyList<Role>> GetPermissionRolesAsync(string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.RolePermissions
                .Include(rp => rp.Role)
                .Where(rp => rp.PermissionId == permissionId)
                .Select(rp => rp.Role)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for permission {PermissionId}", permissionId);
            return Array.Empty<Role>();
        }
    }

    public async Task<IReadOnlyList<User>> GetPermissionUsersAsync(string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var directUsers = await _context.UserPermissions
                .Include(up => up.User)
                .Where(up => up.PermissionId == permissionId)
                .Select(up => up.User)
                .ToListAsync(cancellationToken);

            var roleUsers = await _context.RolePermissions
                .Include(rp => rp.Role)
                .ThenInclude(r => r.Users)
                .ThenInclude(ur => ur.User)
                .Where(rp => rp.PermissionId == permissionId)
                .SelectMany(rp => rp.Role.Users)
                .Select(ur => ur.User)
                .ToListAsync(cancellationToken);

            var allUsers = directUsers.Concat(roleUsers).DistinctBy(u => u.Id).ToList();
            return allUsers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for permission {PermissionId}", permissionId);
            return Array.Empty<User>();
        }
    }

    public async Task<OperationResult> SeedDefaultPermissionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🌱 Seeding default permissions and roles...");

            // Check if permissions already exist
            var existingPermissionsCount = await _context.Permissions.CountAsync(cancellationToken);
            if (existingPermissionsCount > 0)
            {
                _logger.LogInformation("✅ Permissions already seeded ({Count} permissions found)", existingPermissionsCount);
                return OperationResult.Success();
            }

            // Seed permissions
            var defaultPermissions = SystemPermissions.GetDefaultPermissions();
            var permissions = new List<Permission>();

            foreach (var (name, description, resource, action) in defaultPermissions)
            {
                permissions.Add(new Permission
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = name,
                    Description = description,
                    Resource = resource,
                    Action = action,
                    IsSystemPermission = true
                });
            }

            _context.Permissions.AddRange(permissions);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("✅ Seeded {Count} permissions", permissions.Count);

            // Seed roles
            var existingRolesCount = await _context.Roles.CountAsync(cancellationToken);
            if (existingRolesCount == 0)
            {
                var defaultRoles = SystemRoles.GetDefaultRoles();
                var roles = new List<Role>();

                foreach (var (name, description, scope, permissionNames) in defaultRoles)
                {
                    var role = new Role
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = name,
                        Description = description,
                        Scope = scope,
                        IsSystemRole = true,
                        CreatedAt = DateTime.UtcNow
                    };

                    roles.Add(role);
                }

                _context.Roles.AddRange(roles);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("✅ Seeded {Count} roles", roles.Count);

                // Assign permissions to roles
                var rolePermissions = new List<RolePermission>();

                foreach (var (name, description, scope, permissionNames) in defaultRoles)
                {
                    var role = roles.First(r => r.Name == name);

                    foreach (var permissionName in permissionNames)
                    {
                        var permission = permissions.FirstOrDefault(p => p.Name == permissionName);
                        if (permission != null)
                        {
                            rolePermissions.Add(new RolePermission
                            {
                                RoleId = role.Id,
                                PermissionId = permission.Id,
                                AssignedAt = DateTime.UtcNow,
                                AssignedBy = "System Seed"
                            });
                        }
                    }
                }

                _context.RolePermissions.AddRange(rolePermissions);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("✅ Assigned {Count} role permissions", rolePermissions.Count);
            }

            _logger.LogInformation("🎉 Default permissions and roles seeded successfully!");
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to seed default permissions and roles");
            return OperationResult.Failure($"Failed to seed default data: {ex.Message}");
        }
    }
}
