using StackExchange.Redis;
using System.Collections.Concurrent;

namespace NotifyMasterApi.Services;

/// <summary>
/// Redis connection service with fallback to in-memory storage
/// </summary>
public class RedisConnectionService
{
    private readonly ILogger<RedisConnectionService> _logger;
    private readonly IConnectionMultiplexer? _redis;
    private readonly ConcurrentDictionary<string, string> _fallbackStorage = new();
    private readonly bool _isUsingFallback;

    public RedisConnectionService(ILogger<RedisConnectionService> logger, IConfiguration configuration)
    {
        _logger = logger;
        
        try
        {
            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
            _redis = ConnectionMultiplexer.Connect(connectionString);
            
            if (_redis.IsConnected)
            {
                _isUsingFallback = false;
                _logger.LogInformation("✅ Redis connection established successfully");
                return;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️  Redis connection failed: {Message}", ex.Message);
        }
        
        _isUsingFallback = true;
        _logger.LogWarning("🔄 Using in-memory fallback for Redis operations");
        _logger.LogInformation("📝 All Redis data will be stored in memory and lost on restart");
    }

    public bool IsUsingFallback => _isUsingFallback;
    public bool IsConnected => _redis?.IsConnected ?? false;

    public async Task<bool> SetStringAsync(string key, string value, TimeSpan? expiry = null)
    {
        if (_isUsingFallback)
        {
            _fallbackStorage[key] = value;
            _logger.LogDebug("Fallback Redis: SET {Key} = {Value}", key, value);
            return true;
        }

        try
        {
            var database = _redis!.GetDatabase();
            return await database.StringSetAsync(key, value, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis SET operation failed for key {Key}", key);
            return false;
        }
    }

    public async Task<string?> GetStringAsync(string key)
    {
        if (_isUsingFallback)
        {
            _fallbackStorage.TryGetValue(key, out var value);
            _logger.LogDebug("Fallback Redis: GET {Key} = {Value}", key, value ?? "null");
            return value;
        }

        try
        {
            var database = _redis!.GetDatabase();
            var result = await database.StringGetAsync(key);
            return result.HasValue ? result.ToString() : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis GET operation failed for key {Key}", key);
            return null;
        }
    }

    public async Task<bool> DeleteKeyAsync(string key)
    {
        if (_isUsingFallback)
        {
            var removed = _fallbackStorage.TryRemove(key, out _);
            _logger.LogDebug("Fallback Redis: DEL {Key} = {Removed}", key, removed);
            return removed;
        }

        try
        {
            var database = _redis!.GetDatabase();
            return await database.KeyDeleteAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis DELETE operation failed for key {Key}", key);
            return false;
        }
    }

    public async Task<long> ListPushAsync(string key, string value)
    {
        if (_isUsingFallback)
        {
            // Simple list simulation using concatenated strings
            var existingList = _fallbackStorage.GetOrAdd(key, "");
            var newList = string.IsNullOrEmpty(existingList) ? value : $"{existingList}|{value}";
            _fallbackStorage[key] = newList;
            var count = newList.Split('|').Length;
            _logger.LogDebug("Fallback Redis: LPUSH {Key} {Value} = {Count}", key, value, count);
            return count;
        }

        try
        {
            var database = _redis!.GetDatabase();
            return await database.ListLeftPushAsync(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis LPUSH operation failed for key {Key}", key);
            return 0;
        }
    }

    public async Task<string?> ListPopAsync(string key)
    {
        if (_isUsingFallback)
        {
            if (_fallbackStorage.TryGetValue(key, out var list) && !string.IsNullOrEmpty(list))
            {
                var items = list.Split('|');
                if (items.Length > 0)
                {
                    var value = items[^1]; // Get last item
                    var newList = string.Join("|", items[..^1]); // Remove last item
                    if (string.IsNullOrEmpty(newList))
                        _fallbackStorage.TryRemove(key, out _);
                    else
                        _fallbackStorage[key] = newList;
                    
                    _logger.LogDebug("Fallback Redis: RPOP {Key} = {Value}", key, value);
                    return value;
                }
            }
            return null;
        }

        try
        {
            var database = _redis!.GetDatabase();
            var result = await database.ListRightPopAsync(key);
            return result.HasValue ? result.ToString() : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis RPOP operation failed for key {Key}", key);
            return null;
        }
    }

    public async Task<bool> PublishAsync(string channel, string message)
    {
        if (_isUsingFallback)
        {
            _logger.LogDebug("Fallback Redis: PUBLISH {Channel} {Message} (no subscribers)", channel, message);
            // In fallback mode, we can't really publish to subscribers
            // This would need a more sophisticated in-memory pub/sub system
            return true;
        }

        try
        {
            var subscriber = _redis!.GetSubscriber();
            var result = await subscriber.PublishAsync(channel, message);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis PUBLISH operation failed for channel {Channel}", channel);
            return false;
        }
    }

    public Dictionary<string, object> GetStatus()
    {
        return new Dictionary<string, object>
        {
            ["IsConnected"] = IsConnected,
            ["IsUsingFallback"] = _isUsingFallback,
            ["ConnectionStatus"] = _isUsingFallback ? "Fallback (In-Memory)" : "Connected",
            ["FallbackItemCount"] = _isUsingFallback ? _fallbackStorage.Count : 0,
            ["RedisEndpoints"] = _redis?.GetEndPoints()?.Select(ep => ep.ToString()).ToArray() ?? Array.Empty<string>()
        };
    }

    public void ClearFallbackData()
    {
        if (_isUsingFallback)
        {
            var count = _fallbackStorage.Count;
            _fallbackStorage.Clear();
            _logger.LogInformation("🗑️ Cleared {Count} items from fallback storage", count);
        }
    }

    public void Dispose()
    {
        _redis?.Dispose();
        _fallbackStorage.Clear();
    }
}

/// <summary>
/// Extension methods for Redis connection service registration
/// </summary>
public static class RedisConnectionServiceExtensions
{
    public static IServiceCollection AddRedisWithFallback(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<RedisConnectionService>();
        
        // Register IConnectionMultiplexer with fallback logic
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<RedisConnectionService>>();
            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
            
            try
            {
                var connection = ConnectionMultiplexer.Connect(connectionString);
                if (connection.IsConnected)
                {
                    Console.WriteLine("✅ Redis connection established successfully");
                    return connection;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Redis connection failed: {ex.Message}");
            }
            
            Console.WriteLine("🔄 Redis fallback: Using RedisConnectionService wrapper");
            // Return a minimal connection that will be handled by RedisConnectionService
            throw new InvalidOperationException("Redis not available - use RedisConnectionService instead");
        });

        return services;
    }
}
