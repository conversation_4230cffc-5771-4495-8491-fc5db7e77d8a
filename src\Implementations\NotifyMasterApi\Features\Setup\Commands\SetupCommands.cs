using MediatR;
using NotifyMasterApi.Infrastructure;

namespace NotifyMasterApi.Features.Setup.Commands;

/// <summary>
/// Command to initialize the system
/// </summary>
public record InitializeSystemCommand(
    string RootTenantName,
    string AdminEmail,
    string AdminPassword,
    Dictionary<string, object>? Configuration = null) : IRequest<InitializeSystemResult>;

/// <summary>
/// Command to load plugins during setup
/// </summary>
public record LoadPluginsCommand(string PluginDirectory = "plugins") : IRequest<LoadPluginsResult>;

/// <summary>
/// Result of system initialization
/// </summary>
public class InitializeSystemResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? RootTenantName { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, bool> ComponentStatus { get; set; } = new();
}

/// <summary>
/// Result of plugin loading
/// </summary>
public class LoadPluginsResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int LoadedCount { get; set; }
    public List<string> LoadedPlugins { get; set; } = new();
    public List<string> Errors { get; set; } = new();
}
