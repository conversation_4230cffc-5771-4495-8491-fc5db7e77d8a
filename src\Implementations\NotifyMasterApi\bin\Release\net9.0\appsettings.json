{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=NotificationService;Username=********;Password=********", "Redis": "localhost:6379"}, "Services": {"EmailService": {"BaseUrl": "http://localhost:5001"}, "SmsService": {"BaseUrl": "http://localhost:5002"}, "PushService": {"BaseUrl": "http://localhost:5003"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning"}}}, "ElasticSearch": {"Uri": "http://localhost:9200"}, "Plugins": {"PluginsDirectory": "plugins", "Kavenegar": {"ApiKey": "your-kavenegar-api-key", "SenderNumber": "your-sender-number", "IsEnabled": true, "TimeoutSeconds": 30, "RetryCount": 3}, "SMTP": {"Host": "smtp.gmail.com", "Port": 587, "UseSsl": true, "Username": "<EMAIL>", "Password": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "Notification Service", "IsEnabled": true, "TimeoutSeconds": 30}, "Firebase": {"ServiceAccountKeyPath": "path/to/firebase-service-account.json", "ProjectId": "your-firebase-project-id", "IsEnabled": true, "TimeoutSeconds": 30, "DryRun": false}}}