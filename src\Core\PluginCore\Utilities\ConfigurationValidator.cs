using PluginCore.Models;
using System.Text.RegularExpressions;

namespace PluginCore.Utilities;

/// <summary>
/// Utility class for validating gateway configurations.
/// </summary>
public static class ConfigurationValidator
{
    /// <summary>
    /// Validates a gateway configuration and returns validation results.
    /// </summary>
    public static ValidationResult ValidateGatewayConfiguration(
        Dictionary<string, object> configuration,
        GatewayConfigurationSchema schema)
    {
        var errors = new List<string>();

        foreach (var field in schema.RequiredFields)
        {
            var result = ValidateField(configuration, field);
            if (!result.IsValid)
            {
                errors.Add(result.ErrorMessage ?? $"Validation failed for field {field.Name}");
            }
        }

        foreach (var field in schema.OptionalFields)
        {
            if (configuration.ContainsKey(field.Name))
            {
                var result = ValidateField(configuration, field);
                if (!result.IsValid)
                {
                    errors.Add(result.ErrorMessage ?? $"Validation failed for field {field.Name}");
                }
            }
        }

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            ErrorMessage = errors.Any() ? string.Join("; ", errors) : null
        };
    }

    /// <summary>
    /// Validates a single configuration field.
    /// </summary>
    public static ValidationResult ValidateField(Dictionary<string, object> configuration, ConfigurationField field)
    {
        if (!configuration.TryGetValue(field.Name, out var value))
        {
            if (field.IsRequired)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Required field '{field.Name}' is missing"
                };
            }
            return new ValidationResult { IsValid = true };
        }

        if (value == null)
        {
            if (field.IsRequired)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Required field '{field.Name}' cannot be null"
                };
            }
            return new ValidationResult { IsValid = true };
        }

        var stringValue = value.ToString();
        if (string.IsNullOrWhiteSpace(stringValue) && field.IsRequired)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = $"Required field '{field.Name}' cannot be empty"
            };
        }

        // Type validation
        var typeValidation = ValidateFieldType(stringValue, field.Type);
        if (!typeValidation.IsValid)
        {
            return typeValidation;
        }

        // Pattern validation
        if (!string.IsNullOrEmpty(field.ValidationPattern))
        {
            var patternValidation = ValidatePattern(stringValue, field.ValidationPattern, field.Name);
            if (!patternValidation.IsValid)
            {
                return patternValidation;
            }
        }

        // Length validation
        if (field.MinLength.HasValue && stringValue.Length < field.MinLength.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = $"Field '{field.Name}' must be at least {field.MinLength.Value} characters long"
            };
        }

        if (field.MaxLength.HasValue && stringValue.Length > field.MaxLength.Value)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = $"Field '{field.Name}' must be no more than {field.MaxLength.Value} characters long"
            };
        }

        // Allowed values validation
        if (field.AllowedValues?.Any() == true && !field.AllowedValues.Contains(stringValue))
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = $"Field '{field.Name}' must be one of: {string.Join(", ", field.AllowedValues)}"
            };
        }

        return new ValidationResult { IsValid = true };
    }

    /// <summary>
    /// Validates the type of a field value.
    /// </summary>
    public static ValidationResult ValidateFieldType(string value, string expectedType)
    {
        return expectedType.ToLowerInvariant() switch
        {
            "string" => new ValidationResult { IsValid = true },
            "int" or "integer" => ValidateInteger(value),
            "bool" or "boolean" => ValidateBoolean(value),
            "double" or "decimal" => ValidateDouble(value),
            "url" => ValidateUrl(value),
            "email" => ValidateEmail(value),
            "phone" => ValidatePhoneNumber(value),
            _ => new ValidationResult { IsValid = true } // Unknown types pass validation
        };
    }

    /// <summary>
    /// Validates a pattern using regular expressions.
    /// </summary>
    public static ValidationResult ValidatePattern(string value, string pattern, string fieldName)
    {
        try
        {
            var regex = new Regex(pattern, RegexOptions.IgnoreCase);
            if (!regex.IsMatch(value))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Field '{fieldName}' does not match the required pattern"
                };
            }
            return new ValidationResult { IsValid = true };
        }
        catch (Exception ex)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = $"Invalid validation pattern for field '{fieldName}': {ex.Message}"
            };
        }
    }

    private static ValidationResult ValidateInteger(string value)
    {
        if (int.TryParse(value, out _))
        {
            return new ValidationResult { IsValid = true };
        }
        return new ValidationResult
        {
            IsValid = false,
            ErrorMessage = "Value must be a valid integer"
        };
    }

    private static ValidationResult ValidateBoolean(string value)
    {
        if (bool.TryParse(value, out _))
        {
            return new ValidationResult { IsValid = true };
        }
        return new ValidationResult
        {
            IsValid = false,
            ErrorMessage = "Value must be a valid boolean (true/false)"
        };
    }

    private static ValidationResult ValidateDouble(string value)
    {
        if (double.TryParse(value, out _))
        {
            return new ValidationResult { IsValid = true };
        }
        return new ValidationResult
        {
            IsValid = false,
            ErrorMessage = "Value must be a valid number"
        };
    }

    private static ValidationResult ValidateUrl(string value)
    {
        if (Uri.TryCreate(value, UriKind.Absolute, out var uri) &&
            (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps))
        {
            return new ValidationResult { IsValid = true };
        }
        return new ValidationResult
        {
            IsValid = false,
            ErrorMessage = "Value must be a valid HTTP or HTTPS URL"
        };
    }

    private static ValidationResult ValidateEmail(string value)
    {
        var emailRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.IgnoreCase);
        if (emailRegex.IsMatch(value))
        {
            return new ValidationResult { IsValid = true };
        }
        return new ValidationResult
        {
            IsValid = false,
            ErrorMessage = "Value must be a valid email address"
        };
    }

    private static ValidationResult ValidatePhoneNumber(string value)
    {
        // Remove common formatting characters
        var cleaned = Regex.Replace(value, @"[\s\-\(\)\+]", "");
        
        // Basic validation: should be 10-15 digits
        if (Regex.IsMatch(cleaned, @"^\d{10,15}$"))
        {
            return new ValidationResult { IsValid = true };
        }
        return new ValidationResult
        {
            IsValid = false,
            ErrorMessage = "Value must be a valid phone number"
        };
    }
}

/// <summary>
/// Represents a configuration schema for a gateway.
/// </summary>
public class GatewayConfigurationSchema
{
    public List<ConfigurationField> RequiredFields { get; set; } = new();
    public List<ConfigurationField> OptionalFields { get; set; } = new();
}

/// <summary>
/// Represents a configuration field definition.
/// </summary>
public class ConfigurationField
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = "string";
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsSecret { get; set; }
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public List<string>? AllowedValues { get; set; }
    public object? DefaultValue { get; set; }

    public ConfigurationField() { }

    public ConfigurationField(string name, string type, string description, bool isRequired = false, bool isSecret = false)
    {
        Name = name;
        Type = type;
        Description = description;
        IsRequired = isRequired;
        IsSecret = isSecret;
    }
}
