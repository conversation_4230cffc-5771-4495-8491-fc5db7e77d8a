{"sourceFile": "src/Core/NotifyMaster.Database/NotifyMaster.Database.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751216381426, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751216381426, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\r\n\r\n  <PropertyGroup>\r\n    <TargetFramework>net10.0</TargetFramework>\r\n    <ImplicitUsings>enable</ImplicitUsings>\r\n    <Nullable>enable</Nullable>\r\n  </PropertyGroup>\r\n\r\n  <ItemGroup>\r\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\r\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore.Design\" Version=\"9.0.6\" />\r\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore.Tools\" Version=\"9.0.6\" />\r\n    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.6\" />\r\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\r\n  </ItemGroup>\r\n\r\n  <ItemGroup>\r\n    <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\r\n  </ItemGroup>\r\n\r\n</Project>\r\n"}]}