using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PluginContract.Interfaces;
using PluginContract.Models;
using EmailContract.Models;
using PluginCore.Base;
using System.Text.Json;
using System.Text;
using System.Diagnostics;

namespace Plugin.Email.Mailgun;

public class MailgunPlugin : BaseNotificationPlugin
{
    private readonly ILogger<MailgunPlugin> _logger;
    private readonly HttpClient _httpClient;
    private bool _isInitialized = false;
    private string? _apiKey;
    private string? _domain;
    private string? _baseUrl;

    public MailgunPlugin(ILogger<MailgunPlugin> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    public override PluginInfo PluginInfo => new(
        Name: "Mailgun",
        Version: "1.0.0",
        Description: "Mailgun email service plugin for sending transactional and marketing emails",
        Type: PluginContract.Enums.PluginType.Email,
        Author: "NotificationService Team",
        IsEnabled: true
    );

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);

        _apiKey = configuration["Mailgun:ApiKey"];
        _domain = configuration["Mailgun:Domain"];
        _baseUrl = configuration["Mailgun:BaseUrl"] ?? "https://api.mailgun.net/v3";

        if (string.IsNullOrEmpty(_apiKey) || string.IsNullOrEmpty(_domain))
        {
            throw new InvalidOperationException("Mailgun API Key and Domain are required");
        }

        // Set up basic authentication
        var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"api:{_apiKey}"));
        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {authValue}");

        _isInitialized = true;
        _logger.LogInformation("Mailgun plugin initialized successfully for domain: {Domain}", _domain);
    }

    public override async Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (!_isInitialized)
                throw new InvalidOperationException("Plugin not initialized");

            if (request is not EmailRequest emailRequest)
                throw new ArgumentException("Invalid request type for Mailgun plugin");

            var formData = new List<KeyValuePair<string, string>>
            {
                new("from", emailRequest.From),
                new("to", emailRequest.To),
                new("subject", emailRequest.Subject),
                new("text", emailRequest.PlainTextBody ?? ""),
                new("html", emailRequest.HtmlBody ?? emailRequest.PlainTextBody ?? "")
            };

            // Add CC and BCC if provided
            if (!string.IsNullOrEmpty(emailRequest.Cc))
                formData.Add(new("cc", emailRequest.Cc));
            
            if (!string.IsNullOrEmpty(emailRequest.Bcc))
                formData.Add(new("bcc", emailRequest.Bcc));

            // Add custom headers if provided
            if (emailRequest.Headers != null)
            {
                foreach (var header in emailRequest.Headers)
                {
                    formData.Add(new($"h:{header.Key}", header.Value));
                }
            }

            // Add tags for tracking
            formData.Add(new("o:tag", "notification-service"));
            if (!string.IsNullOrEmpty(emailRequest.Category))
                formData.Add(new("o:tag", emailRequest.Category));

            var content = new FormUrlEncodedContent(formData);
            var url = $"{_baseUrl}/{_domain}/messages";
            
            var response = await _httpClient.PostAsync(url, content, cancellationToken);

            stopwatch.Stop();
            var responseTimeMs = stopwatch.Elapsed.TotalMilliseconds;

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
                var messageId = responseData.GetProperty("id").GetString();

                TrackRequest(true, responseTimeMs, emailRequest.HtmlBody?.Length ?? emailRequest.PlainTextBody?.Length ?? 0);
                _logger.LogInformation("Email sent via Mailgun successfully. MessageId: {MessageId}", messageId);

                return new EmailResponse(true, MessageId: messageId);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                TrackRequest(false, responseTimeMs, 0, response.StatusCode.ToString());
                _logger.LogError("Mailgun API error: {StatusCode} - {Error}", response.StatusCode, errorContent);

                return new EmailResponse(false, ErrorMessage: $"Mailgun API error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            TrackRequest(false, stopwatch.Elapsed.TotalMilliseconds, 0, ex.GetType().Name);
            _logger.LogError(ex, "Error sending email via Mailgun");
            return new EmailResponse(false, ErrorMessage: ex.Message);
        }
    }

    public override async Task<bool> HealthCheckAsync()
    {
        try
        {
            if (!_isInitialized) return false;

            var url = $"{_baseUrl}/{_domain}";
            var response = await _httpClient.GetAsync(url);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    protected override string GetProviderName() => "Mailgun";

    protected override List<ConfigurationField> GetConfigurationFields()
    {
        return new List<ConfigurationField>
        {
            new("ApiKey", "string", "Mailgun API Key", true, true),
            new("Domain", "string", "Mailgun Domain", true, false),
            new("BaseUrl", "string", "Mailgun API Base URL", false, false, "https://api.mailgun.net/v3"),
            new("DefaultFromEmail", "string", "Default sender email address", false, false),
            new("DefaultFromName", "string", "Default sender name", false, false)
        };
    }

    protected override Dictionary<string, object> GetCustomMetrics()
    {
        return new Dictionary<string, object>
        {
            ["provider"] = "Mailgun",
            ["domain"] = _domain ?? "not_configured",
            ["base_url"] = _baseUrl ?? "not_configured",
            ["last_health_check"] = DateTime.UtcNow
        };
    }

    // Mailgun-specific methods
    public async Task<object> GetDomainStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        if (!_isInitialized) throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var url = $"{_baseUrl}/{_domain}/stats/total";
            if (startDate.HasValue)
                url += $"?start={startDate.Value:yyyy-MM-dd}";
            if (endDate.HasValue)
                url += $"{(startDate.HasValue ? "&" : "?")}end={endDate.Value:yyyy-MM-dd}";

            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<JsonElement>(content);
            }
            return new { error = "Failed to get domain stats" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Mailgun domain stats");
            return new { error = ex.Message };
        }
    }
}

// Mailgun-specific request and response models
public record EmailRequest(
    string To,
    string From,
    string Subject,
    string? PlainTextBody = null,
    string? HtmlBody = null,
    string? Cc = null,
    string? Bcc = null,
    string? Category = null,
    Dictionary<string, string>? Headers = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(PlainTextBody ?? HtmlBody ?? "", Metadata);

public record EmailResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
