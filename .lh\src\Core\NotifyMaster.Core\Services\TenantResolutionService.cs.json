{"sourceFile": "src/Core/NotifyMaster.Core/Services/TenantResolutionService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751225289033, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751225289033, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Services;\n\n/// <summary>\n/// Service responsible for resolving tenant context from HTTP requests\n/// </summary>\npublic interface ITenantResolutionService\n{\n    /// <summary>\n    /// Resolves tenant from HTTP context\n    /// </summary>\n    Task<Tenant?> ResolveTenantAsync(HttpContext context, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Resolves tenant by domain\n    /// </summary>\n    Task<Tenant?> ResolveTenantByDomainAsync(string domain, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Resolves tenant by ID\n    /// </summary>\n    Task<Tenant?> ResolveTenantByIdAsync(string tenantId, CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Implementation of tenant resolution service\n/// </summary>\npublic class TenantResolutionService : ITenantResolutionService\n{\n    private readonly ITenantService _tenantService;\n    private readonly ILogger<TenantResolutionService> _logger;\n\n    public TenantResolutionService(\n        ITenantService tenantService,\n        ILogger<TenantResolutionService> logger)\n    {\n        _tenantService = tenantService;\n        _logger = logger;\n    }\n\n    public async Task<Tenant?> ResolveTenantAsync(HttpContext context, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Try to resolve from header first\n            var tenantFromHeader = await ResolveTenantFromHeaderAsync(context, cancellationToken);\n            if (tenantFromHeader != null)\n            {\n                return tenantFromHeader;\n            }\n\n            // Try to resolve from subdomain\n            var tenantFromSubdomain = await ResolveTenantFromSubdomainAsync(context, cancellationToken);\n            if (tenantFromSubdomain != null)\n            {\n                return tenantFromSubdomain;\n            }\n\n            // Try to resolve from JWT claims\n            var tenantFromJwt = await ResolveTenantFromJwtAsync(context, cancellationToken);\n            if (tenantFromJwt != null)\n            {\n                return tenantFromJwt;\n            }\n\n            // Fallback to default tenant\n            return await ResolveTenantByDomainAsync(\"default\", cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error resolving tenant from HTTP context\");\n            return null;\n        }\n    }\n\n    public async Task<Tenant?> ResolveTenantByDomainAsync(string domain, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _tenantService.GetTenantByDomainAsync(domain, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error resolving tenant by domain {Domain}\", domain);\n            return null;\n        }\n    }\n\n    public async Task<Tenant?> ResolveTenantByIdAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _tenantService.GetTenantAsync(tenantId, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error resolving tenant by ID {TenantId}\", tenantId);\n            return null;\n        }\n    }\n\n    private async Task<Tenant?> ResolveTenantFromHeaderAsync(HttpContext context, CancellationToken cancellationToken)\n    {\n        if (context.Request.Headers.TryGetValue(\"X-Tenant-Id\", out var tenantIdHeader))\n        {\n            var tenantId = tenantIdHeader.FirstOrDefault();\n            if (!string.IsNullOrEmpty(tenantId))\n            {\n                return await ResolveTenantByIdAsync(tenantId, cancellationToken);\n            }\n        }\n\n        if (context.Request.Headers.TryGetValue(\"X-Tenant-Domain\", out var tenantDomainHeader))\n        {\n            var tenantDomain = tenantDomainHeader.FirstOrDefault();\n            if (!string.IsNullOrEmpty(tenantDomain))\n            {\n                return await ResolveTenantByDomainAsync(tenantDomain, cancellationToken);\n            }\n        }\n\n        return null;\n    }\n\n    private async Task<Tenant?> ResolveTenantFromSubdomainAsync(HttpContext context, CancellationToken cancellationToken)\n    {\n        var host = context.Request.Host.Host;\n        if (string.IsNullOrEmpty(host))\n        {\n            return null;\n        }\n\n        // Extract subdomain (e.g., \"tenant1.api.example.com\" -> \"tenant1\")\n        var parts = host.Split('.');\n        if (parts.Length >= 3)\n        {\n            var subdomain = parts[0];\n            if (!string.IsNullOrEmpty(subdomain) && subdomain != \"www\" && subdomain != \"api\")\n            {\n                return await ResolveTenantByDomainAsync(subdomain, cancellationToken);\n            }\n        }\n\n        return null;\n    }\n\n    private async Task<Tenant?> ResolveTenantFromJwtAsync(HttpContext context, CancellationToken cancellationToken)\n    {\n        if (context.User?.Identity?.IsAuthenticated == true)\n        {\n            var tenantIdClaim = context.User.FindFirst(\"tenant_id\")?.Value;\n            if (!string.IsNullOrEmpty(tenantIdClaim))\n            {\n                return await ResolveTenantByIdAsync(tenantIdClaim, cancellationToken);\n            }\n\n            var tenantDomainClaim = context.User.FindFirst(\"tenant_domain\")?.Value;\n            if (!string.IsNullOrEmpty(tenantDomainClaim))\n            {\n                return await ResolveTenantByDomainAsync(tenantDomainClaim, cancellationToken);\n            }\n        }\n\n        return null;\n    }\n}\n"}]}