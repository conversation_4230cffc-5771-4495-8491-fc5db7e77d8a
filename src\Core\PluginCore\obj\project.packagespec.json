﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj","projectName":"PluginCore","projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj","outputPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\EmailContract\\EmailContract.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\EmailContract\\EmailContract.csproj"},"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\PluginContract\\PluginContract.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\PluginContract\\PluginContract.csproj"},"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj"},"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\SmsContract\\SmsContract.csproj":{"projectPath":"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Contracts\\SmsContract\\SmsContract.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.Extensions.Configuration":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.Configuration.Binder":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.DependencyInjection":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.Hosting.Abstractions":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.Logging":{"target":"Package","version":"[9.0.0, )"},"System.Reflection.MetadataLoadContext":{"target":"Package","version":"[9.0.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json"}}