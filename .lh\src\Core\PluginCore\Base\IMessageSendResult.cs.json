{"sourceFile": "src/Core/PluginCore/Base/IMessageSendResult.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751199097486, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751199097486, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic interface IMessageSendResult\r\n{\r\n    string? MessageId { get; set; }\r\n    string? ProviderMessageId { get; set; }\r\n    string? Status { get; set; }\r\n    string? ErrorMessage { get; set; }\r\n    Dictionary<string, object>? ResponseData { get; set; }\r\n    Dictionary<string, object>? Metadata { get; set; }\r\n    Dictionary<string, object>? Headers { get; set; }\r\n    Dictionary<string, object>? QueryParameters { get; set; }\r\n    Dictionary<string, object>? FormParameters { get; set; }\r\n    Dictionary<string, object>? Cookies { get; set; }\r\n    Dictionary<string, object>? Files { get; set; }\r\n    Dictionary<string, object>? Session { get; set; }\r\n    Dictionary<string, object>? ServerVariables { get; set; }\r\n    Dictionary<string, object>? ClientVariables { get; set; }\r\n    Dictionary<string, object>? CustomVariables { get; set; }\r\n    Dictionary<string, object>? RequestData { get; set; }\r\n    Dictionary<string, object>? ErrorData { get; set; }\r\n    Dictionary<string, object>? LogData { get; set; }\r\n    Dictionary<string, object>? CustomData { get; set; }\r\n    bool IsSuccess { get; }\r\n    string? Recipient { get; }\r\n    string? Reason { get; }\r\n}\r\n\r\n{\r\n"}]}