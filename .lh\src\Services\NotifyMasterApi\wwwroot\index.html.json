{"sourceFile": "src/Services/NotifyMasterApi/wwwroot/index.html", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1751190488910, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751190495226, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,9 +77,9 @@\n     </style>\n </head>\n <body>\n     <div class=\"container\">\n-        <img src=\"/logo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n+        <img src=\"logo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n         <h1>NotificationService API</h1>\n         <p>A comprehensive notification service supporting Email, SMS, and Push notifications with a powerful plugin architecture.</p>\n         \n         <div class=\"links\">\n"}, {"date": 1751190510682, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,9 +77,9 @@\n     </style>\n </head>\n <body>\n     <div class=\"container\">\n-        <img src=\"logo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n+        <img src=\"/www/logo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n         <h1>NotificationService API</h1>\n         <p>A comprehensive notification service supporting Email, SMS, and Push notifications with a powerful plugin architecture.</p>\n         \n         <div class=\"links\">\n"}, {"date": 1751190520839, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,9 +77,9 @@\n     </style>\n </head>\n <body>\n     <div class=\"container\">\n-        <img src=\"www/logo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n+        <img src=\"wwwlogo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n         <h1>NotificationService API</h1>\n         <p>A comprehensive notification service supporting Email, SMS, and Push notifications with a powerful plugin architecture.</p>\n         \n         <div class=\"links\">\n"}, {"date": 1751190520991, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,9 +77,9 @@\n     </style>\n </head>\n <body>\n     <div class=\"container\">\n-        <img src=\"www/logo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n+        <img src=\"wwlogo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n         <h1>NotificationService API</h1>\n         <p>A comprehensive notification service supporting Email, SMS, and Push notifications with a powerful plugin architecture.</p>\n         \n         <div class=\"links\">\n"}], "date": 1751190488910, "name": "Commit-0", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>NotificationService API</title>\n    <link rel=\"icon\" type=\"image/svg+xml\" href=\"/logo.svg\">\n    <style>\n        body {\n            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            margin: 0;\n            padding: 0;\n            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\n            color: white;\n            min-height: 100vh;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n        \n        .container {\n            text-align: center;\n            max-width: 600px;\n            padding: 2rem;\n        }\n        \n        .logo {\n            width: 120px;\n            height: 120px;\n            margin: 0 auto 2rem;\n            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));\n        }\n        \n        h1 {\n            font-size: 3rem;\n            margin: 0 0 1rem;\n            font-weight: 700;\n            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        }\n        \n        p {\n            font-size: 1.2rem;\n            margin: 0 0 2rem;\n            opacity: 0.9;\n            line-height: 1.6;\n        }\n        \n        .links {\n            display: flex;\n            gap: 1rem;\n            justify-content: center;\n            flex-wrap: wrap;\n        }\n        \n        .link {\n            background: rgba(255, 255, 255, 0.1);\n            backdrop-filter: blur(10px);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n            border-radius: 12px;\n            padding: 1rem 2rem;\n            text-decoration: none;\n            color: white;\n            font-weight: 500;\n            transition: all 0.2s ease;\n        }\n        \n        .link:hover {\n            background: rgba(255, 255, 255, 0.2);\n            transform: translateY(-2px);\n            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\n        }\n        \n        .emoji {\n            font-size: 1.2em;\n            margin-right: 0.5rem;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <img src=\"/logo.png\" alt=\"NotificationService Logo\" class=\"logo\">\n        <h1>NotificationService API</h1>\n        <p>A comprehensive notification service supporting Email, SMS, and Push notifications with a powerful plugin architecture.</p>\n        \n        <div class=\"links\">\n            <a href=\"/scalar/v1\" class=\"link\">\n                <span class=\"emoji\">📚</span>\n                API Documentation\n            </a>\n            <a href=\"/hangfire\" class=\"link\">\n                <span class=\"emoji\">⚙️</span>\n                Hangfire Dashboard\n            </a>\n            <a href=\"/health/ready\" class=\"link\">\n                <span class=\"emoji\">💚</span>\n                Health Check\n            </a>\n            <a href=\"/api/plugins\" class=\"link\">\n                <span class=\"emoji\">🔌</span>\n                Plugins\n            </a>\n        </div>\n    </div>\n</body>\n</html>\n"}]}