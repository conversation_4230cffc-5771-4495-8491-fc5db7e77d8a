{"sourceFile": "src/Plugins/Plugin.Sms.BulkSms/BulkSmsGateway.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1751210535121, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751210549340, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,13 +19,19 @@\n     private readonly string _gatewayId;\n     private string? _username;\n     private string? _password;\n \n-    public BulkSmsGateway(ILogger<BulkSmsGateway> logger) : base(logger)\n+    public BulkSmsGateway(\n+        ILogger<BulkSmsGateway> logger,\n+        IMessageSchedulingService? schedulingService = null,\n+        IMessageStorageService? storageService = null) : base(logger)\n     {\n         _httpClient = GatewayUtilities.CreateHttpClient(\"https://api.bulksms.com/v1/\", TimeSpan.FromSeconds(30));\n         _rateLimitConfig = new RateLimitConfig { MaxRequests = 100, TimeWindow = TimeSpan.FromMinutes(1) }; // Conservative rate limit\n-        \n+        _schedulingService = schedulingService ?? new DefaultMessageSchedulingService(_logger);\n+        _storageService = storageService ?? new DefaultMessageStorageService(_logger);\n+        _gatewayId = \"BulkSMS\";\n+\n         InitializeConfiguration();\n     }\n \n     private void InitializeConfiguration()\n"}, {"date": 1751210570708, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -69,11 +69,16 @@\n                 if (response.IsSuccessStatusCode)\n                 {\n                     var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);\n                     var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);\n-                    \n+\n                     var messageId = responseData.GetProperty(\"id\").GetString() ?? Guid.NewGuid().ToString();\n-                    return GatewayUtilities.CreateSuccessResult(messageId, payload.CorrelationId);\n+                    var result = GatewayUtilities.CreateSuccessResult(messageId, payload.CorrelationId);\n+\n+                    // Store the message for potential resending\n+                    await _storageService.StoreMessageAsync(messageId, _gatewayId, payload, result, cancellationToken);\n+\n+                    return result;\n                 }\n                 else\n                 {\n                     var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);\n"}, {"date": 1751210590206, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -361,20 +361,40 @@\n     #region Message Operations (Not implemented in base class)\n \n     public override async Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)\n     {\n-        // BulkSMS doesn't support native scheduling\n-        throw new NotSupportedException(\"Message scheduling is not supported by BulkSMS gateway\");\n+        // BulkSMS doesn't support native scheduling, so we use our scheduling service\n+        return await _schedulingService.ScheduleMessageAsync(_gatewayId, payload, scheduledTime, cancellationToken);\n     }\n \n     public override async Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)\n     {\n-        throw new NotSupportedException(\"Message scheduling is not supported by BulkSMS gateway\");\n+        // Use our scheduling service to cancel the message\n+        return await _schedulingService.CancelScheduledMessageAsync(scheduledMessageId, cancellationToken);\n     }\n \n     public override async Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default)\n     {\n-        throw new NotSupportedException(\"Message resending is not supported by BulkSMS gateway\");\n+        // Use our storage service to resend the message\n+        var storedMessage = await _storageService.GetStoredMessageAsync(originalMessageId, cancellationToken);\n+        if (storedMessage == null)\n+        {\n+            return GatewayUtilities.CreateFailureResult(null, \"Original message not found\");\n+        }\n+\n+        // Create a new correlation ID for the resend\n+        var resendPayload = storedMessage.Payload with\n+        {\n+            CorrelationId = $\"resend_{originalMessageId}_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}\"\n+        };\n+\n+        // Send the message again\n+        var sendResult = await SendSmsInternalAsync(resendPayload, cancellationToken);\n+\n+        // Update the storage with the resend result\n+        await _storageService.UpdateSendResultAsync(originalMessageId, sendResult, cancellationToken);\n+\n+        return sendResult;\n     }\n \n     public override async Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)\n     {\n"}, {"date": 1751210603717, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -496,15 +496,8 @@\n \n         return bulkSmsPayload;\n     }\n \n-    protected override void Dispose(bool disposing)\n-    {\n-        if (disposing)\n-        {\n-            _httpClient?.Dispose();\n-        }\n-        base.Dispose(disposing);\n-    }\n \n+\n     #endregion\n }\n"}], "date": 1751210535121, "name": "Commit-0", "content": "using Microsoft.Extensions.Logging;\nusing PluginCore.Base;\nusing PluginCore.Models;\nusing PluginCore.Utilities;\nusing System.Text.Json;\nusing System.Text;\n\nnamespace Plugin.Sms.BulkSms;\n\n/// <summary>\n/// BulkSMS gateway implementation with scheduling and storage capabilities.\n/// </summary>\npublic class BulkSmsGateway : SmsGatewayBase\n{\n    private readonly HttpClient _httpClient;\n    private readonly RateLimitConfig _rateLimitConfig;\n    private readonly IMessageSchedulingService _schedulingService;\n    private readonly IMessageStorageService _storageService;\n    private readonly string _gatewayId;\n    private string? _username;\n    private string? _password;\n\n    public BulkSmsGateway(ILogger<BulkSmsGateway> logger) : base(logger)\n    {\n        _httpClient = GatewayUtilities.CreateHttpClient(\"https://api.bulksms.com/v1/\", TimeSpan.FromSeconds(30));\n        _rateLimitConfig = new RateLimitConfig { MaxRequests = 100, TimeWindow = TimeSpan.FromMinutes(1) }; // Conservative rate limit\n        \n        InitializeConfiguration();\n    }\n\n    private void InitializeConfiguration()\n    {\n        // Set default configuration\n        SetConfiguration(\"Username\", \"\");\n        SetConfiguration(\"Password\", \"\");\n        SetConfiguration(\"DefaultSender\", \"\");\n        SetConfiguration(\"MaxMessageLength\", 160);\n        SetConfiguration(\"EnableDeliveryReports\", true);\n        SetConfiguration(\"EnableUnicode\", false);\n    }\n\n    #region SmsGatewayBase Implementation\n\n    protected override async Task<MessageSendResult> SendSmsInternalAsync(MessagePayload payload, CancellationToken cancellationToken)\n    {\n        try\n        {\n            var rateLimitKey = $\"bulksms_{_username}\";\n            \n            return await new Func<Task<MessageSendResult>>(async () =>\n            {\n                var bulkSmsPayload = CreateBulkSmsPayload(payload);\n                var json = JsonSerializer.Serialize(bulkSmsPayload);\n                var content = new StringContent(json, Encoding.UTF8, \"application/json\");\n\n                // Set basic authentication\n                var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($\"{_username}:{_password}\"));\n                _httpClient.DefaultRequestHeaders.Clear();\n                _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Basic {credentials}\");\n\n                var response = await _httpClient.PostAsync(\"messages\", content, cancellationToken);\n                \n                if (response.IsSuccessStatusCode)\n                {\n                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);\n                    var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);\n                    \n                    var messageId = responseData.GetProperty(\"id\").GetString() ?? Guid.NewGuid().ToString();\n                    return GatewayUtilities.CreateSuccessResult(messageId, payload.CorrelationId);\n                }\n                else\n                {\n                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);\n                    _logger.LogError(\"BulkSMS API error: {StatusCode} - {Content}\", response.StatusCode, errorContent);\n                    return GatewayUtilities.CreateFailureResult(payload.CorrelationId, $\"BulkSMS API error: {response.StatusCode}\");\n                }\n            }).ExecuteWithRateLimitAsync(rateLimitKey, _rateLimitConfig, _logger, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to send SMS via BulkSMS\");\n            return GatewayUtilities.CreateFailureResult(payload.CorrelationId, ex.Message);\n        }\n    }\n\n    protected override async Task<IReadOnlyList<MessageSendResult>> SendBulkSmsInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken)\n    {\n        try\n        {\n            var rateLimitKey = $\"bulksms_{_username}\";\n            \n            return await new Func<Task<IReadOnlyList<MessageSendResult>>>(async () =>\n            {\n                var bulkPayloads = payloads.Select(CreateBulkSmsPayload).ToArray();\n                var json = JsonSerializer.Serialize(bulkPayloads);\n                var content = new StringContent(json, Encoding.UTF8, \"application/json\");\n\n                var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($\"{_username}:{_password}\"));\n                _httpClient.DefaultRequestHeaders.Clear();\n                _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Basic {credentials}\");\n\n                var response = await _httpClient.PostAsync(\"messages\", content, cancellationToken);\n                \n                if (response.IsSuccessStatusCode)\n                {\n                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);\n                    var responseArray = JsonSerializer.Deserialize<JsonElement[]>(responseContent);\n                    \n                    var results = new List<MessageSendResult>();\n                    for (int i = 0; i < responseArray.Length; i++)\n                    {\n                        var messageId = responseArray[i].GetProperty(\"id\").GetString() ?? Guid.NewGuid().ToString();\n                        var correlationId = payloads.ElementAtOrDefault(i)?.CorrelationId;\n                        results.Add(GatewayUtilities.CreateSuccessResult(messageId, correlationId));\n                    }\n                    \n                    return results.AsReadOnly();\n                }\n                else\n                {\n                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);\n                    _logger.LogError(\"BulkSMS bulk API error: {StatusCode} - {Content}\", response.StatusCode, errorContent);\n                    \n                    // Return failure results for all messages\n                    return payloads.Select(p => GatewayUtilities.CreateFailureResult(p.CorrelationId, $\"BulkSMS API error: {response.StatusCode}\"))\n                                  .ToList().AsReadOnly();\n                }\n            }).ExecuteWithRateLimitAsync(rateLimitKey, _rateLimitConfig, _logger, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to send bulk SMS via BulkSMS\");\n            return payloads.Select(p => GatewayUtilities.CreateFailureResult(p.CorrelationId, ex.Message))\n                          .ToList().AsReadOnly();\n        }\n    }\n\n    protected override async Task<string?> GetSmsTemplateAsync(string templateId, CancellationToken cancellationToken)\n    {\n        // BulkSMS doesn't have a native template system, so we'll implement a simple local template store\n        var templates = new Dictionary<string, string>\n        {\n            [\"welcome\"] = \"Welcome {name}! Your account has been created successfully.\",\n            [\"verification\"] = \"Your verification code is: {code}\",\n            [\"reminder\"] = \"Hi {name}, this is a reminder about {event} on {date}.\",\n            [\"alert\"] = \"ALERT: {message}. Please take immediate action.\"\n        };\n\n        return templates.TryGetValue(templateId.ToLowerInvariant(), out var template) ? template : null;\n    }\n\n    protected override async Task<IEnumerable<string>> ValidateSmsSpecificAsync(MessagePayload payload, CancellationToken cancellationToken)\n    {\n        var errors = new List<string>();\n        \n        // Validate credentials\n        if (string.IsNullOrEmpty(_username) || string.IsNullOrEmpty(_password))\n        {\n            errors.Add(\"BulkSMS username and password are required\");\n        }\n        \n        // Validate phone number format for BulkSMS\n        var phoneNumber = payload.Recipient;\n        if (!phoneNumber.StartsWith(\"+\"))\n        {\n            errors.Add(\"Phone number must be in international format starting with +\");\n        }\n        \n        // Check message length based on encoding\n        var enableUnicode = GetConfiguration<bool>(\"EnableUnicode\");\n        var maxLength = enableUnicode ? 70 : 160; // Unicode SMS are shorter\n        \n        if (payload.Content.Length > maxLength)\n        {\n            errors.Add($\"Message exceeds maximum length of {maxLength} characters for {(enableUnicode ? \"Unicode\" : \"standard\")} SMS\");\n        }\n        \n        return errors;\n    }\n\n    #endregion\n\n    #region Admin Implementation\n\n    protected override async Task<IEnumerable<GatewayConfiguration>> GetSmsProviderConfigurationsAsync(CancellationToken cancellationToken)\n    {\n        return new[]\n        {\n            new GatewayConfiguration(\"Username\", \"BulkSMS Username\", \"\", \"Your BulkSMS username\", true, false),\n            new GatewayConfiguration(\"Password\", \"BulkSMS Password\", \"\", \"Your BulkSMS password\", true, true),\n            new GatewayConfiguration(\"EnableUnicode\", \"Enable Unicode SMS\", GetConfiguration<bool>(\"EnableUnicode\").ToString(), \"Enable Unicode SMS support\", false, false, \"bool\")\n        };\n    }\n\n    protected override async Task UpdateSmsProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken)\n    {\n        switch (setting.Key)\n        {\n            case \"Username\":\n                if (!string.IsNullOrEmpty(setting.Value))\n                {\n                    _username = setting.Value;\n                    SetConfiguration(\"Username\", setting.Value);\n                }\n                break;\n            case \"Password\":\n                if (!string.IsNullOrEmpty(setting.Value))\n                {\n                    _password = setting.Value;\n                    SetConfiguration(\"Password\", setting.Value);\n                }\n                break;\n            case \"EnableUnicode\":\n                if (bool.TryParse(setting.Value, out var enableUnicode))\n                    SetConfiguration(\"EnableUnicode\", enableUnicode);\n                break;\n        }\n    }\n\n    protected override async Task<OperationResult> TestSmsConfigurationAsync(CancellationToken cancellationToken)\n    {\n        try\n        {\n            if (string.IsNullOrEmpty(_username) || string.IsNullOrEmpty(_password))\n            {\n                return GatewayUtilities.CreateOperationResult(false, \"Username and password are required\");\n            }\n\n            // Test credentials by getting account balance\n            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($\"{_username}:{_password}\"));\n            _httpClient.DefaultRequestHeaders.Clear();\n            _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Basic {credentials}\");\n\n            var response = await _httpClient.GetAsync(\"profile\", cancellationToken);\n            \n            if (response.IsSuccessStatusCode)\n            {\n                return GatewayUtilities.CreateOperationResult(true, \"BulkSMS configuration is valid\");\n            }\n            else\n            {\n                return GatewayUtilities.CreateOperationResult(false, $\"BulkSMS API test failed: {response.StatusCode}\");\n            }\n        }\n        catch (Exception ex)\n        {\n            return GatewayUtilities.CreateOperationResult(false, \"Configuration test failed\", ex);\n        }\n    }\n\n    protected override async Task<PluginManifest> GetSmsManifestAsync(CancellationToken cancellationToken)\n    {\n        return new PluginManifest(\n            Name: \"BulkSMS Gateway\",\n            Version: \"1.0.0\",\n            Description: \"SMS gateway implementation for BulkSMS service\",\n            Author: \"NotificationService Team\",\n            Type: \"SMS\",\n            Provider: \"BulkSMS\",\n            AssemblyName: \"Plugin.Sms.BulkSms.dll\",\n            EntryPoint: \"Plugin.Sms.BulkSms.BulkSmsGateway\",\n            Dependencies: new List<PluginDependency>(),\n            Configuration: new Dictionary<string, PluginConfigurationItem>\n            {\n                [\"Username\"] = new(\"string\", \"BulkSMS Username\", null, null, true, false),\n                [\"Password\"] = new(\"string\", \"BulkSMS Password\", null, null, true, true),\n                [\"EnableUnicode\"] = new(\"bool\", \"Enable Unicode SMS\", null, false, false, false)\n            },\n            SupportedFeatures: new List<string> { \"SendSMS\", \"BulkSMS\", \"DeliveryReports\", \"Unicode\" }\n        );\n    }\n\n    #endregion\n\n    #region Metrics Implementation (Mock implementations)\n\n    protected override async Task<GatewayStatusReport> GetSmsStatusReportAsync(CancellationToken cancellationToken)\n    {\n        return new GatewayStatusReport(\n            IsHealthy: true,\n            Status: \"Operational\",\n            LastChecked: DateTimeOffset.UtcNow,\n            ResponseTime: TimeSpan.FromMilliseconds(200)\n        );\n    }\n\n    protected override async Task<IReadOnlyList<DeliveryResult>> GetSmsDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken)\n    {\n        var results = new List<DeliveryResult>();\n        for (int i = 0; i < Math.Min(maxItems, 10); i++)\n        {\n            results.Add(GatewayUtilities.CreateMockDeliveryResult($\"bulk_{Guid.NewGuid():N}\"));\n        }\n        return results.AsReadOnly();\n    }\n\n    protected override async Task<UsageMetrics> GetSmsUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return GatewayUtilities.CreateMockUsageMetrics(from, to);\n    }\n\n    // Additional mock implementations for other metrics methods...\n    protected override async Task<IReadOnlyList<GatewayErrorEntry>> GetSmsErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new List<GatewayErrorEntry>().AsReadOnly();\n    }\n\n    protected override async Task<PerformanceSnapshot> GetSmsPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken)\n    {\n        return new PerformanceSnapshot(DateTimeOffset.UtcNow, 100, 95, TimeSpan.FromMilliseconds(200));\n    }\n\n    protected override async Task<SlaReport> GetSmsSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new SlaReport(99.5, TimeSpan.FromMilliseconds(200), 0.5);\n    }\n\n    protected override async Task<LatencyMetrics> GetSmsLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new LatencyMetrics(TimeSpan.FromMilliseconds(200), TimeSpan.FromMilliseconds(300), TimeSpan.FromMilliseconds(100));\n    }\n\n    protected override async Task<IReadOnlyList<TrafficTrend>> GetSmsTrafficTrendsAsync(string granularity, CancellationToken cancellationToken)\n    {\n        return new List<TrafficTrend>().AsReadOnly();\n    }\n\n    protected override async Task<IReadOnlyList<AnomalyDetectionResult>> GetSmsAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new List<AnomalyDetectionResult>().AsReadOnly();\n    }\n\n    protected override async Task<GeneratedReport> GenerateSmsMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken)\n    {\n        return new GeneratedReport(\"sms_metrics.csv\", \"text/csv\", Array.Empty<byte>());\n    }\n\n    protected override async Task<IReadOnlyList<RetryAttemptInfo>> GetSmsRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken)\n    {\n        return new List<RetryAttemptInfo>().AsReadOnly();\n    }\n\n    protected override async Task<IReadOnlyList<ConfigurationImpactRecord>> GetSmsChangeImpactHistoryAsync(CancellationToken cancellationToken)\n    {\n        return new List<ConfigurationImpactRecord>().AsReadOnly();\n    }\n\n    #endregion\n\n    #region Message Operations (Not implemented in base class)\n\n    public override async Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)\n    {\n        // BulkSMS doesn't support native scheduling\n        throw new NotSupportedException(\"Message scheduling is not supported by BulkSMS gateway\");\n    }\n\n    public override async Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)\n    {\n        throw new NotSupportedException(\"Message scheduling is not supported by BulkSMS gateway\");\n    }\n\n    public override async Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default)\n    {\n        throw new NotSupportedException(\"Message resending is not supported by BulkSMS gateway\");\n    }\n\n    public override async Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($\"{_username}:{_password}\"));\n            _httpClient.DefaultRequestHeaders.Clear();\n            _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Basic {credentials}\");\n\n            var response = await _httpClient.GetAsync($\"messages/{messageId}\", cancellationToken);\n            \n            if (response.IsSuccessStatusCode)\n            {\n                var content = await response.Content.ReadAsStringAsync(cancellationToken);\n                var messageData = JsonSerializer.Deserialize<JsonElement>(content);\n                var status = messageData.GetProperty(\"status\").GetProperty(\"type\").GetString() ?? \"Unknown\";\n                \n                return new MessageStatusInfo(messageId, status, DateTimeOffset.UtcNow);\n            }\n            \n            return new MessageStatusInfo(messageId, \"Unknown\", DateTimeOffset.UtcNow);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to get message status for {MessageId}\", messageId);\n            return new MessageStatusInfo(messageId, \"Error\", DateTimeOffset.UtcNow, ex.Message);\n        }\n    }\n\n    public override async Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default)\n    {\n        var statusInfo = await GetMessageStatusAsync(messageId, cancellationToken);\n        return new DeliveryReceipt(messageId, statusInfo.Status, statusInfo.Timestamp);\n    }\n\n    public override async Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default)\n    {\n        var json = JsonSerializer.Serialize(rawPayload);\n        var content = new StringContent(json, Encoding.UTF8, \"application/json\");\n\n        var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($\"{_username}:{_password}\"));\n        _httpClient.DefaultRequestHeaders.Clear();\n        _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Basic {credentials}\");\n\n        var response = await _httpClient.PostAsync(\"messages\", content, cancellationToken);\n        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);\n\n        return new RawGatewayResponse(response.IsSuccessStatusCode, (int)response.StatusCode, responseContent);\n    }\n\n    public override async Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)\n    {\n        var bulkSmsPayload = CreateBulkSmsPayload(payload);\n        var validation = await ValidateMessageAsync(payload, cancellationToken);\n        \n        return new PreparedMessage(\n            bulkSmsPayload,\n            payload.Headers ?? new Dictionary<string, string>(),\n            validation\n        );\n    }\n\n    public override async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            if (string.IsNullOrEmpty(_username) || string.IsNullOrEmpty(_password))\n                return false;\n\n            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($\"{_username}:{_password}\"));\n            _httpClient.DefaultRequestHeaders.Clear();\n            _httpClient.DefaultRequestHeaders.Add(\"Authorization\", $\"Basic {credentials}\");\n\n            var response = await _httpClient.GetAsync(\"profile\", cancellationToken);\n            return response.IsSuccessStatusCode;\n        }\n        catch\n        {\n            return false;\n        }\n    }\n\n    #endregion\n\n    #region Helper Methods\n\n    private object CreateBulkSmsPayload(MessagePayload payload)\n    {\n        var sender = payload.From ?? GetDefaultSender();\n        var enableUnicode = GetConfiguration<bool>(\"EnableUnicode\");\n\n        var bulkSmsPayload = new\n        {\n            to = payload.Recipient,\n            body = payload.Content,\n            from = !string.IsNullOrEmpty(sender) ? sender : null,\n            encoding = enableUnicode ? \"UNICODE\" : \"TEXT\"\n        };\n\n        return bulkSmsPayload;\n    }\n\n    protected override void Dispose(bool disposing)\n    {\n        if (disposing)\n        {\n            _httpClient?.Dispose();\n        }\n        base.Dispose(disposing);\n    }\n\n    #endregion\n}\n"}]}