{"sourceFile": "src/Core/PluginCore/Base/MetricsModels.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751200966694, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751200966694, "name": "Commit-0", "content": "\nusing System;\nusing System.Collections.Generic;\n\nnamespace PluginCore.Models\n{\n   \n}\n"}]}