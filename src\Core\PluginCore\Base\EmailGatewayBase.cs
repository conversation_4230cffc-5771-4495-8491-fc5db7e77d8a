using PluginCore.Interfaces;
using PluginCore.Models;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace PluginCore.Base;

/// <summary>
/// Abstract base class for Email gateway plugins.
/// Provides common Email-specific functionality, validation, and helper methods.
/// Implements all three gateway interfaces: Message, Admin, and Metrics.
/// </summary>
public abstract class EmailGatewayBase : IEmailPlugin, IGatewayAdminPluginType, IGatewayMetricsPluginType
{
    protected readonly ILogger _logger;
    protected readonly Dictionary<string, object> _configuration = new();

    protected EmailGatewayBase(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region IGatewayMessagePluginType Implementation

    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                );
            }

            var normalizedPayload = NormalizeEmailPayload(payload);
            return await SendEmailInternalAsync(normalizedPayload, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email message to {Recipient}", payload.Recipient);
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed",
                CorrelationId: payload.CorrelationId
            );
        }
    }

    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<MessageSendResult>();
        var validPayloads = new List<MessagePayload>();

        // Validate all payloads first
        foreach (var payload in payloads)
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (validationResult.IsValid)
            {
                validPayloads.Add(NormalizeEmailPayload(payload));
            }
            else
            {
                results.Add(new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                ));
            }
        }

        if (validPayloads.Any())
        {
            var bulkResults = await SendBulkEmailInternalAsync(validPayloads, cancellationToken);
            results.AddRange(bulkResults);
        }

        return results.AsReadOnly();
    }

    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)
    {
        var template = await GetEmailTemplateAsync(templateId, cancellationToken);
        if (template == null)
        {
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed"
            );
        }

        var content = ProcessEmailTemplate(template.Content, templateData);
        var subject = ProcessEmailTemplate(template.Subject ?? "No Subject", templateData);
        
        var payload = new MessagePayload(
            Recipient: recipient.Email ?? recipient.Address,
            Content: content,
            From: GetDefaultSender(),
            Subject: subject,
            Headers: new Dictionary<string, string> { ["TemplateId"] = templateId }
        );

        return await SendMessageAsync(payload, cancellationToken);
    }

    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);

    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);

    public virtual async Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        // Validate email address
        if (!IsValidEmailAddress(payload.Recipient))
        {
            errors.Add("Invalid email address format");
        }

        // Validate message content
        if (string.IsNullOrWhiteSpace(payload.Content))
        {
            errors.Add("Message content cannot be empty");
        }

        // Validate subject (optional but recommended)
        if (string.IsNullOrWhiteSpace(payload.Subject))
        {
            errors.Add("Email subject is recommended");
        }

        // Provider-specific validation
        var providerErrors = await ValidateEmailSpecificAsync(payload, cancellationToken);
        errors.AddRange(providerErrors);

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            ErrorMessage = errors.Any() ? string.Join("; ", errors) : null
        };
    }

    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);

    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);

    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    #endregion

    #region IGatewayAdminPluginType Implementation

    public virtual async Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        var configs = new List<GatewayConfiguration>();
        
        // Add Email-specific configuration items
        configs.Add(new GatewayConfiguration("DefaultSender", "Default Email sender address", GetConfiguration<string>("DefaultSender") ?? "", "Default email address for sending emails", false, false));
        configs.Add(new GatewayConfiguration("DefaultSenderName", "Default sender name", GetConfiguration<string>("DefaultSenderName") ?? "", "Default display name for email sender", false, false));
        configs.Add(new GatewayConfiguration("EnableHtml", "Enable HTML emails", GetConfiguration<bool>("EnableHtml").ToString() ?? "true", "Enable or disable HTML email support", false, false, "bool"));
        configs.Add(new GatewayConfiguration("EnableAttachments", "Enable email attachments", GetConfiguration<bool>("EnableAttachments").ToString() ?? "true", "Enable or disable email attachment support", false, false, "bool"));
        
        // Add provider-specific configurations
        var providerConfigs = await GetEmailProviderConfigurationsAsync(cancellationToken);
        configs.AddRange(providerConfigs);
        
        return configs.AsReadOnly();
    }

    public virtual async Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default)
    {
        try
        {
            foreach (var setting in settingsToUpdate)
            {
                switch (setting.Key)
                {
                    case "DefaultSender":
                        if (setting.Value != null && IsValidEmailAddress(setting.Value))
                            SetConfiguration("DefaultSender", setting.Value);
                        break;
                    case "DefaultSenderName":
                        if (setting.Value != null)
                            SetConfiguration("DefaultSenderName", setting.Value);
                        break;
                    case "EnableHtml":
                        if (bool.TryParse(setting.Value, out var enableHtml))
                            SetConfiguration("EnableHtml", enableHtml);
                        break;
                    case "EnableAttachments":
                        if (bool.TryParse(setting.Value, out var enableAttachments))
                            SetConfiguration("EnableAttachments", enableAttachments);
                        break;
                    default:
                        await UpdateEmailProviderConfigurationAsync(setting, cancellationToken);
                        break;
                }
            }

            return new OperationResult(true, "Configuration updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update Email gateway configuration");
            return new OperationResult(false, "Failed to update configuration", new[] { new ErrorDetail("ConfigurationError", ex.Message) });
        }
    }

    public virtual async Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Test basic Email functionality
            var isAvailable = await IsAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                return new OperationResult(false, "Email gateway is not available");
            }

            // Perform Email-specific configuration tests
            var testResult = await TestEmailConfigurationAsync(cancellationToken);
            return testResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Email configuration test failed");
            return new OperationResult(false, "Configuration test failed", new[] { new ErrorDetail("TestError", ex.Message) });
        }
    }

    public virtual async Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default)
    {
        return await GetEmailManifestAsync(cancellationToken);
    }

    #endregion

    #region IGatewayMetricsPluginType Implementation

    public virtual async Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default)
    {
        return await GetEmailStatusReportAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default)
    {
        return await GetEmailDeliveryReportsAsync(maxItems, cancellationToken);
    }

    public virtual async Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetEmailUsageMetricsAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetEmailErrorLogAsync(from, to, cancellationToken);
    }

    public virtual async Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default)
    {
        return await GetEmailPerformanceSnapshotAsync(resolution, cancellationToken);
    }

    public virtual async Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetEmailSlaReportAsync(from, to, cancellationToken);
    }

    public virtual async Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetEmailLatencyMetricsAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = "daily", CancellationToken cancellationToken = default)
    {
        return await GetEmailTrafficTrendsAsync(granularity, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetEmailAnomalyReportAsync(from, to, cancellationToken);
    }

    public virtual async Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default)
    {
        return await GenerateEmailMetricsReportAsync(options, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetEmailRetryHistoryAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default)
    {
        return await GetEmailChangeImpactHistoryAsync(cancellationToken);
    }

    #endregion

    #region Email-Specific Abstract Methods

    /// <summary>
    /// Sends a single email message using the provider's API.
    /// </summary>
    protected abstract Task<MessageSendResult> SendEmailInternalAsync(MessagePayload payload, CancellationToken cancellationToken);

    /// <summary>
    /// Sends multiple email messages in a single operation.
    /// </summary>
    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkEmailInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);

    /// <summary>
    /// Gets an email template by ID.
    /// </summary>
    protected abstract Task<EmailTemplate?> GetEmailTemplateAsync(string templateId, CancellationToken cancellationToken);

    /// <summary>
    /// Performs provider-specific email validation.
    /// </summary>
    protected abstract Task<IEnumerable<string>> ValidateEmailSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);

    #endregion

    #region Email-Specific Abstract Methods for Admin

    /// <summary>
    /// Gets email provider-specific configuration items.
    /// </summary>
    protected abstract Task<IEnumerable<GatewayConfiguration>> GetEmailProviderConfigurationsAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Updates email provider-specific configuration.
    /// </summary>
    protected abstract Task UpdateEmailProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken);

    /// <summary>
    /// Tests email provider-specific configuration.
    /// </summary>
    protected abstract Task<OperationResult> TestEmailConfigurationAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Gets the email plugin manifest.
    /// </summary>
    protected abstract Task<PluginManifest> GetEmailManifestAsync(CancellationToken cancellationToken);

    #endregion

    #region Email-Specific Abstract Methods for Metrics

    /// <summary>
    /// Gets email-specific status report.
    /// </summary>
    protected abstract Task<GatewayStatusReport> GetEmailStatusReportAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Gets email delivery reports.
    /// </summary>
    protected abstract Task<IReadOnlyList<DeliveryResult>> GetEmailDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email usage metrics.
    /// </summary>
    protected abstract Task<UsageMetrics> GetEmailUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email error logs.
    /// </summary>
    protected abstract Task<IReadOnlyList<GatewayErrorEntry>> GetEmailErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email performance snapshot.
    /// </summary>
    protected abstract Task<PerformanceSnapshot> GetEmailPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email SLA report.
    /// </summary>
    protected abstract Task<SlaReport> GetEmailSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email latency metrics.
    /// </summary>
    protected abstract Task<LatencyMetrics> GetEmailLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email traffic trends.
    /// </summary>
    protected abstract Task<IReadOnlyList<TrafficTrend>> GetEmailTrafficTrendsAsync(string granularity, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email anomaly report.
    /// </summary>
    protected abstract Task<IReadOnlyList<AnomalyDetectionResult>> GetEmailAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Generates email metrics report.
    /// </summary>
    protected abstract Task<GeneratedReport> GenerateEmailMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email retry history.
    /// </summary>
    protected abstract Task<IReadOnlyList<RetryAttemptInfo>> GetEmailRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets email configuration change impact history.
    /// </summary>
    protected abstract Task<IReadOnlyList<ConfigurationImpactRecord>> GetEmailChangeImpactHistoryAsync(CancellationToken cancellationToken);

    #endregion

    #region Email-Specific Helper Methods

    /// <summary>
    /// Validates email address format.
    /// </summary>
    protected virtual bool IsValidEmailAddress(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            // Use a more comprehensive email validation regex
            var emailRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.IgnoreCase);
            return emailRegex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Normalizes email payload for consistent processing.
    /// </summary>
    protected virtual MessagePayload NormalizeEmailPayload(MessagePayload payload)
    {
        var normalizedRecipient = payload.Recipient.Trim().ToLowerInvariant();
        var normalizedSubject = payload.Subject?.Trim() ?? "No Subject";

        return payload with
        {
            Recipient = normalizedRecipient,
            Subject = normalizedSubject
        };
    }

    /// <summary>
    /// Processes email template with data substitution.
    /// </summary>
    protected virtual string ProcessEmailTemplate(string template, IDictionary<string, string> templateData)
    {
        var result = template;
        foreach (var kvp in templateData)
        {
            result = result.Replace($"{{{kvp.Key}}}", kvp.Value);
        }
        return result;
    }

    /// <summary>
    /// Gets the default sender for email messages.
    /// </summary>
    protected virtual string? GetDefaultSender()
    {
        return _configuration.TryGetValue("DefaultSender", out var sender) ? sender?.ToString() : null;
    }

    /// <summary>
    /// Gets the default sender name for email messages.
    /// </summary>
    protected virtual string? GetDefaultSenderName()
    {
        return _configuration.TryGetValue("DefaultSenderName", out var senderName) ? senderName?.ToString() : null;
    }

    /// <summary>
    /// Checks if HTML emails are enabled.
    /// </summary>
    protected virtual bool IsHtmlEnabled()
    {
        return GetConfiguration<bool>("EnableHtml");
    }

    /// <summary>
    /// Checks if email attachments are enabled.
    /// </summary>
    protected virtual bool AreAttachmentsEnabled()
    {
        return GetConfiguration<bool>("EnableAttachments");
    }

    /// <summary>
    /// Sets configuration value.
    /// </summary>
    protected virtual void SetConfiguration(string key, object value)
    {
        _configuration[key] = value;
    }

    /// <summary>
    /// Gets configuration value.
    /// </summary>
    protected virtual T? GetConfiguration<T>(string key)
    {
        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    #endregion
}

/// <summary>
/// Represents an email template with subject and content.
/// </summary>
public record EmailTemplate(string Subject, string Content);
