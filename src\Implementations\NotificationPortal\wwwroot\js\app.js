// NotificationService Portal JavaScript Utilities

window.notificationPortal = {
    // Theme management
    theme: {
        setTheme: function(isDark) {
            const body = document.body;
            if (isDark) {
                body.classList.add('mud-theme-dark');
                body.classList.remove('mud-theme-light');
            } else {
                body.classList.add('mud-theme-light');
                body.classList.remove('mud-theme-dark');
            }
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
        },
        
        getTheme: function() {
            return localStorage.getItem('theme') || 'light';
        },
        
        initTheme: function() {
            const savedTheme = this.getTheme();
            this.setTheme(savedTheme === 'dark');
        }
    },

    // Local storage helpers
    storage: {
        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('Failed to save to localStorage:', e);
                return false;
            }
        },
        
        get: function(key) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : null;
            } catch (e) {
                console.error('Failed to read from localStorage:', e);
                return null;
            }
        },
        
        remove: function(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('Failed to remove from localStorage:', e);
                return false;
            }
        }
    },

    // Clipboard utilities
    clipboard: {
        copy: async function(text) {
            try {
                await navigator.clipboard.writeText(text);
                return true;
            } catch (e) {
                console.error('Failed to copy to clipboard:', e);
                return false;
            }
        }
    },

    // Download utilities
    download: {
        text: function(filename, content) {
            const element = document.createElement('a');
            element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
            element.setAttribute('download', filename);
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        },
        
        json: function(filename, data) {
            const content = JSON.stringify(data, null, 2);
            this.text(filename, content);
        }
    },

    // Notification utilities
    notifications: {
        requestPermission: async function() {
            if ('Notification' in window) {
                const permission = await Notification.requestPermission();
                return permission === 'granted';
            }
            return false;
        },
        
        show: function(title, options = {}) {
            if ('Notification' in window && Notification.permission === 'granted') {
                return new Notification(title, {
                    icon: '/favicon.png',
                    badge: '/favicon.png',
                    ...options
                });
            }
            return null;
        }
    },

    // SignalR connection management
    signalR: {
        connection: null,
        
        start: async function(hubUrl) {
            if (this.connection) {
                return this.connection;
            }
            
            try {
                this.connection = new signalR.HubConnectionBuilder()
                    .withUrl(hubUrl)
                    .withAutomaticReconnect()
                    .build();
                
                await this.connection.start();
                console.log('SignalR connected');
                return this.connection;
            } catch (e) {
                console.error('SignalR connection failed:', e);
                return null;
            }
        },
        
        stop: async function() {
            if (this.connection) {
                await this.connection.stop();
                this.connection = null;
                console.log('SignalR disconnected');
            }
        },
        
        on: function(methodName, callback) {
            if (this.connection) {
                this.connection.on(methodName, callback);
            }
        },
        
        off: function(methodName) {
            if (this.connection) {
                this.connection.off(methodName);
            }
        },
        
        invoke: async function(methodName, ...args) {
            if (this.connection) {
                try {
                    return await this.connection.invoke(methodName, ...args);
                } catch (e) {
                    console.error('SignalR invoke failed:', e);
                    return null;
                }
            }
            return null;
        }
    },

    // Utility functions
    utils: {
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },
        
        formatBytes: function(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        },
        
        formatNumber: function(num) {
            return new Intl.NumberFormat().format(num);
        },
        
        formatDate: function(date, options = {}) {
            return new Intl.DateTimeFormat('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                ...options
            }).format(new Date(date));
        }
    }
};

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', function() {
    window.notificationPortal.theme.initTheme();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('Page hidden');
    } else {
        console.log('Page visible');
    }
});

// Global error handler
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
});
