using System.Reflection;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using NotifyMasterApi.Data;
using NotifyMasterApi.Services;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Features.Setup;
using Serilog;
using Serilog.Events;
using Hangfire;
using Hangfire.InMemory;
using FastEndpoints;
using Scalar.AspNetCore;
using Hangfire.Dashboard;
using EmailService.Library.Extensions;
using SmsService.Library.Extensions;
using PushNotificationService.Library.Extensions;
using PluginCore.Extensions;
using PluginCore.Services;
using PluginCore.Interfaces;
using NotifyMasterApi.Services;
using NotifyMasterApi.Authorization;
using NotifyMasterApi.Infrastructure;
using NotifyMaster.Database;
using NotifyMaster.Database.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.EntityFrameworkCore;
using Hangfire;
using Hangfire.PostgreSql;
using System.Text;

namespace NotifyMasterApi.Ioc;

public static class Configurator
{
    public static void InjectService(this IServiceCollection services , IConfiguration configuration)
    {
        // Add FastEndpoints
        services.AddFastEndpoints();

        // Add MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Program).Assembly));

        // Add authorization services
        services.AddAuthorization();

        // Add OpenAPI with Scalar
        services.AddOpenApi(options =>
        {
            options.AddDocumentTransformer((document, context, cancellationToken) =>
            {
                document.Info = new()
                {
                    Title = "NotificationService API",
                    Version = "v2.0.0",
                    Description = """
                        # 🔔 NotificationService API

                        A comprehensive notification service supporting **Email**, **SMS**, and **Push notifications** with a powerful plugin architecture.

                        ## 🚀 Features

                        - **Multi-Channel Support**: Email, SMS, Push Notifications, and App Messaging
                        - **Plugin Architecture**: Extensible with custom providers
                        - **Real-time Processing**: Background job processing with Hangfire
                        - **Health Monitoring**: Built-in health checks and metrics
                        - **Multi-Tenancy**: Support for multiple tenants and whitelabeling
                        - **Template Engine**: Dynamic message templating
                        - **Webhook Support**: Real-time event notifications

                        ## 🔧 Quick Start

                        1. **Send an Email**: `POST /api/email/send`
                        2. **Send SMS**: `POST /api/sms/send`
                        3. **Send Push Notification**: `POST /api/push/send`
                        4. **Check Health**: `GET /health/ready`
                        5. **View Plugins**: `GET /api/plugins`

                        ## 📚 Documentation

                        - **API Reference**: This interactive documentation
                        - **Hangfire Dashboard**: [/hangfire](/hangfire) - Background job monitoring
                        - **Health Checks**: [/health/ready](/health/ready) - System health status

                        ## 🔐 Authentication

                        Most endpoints require authentication. Use the **Authorize** button above to configure your API key.

                        ## 💡 Support

                        For support and questions, visit our [GitHub repository](https://github.com/notificationservice).
                        """,
                    Contact = new()
                    {
                        Name = "NotificationService Team",
                        Url = new Uri("https://github.com/notificationservice"),
                        Email = "<EMAIL>"
                    },
                    License = new()
                    {
                        Name = "MIT License",
                        Url = new Uri("https://opensource.org/licenses/MIT")
                    }
                };

                document.Servers = new List<Microsoft.OpenApi.Models.OpenApiServer>
                {
                    new() { Url = "http://localhost:5120", Description = "🔧 Development Server" },
                    new() { Url = "https://api.notificationservice.com", Description = "🚀 Production Server" }
                };

                return Task.CompletedTask;
            });
        });
        services.AddEndpointsApiExplorer();

        // Add Entity Framework with In-Memory database (simplified)
        services.AddDbContext<NotificationDbContext>(options =>
            options.UseInMemoryDatabase("NotificationService"));

        // Add Hangfire for background jobs
        services.AddHangfire(config => config
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseInMemoryStorage());

        services.AddHangfireServer();

        // Add new PluginCore system (plugins are optional)
        services.AddCompleteGatewayPluginSystem(
            pluginDirectory: "plugins",
            autoLoad: false  // Don't auto-load, make plugins optional
        );

        // Add library services (fallback when no plugins)
        services.AddEmailService(configuration);
        services.AddSmsService();
        services.AddPushNotificationService();

        // Add simplified services
        services.AddScoped<IEmailGateway, EmailGateway>();
        services.AddScoped<ISmsGateway, SmsGateway>();
        services.AddScoped<IPushGateway, PushGateway>();
        services.AddScoped<INotificationLoggingService, NotificationLoggingService>();

        // Add advanced services
        services.AddScoped<IWebhookQueueService, WebhookQueueService>();
        services.AddScoped<IEventStreamService, EventStreamService>();
        services.AddScoped<IDeadLetterQueueService, DeadLetterQueueService>();
        services.AddScoped<IValidationService, ValidationService>();
        services.AddScoped<ITemplateRenderingService, TemplateRenderingService>();
        services.AddScoped<ISchedulingService, SchedulingService>();
        services.AddScoped<ISetupService, SetupService>();

        // Add database services
        var connectionString = configuration.GetConnectionString("DefaultConnection") ??
                               "Host=localhost;Database=notifymaster;Username=********;Password=********";

        services.AddDbContext<NotifyMasterDbContext>(options =>
        {
            if (connectionString.Contains("Host=") || connectionString.Contains("Server="))
            {
                options.UseNpgsql(connectionString);
            }
            else
            {
                // Fallback to in-memory for development
                options.UseInMemoryDatabase("NotifyMaster");
            }
        });

        // Add service implementations
        services.AddScoped<ITenantService, TenantService>();
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IRoleService, RoleService>();
        services.AddScoped<IPermissionService, PermissionService>();

        // Add multi-tenancy services
        services.AddTenantContext();
        services.AddTenantAwarePluginManager();
        services.AddAuthenticationServices(configuration);
        services.AddAuthorizationPolicies();

        // Add Hangfire
        services.AddHangfire(config =>
        {
            if (connectionString.Contains("Host=") || connectionString.Contains("Server="))
            {
                config.UsePostgreSqlStorage(connectionString);
            }
            else
            {
                config.UseInMemoryStorage();
            }
        });
        services.AddHangfireServer();

        // Add startup service
        services.AddStartupService();

        // Add HttpClient for plugins and webhooks
        services.AddHttpClient();

        // Add JWT authentication
        var jwtSettings = configuration.GetSection("Jwt");
        var secretKey = jwtSettings["SecretKey"] ?? "NotifyMaster-Super-Secret-Key-That-Should-Be-Changed-In-Production";
        var key = Encoding.UTF8.GetBytes(secretKey);

        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = jwtSettings["Issuer"] ?? "NotifyMaster",
                ValidateAudience = true,
                ValidAudience = jwtSettings["Audience"] ?? "NotifyMaster",
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };
        });

        // Add health checks
        services.AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy(), tags: new[] { "ready" });

        AddLogging(configuration);
    }
    
    public static void ConfigurePipeline(this WebApplication app)
    {
        // Configure Hangfire Dashboard
        app.UseHangfireDashboard("/hangfire", new DashboardOptions
        {
            Authorization = new[] { new HangfireAuthorizationFilter() }
        });

        // Configure authentication and authorization
        app.UseAuthentication();
        app.UseAuthorization();

        // Configure tenant context middleware
        app.UseTenantContext();

        // Configure Hangfire Dashboard
        app.UseHangfireDashboard("/hangfire", new DashboardOptions
        {
            Authorization = new[] { new HangfireAuthorizationFilter() }
        });

        // Configure FastEndpoints
        app.UseFastEndpoints();

        // Configure static files for assets
        app.UseStaticFiles();

        // Configure default files
        app.UseDefaultFiles(new DefaultFilesOptions
        {
            DefaultFileNames = { "index.html" }
        });

        // Configure OpenAPI and Scalar UI with custom branding
        app.MapOpenApi();
        app.MapScalarApiReference(options =>
        {
            options
                .WithTitle("🔔 NotificationService API")
                .WithTheme(ScalarTheme.Purple)
                .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient)
                .WithFavicon("/favicon.ico")
                .WithCustomCss(File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "scalar-custom.css")));
        });

        // Add API endpoint for project configuration
        app.MapGet("/api/config", () => new
        {
            Name = "NotificationService",
            Version = "2.0.0",
            Description = "Modern plugin-based notification service with comprehensive admin and metrics",
            Architecture = "FastEndpoints + Plugin System",
            Features = new[] {
                "Email", "SMS", "Push", "WhatsApp", "Slack",
                "Plugin Management", "Multi-Tenancy", "Health Monitoring",
                "Metrics & Analytics", "Feature Detection"
            },
            Endpoints = new
            {
                Health = "/health/ready",
                Email = "/api/email/send",
                SMS = "/api/sms/send",
                Push = "/api/push/send",
                Plugins = "/api/plugins",
                Tenants = "/api/tenants",
                Features = "/api/system/features",
                Admin = "/api/admin",
                Documentation = "/scalar/v1"
            },
            Plugins = new
            {
                Email = new[] { "SendGrid", "Mailgun" },
                SMS = new[] { "Twilio", "BulkSMS", "Clickatel" },
                Push = new[] { "Firebase FCM" },
                Messaging = new[] { "WhatsApp Business", "Slack" },
                System = new[] { "MultiTenancy" }
            }
        }).WithTags("Configuration");

        // Health checks
        app.MapHealthChecks("/health/ready", new HealthCheckOptions()
        {
            Predicate = (check) => check.Tags.Contains("ready"),
        });
        app.MapHealthChecks("/health/live", new HealthCheckOptions());

        app.UseHttpsRedirection();
        app.UseAuthorization();
    }
    
    private static void AddLogging(IConfiguration configuration)
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .WriteTo.Debug()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();
    }
}

public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
{
    public bool Authorize(DashboardContext context)
    {
        // In production, implement proper authorization
        // For now, allow all access in development
        return true;
    }
}