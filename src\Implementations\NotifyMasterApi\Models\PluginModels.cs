namespace NotifyMasterApi.Models;

/// <summary>
/// Simple plugin information for API responses
/// </summary>
public class Plugin
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public bool IsLoaded { get; set; }
    public bool IsHealthy { get; set; }
    public DateTime LastChecked { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> Features { get; set; } = new();
}

/// <summary>
/// Plugin list response
/// </summary>
public class PluginListResponse
{
    public List<Plugin> Plugins { get; set; } = new();
    public int Total { get; set; }
    public int Loaded { get; set; }
    public int Healthy { get; set; }
}

/// <summary>
/// Plugin operation result
/// </summary>
public class PluginOperationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? PluginName { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Load plugin request
/// </summary>
public class LoadPluginRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Path { get; set; }
}

/// <summary>
/// Unload plugin request
/// </summary>
public class UnloadPluginRequest
{
    public string Name { get; set; } = string.Empty;
}

/// <summary>
/// Load plugins from directory request
/// </summary>
public class LoadDirectoryRequest
{
    public string Directory { get; set; } = "plugins";
}

/// <summary>
/// Plugin health status
/// </summary>
public class PluginHealth
{
    public string Name { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Plugin health list response
/// </summary>
public class PluginHealthResponse
{
    public List<PluginHealth> Plugins { get; set; } = new();
    public int Total { get; set; }
    public int Healthy { get; set; }
    public int Unhealthy { get; set; }
}
