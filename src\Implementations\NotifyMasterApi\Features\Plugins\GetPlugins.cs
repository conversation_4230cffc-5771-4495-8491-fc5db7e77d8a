using FastEndpoints;
using PluginCore.Interfaces;
using NotifyMasterApi.Documentation;
using System.ComponentModel.DataAnnotations;

namespace NotifyMasterApi.Features.Plugins;

/// <summary>
/// Request model for retrieving plugins with optional filtering
/// </summary>
public class GetPluginsRequest
{
    /// <summary>
    /// Filter plugins by type (optional)
    /// </summary>
    /// <example>Email, SMS, Push, Messaging</example>
    public string? Type { get; set; }

    /// <summary>
    /// Filter plugins by enabled status (optional)
    /// </summary>
    /// <example>true</example>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// Filter plugins by provider (optional)
    /// </summary>
    /// <example>SendGrid, Twilio, FCM</example>
    public string? Provider { get; set; }

    /// <summary>
    /// Include detailed plugin information
    /// </summary>
    /// <example>false</example>
    public bool IncludeDetails { get; set; } = false;
}

public class GetPluginsResponse
{
    public List<PluginSummary> Plugins { get; set; } = new();
    public int TotalCount { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class PluginSummary
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? LoadedAt { get; set; }
}

public class GetPluginsEndpoint : Endpoint<GetPluginsRequest, GetPluginsResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginsEndpoint> _logger;

    public GetPluginsEndpoint(IPluginManager pluginManager, ILogger<GetPluginsEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigurePluginEndpoint(
            "GET",
            "/api/plugins",
            "Get All Plugins",
            "Retrieve a comprehensive list of all loaded plugins with their status, configuration, and capabilities.\n\n" +
            "## 🔌 Plugin Information\n" +
            "- **Plugin Details**: Name, version, provider, and type\n" +
            "- **Status Information**: Enabled/disabled state and health\n" +
            "- **Configuration**: Current settings and capabilities\n" +
            "- **Metrics**: Performance and usage statistics\n" +
            "- **Dependencies**: Required services and connections\n\n" +
            "## 📋 Plugin Types\n" +
            "- **Email Plugins**: SendGrid, Mailgun, SMTP, Amazon SES\n" +
            "- **SMS Plugins**: Twilio, Kavenegar, Nexmo, Amazon SNS\n" +
            "- **Push Plugins**: FCM, APNS, OneSignal, Web Push\n" +
            "- **Messaging Plugins**: WhatsApp, Slack, Teams, Discord\n" +
            "- **Utility Plugins**: Templates, Analytics, Webhooks\n\n" +
            "## 🔍 Filtering Options\n" +
            "- Filter by plugin type (Email, SMS, Push, etc.)\n" +
            "- Filter by enabled/disabled status\n" +
            "- Filter by specific provider\n" +
            "- Include/exclude detailed information\n\n" +
            "## 📊 Plugin Status\n" +
            "- **Active**: Plugin is loaded and operational\n" +
            "- **Inactive**: Plugin is loaded but disabled\n" +
            "- **Error**: Plugin failed to load or has issues\n" +
            "- **Loading**: Plugin is currently being initialized\n\n" +
            "## 🛠️ Management Operations\n" +
            "Use this endpoint to:\n" +
            "- Monitor plugin health and status\n" +
            "- Identify available messaging providers\n" +
            "- Check plugin configurations\n" +
            "- Plan plugin management operations",
            new[] { "System Management", "Configuration" }
        );
    }

    public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)
    {
        try
        {
            var statuses = await _pluginManager.GetPluginStatusesAsync(ct);
            var manifests = _pluginManager.GetPluginManifests();

            var filteredStatuses = statuses.AsEnumerable();

            if (!string.IsNullOrEmpty(req.Type))
            {
                filteredStatuses = filteredStatuses.Where(s =>
                    s.Type.Equals(req.Type, StringComparison.OrdinalIgnoreCase));
            }

            if (req.IsEnabled.HasValue)
            {
                filteredStatuses = filteredStatuses.Where(s => s.IsLoaded == req.IsEnabled.Value);
            }

            if (!string.IsNullOrEmpty(req.Provider))
            {
                filteredStatuses = filteredStatuses.Where(s =>
                {
                    var manifest = manifests.FirstOrDefault(m => m.Name == s.Name);
                    return manifest?.Provider?.Equals(req.Provider, StringComparison.OrdinalIgnoreCase) == true;
                });
            }

            var pluginSummaries = filteredStatuses.Select(status =>
            {
                var manifest = manifests.FirstOrDefault(m => m.Name == status.Name);
                return new PluginSummary
                {
                    Name = status.Name,
                    Version = status.Version,
                    Type = status.Type,
                    Provider = manifest?.Provider ?? "Unknown",
                    IsEnabled = status.IsLoaded,
                    Status = status.Status,
                    LoadedAt = status.LastChecked.DateTime
                };
            }).ToList();

            await SendOkAsync(new GetPluginsResponse
            {
                Plugins = pluginSummaries,
                TotalCount = pluginSummaries.Count
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving plugins");
            await SendErrorsAsync(500, ct);
        }
    }
}
