using FastEndpoints;
using MediatR;
using NotifyMasterApi.Features.Plugins.Queries;
using NotifyMasterApi.Models;

namespace NotifyMasterApi.Features.Plugins;

/// <summary>
/// Simple request for getting plugins
/// </summary>
public class GetPluginsRequest
{
    public string? Type { get; set; }
    public bool? IsLoaded { get; set; }
}

public class GetPluginsEndpoint : Endpoint<GetPluginsRequest, PluginListResponse>
{
    private readonly IMediator _mediator;

    public GetPluginsEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Get("/api/plugins");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get Plugins";
            s.Description = "Get all loaded plugins with optional filtering";
            s.Response<PluginListResponse>(200, "Plugins retrieved successfully");
        });
    }

    public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)
    {
        try
        {
            var statuses = await _pluginManager.GetPluginStatusesAsync(ct);
            var manifests = _pluginManager.GetPluginManifests();

            var filteredStatuses = statuses.AsEnumerable();

            if (!string.IsNullOrEmpty(req.Type))
            {
                filteredStatuses = filteredStatuses.Where(s =>
                    s.Type.Equals(req.Type, StringComparison.OrdinalIgnoreCase));
            }

            if (req.IsEnabled.HasValue)
            {
                filteredStatuses = filteredStatuses.Where(s => s.IsLoaded == req.IsEnabled.Value);
            }

            if (!string.IsNullOrEmpty(req.Provider))
            {
                filteredStatuses = filteredStatuses.Where(s =>
                {
                    var manifest = manifests.FirstOrDefault(m => m.Name == s.Name);
                    return manifest?.Provider?.Equals(req.Provider, StringComparison.OrdinalIgnoreCase) == true;
                });
            }

            var pluginSummaries = filteredStatuses.Select(status =>
            {
                var manifest = manifests.FirstOrDefault(m => m.Name == status.Name);
                return new PluginSummary
                {
                    Name = status.Name,
                    Version = status.Version,
                    Type = status.Type,
                    Provider = manifest?.Provider ?? "Unknown",
                    IsEnabled = status.IsLoaded,
                    Status = status.Status,
                    LoadedAt = status.LastChecked.DateTime
                };
            }).ToList();

            await SendOkAsync(new GetPluginsResponse
            {
                Plugins = pluginSummaries,
                TotalCount = pluginSummaries.Count
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving plugins");
            await SendErrorsAsync(500, ct);
        }
    }
}
