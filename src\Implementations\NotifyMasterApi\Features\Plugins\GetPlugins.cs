using FastEndpoints;
using MediatR;
using NotifyMasterApi.Features.Plugins.Queries;
using NotifyMasterApi.Models;

namespace NotifyMasterApi.Features.Plugins;

/// <summary>
/// Simple request for getting plugins
/// </summary>
public class GetPluginsRequest
{
    public string? Type { get; set; }
    public bool? IsLoaded { get; set; }
}

public class GetPluginsEndpoint : Endpoint<GetPluginsRequest, PluginListResponse>
{
    private readonly IMediator _mediator;

    public GetPluginsEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Get("/api/plugins");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get Plugins";
            s.Description = "Get all loaded plugins with optional filtering";
            s.Response<PluginListResponse>(200, "Plugins retrieved successfully");
        });
    }

    public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)
    {
        var query = new GetPluginsQuery(req.Type, req.IsLoaded);
        var response = await _mediator.Send(query, ct);
        await SendOkAsync(response, ct);
    }
}
