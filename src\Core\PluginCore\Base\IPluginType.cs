namespace PluginCore.Base;

/// <summary>
/// Marker interface for all plugin type interfaces.
/// Used for reflection purposes and better type organization.
/// </summary>
/// <remarks>
/// This interface serves as a base marker for all plugin type interfaces in the system.
/// It provides a common type for reflection and type organization purposes.
/// All plugin type interfaces should inherit from this interface.
/// </remarks>
public interface IPluginType
{
}
