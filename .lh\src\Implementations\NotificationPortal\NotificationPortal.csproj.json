{"sourceFile": "src/Implementations/NotificationPortal/NotificationPortal.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751218878329, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751218929547, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,9 @@\n   <ItemGroup>\r\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n     <PackageReference Include=\"Microsoft.AspNetCore.Authorization\" Version=\"9.0.6\" />\r\n     <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.2.0\" />\r\n-.0.0    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\r\n+.0.6    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\r\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.0\" />\r\n     <PackageReference Include=\"Blazored.LocalStorage\" Version=\"4.5.0\" />\r\n     <PackageReference Include=\"Blazored.Toast\" Version=\"4.2.1\" />\r\n     <PackageReference Include=\"MudBlazor\" Version=\"7.8.0\" />\r\n"}], "date": 1751218878329, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk.Web\">\r\n\r\n  <PropertyGroup>\r\n    <TargetFramework>net9.0</TargetFramework>\r\n    <Nullable>enable</Nullable>\r\n    <ImplicitUsings>enable</ImplicitUsings>\r\n  </PropertyGroup>\r\n\r\n  <ItemGroup>\r\n    <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n    <PackageReference Include=\"Microsoft.AspNetCore.Authorization\" Version=\"9.0.6\" />\r\n    <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"9.0.0\" />\r\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.0\" />\r\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.0\" />\r\n    <PackageReference Include=\"Blazored.LocalStorage\" Version=\"4.5.0\" />\r\n    <PackageReference Include=\"Blazored.Toast\" Version=\"4.2.1\" />\r\n    <PackageReference Include=\"MudBlazor\" Version=\"7.8.0\" />\r\n    <PackageReference Include=\"MudBlazor.Charts\" Version=\"0.1.8\" />\r\n    <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\r\n    <PackageReference Include=\"Microsoft.Extensions.Http\" Version=\"9.0.0\" />\r\n    <PackageReference Include=\"Microsoft.AspNetCore.Components.DataAnnotations.Validation\" Version=\"3.2.0-rc1.20223.4\" />\r\n    <PackageReference Include=\"Microsoft.Extensions.Localization\" Version=\"9.0.0\" />\r\n    <PackageReference Include=\"Microsoft.AspNetCore.Mvc.Localization\" Version=\"2.2.0\" />\r\n    <PackageReference Include=\"BlazorMonaco\" Version=\"3.2.0\" />\r\n    <PackageReference Include=\"Markdig\" Version=\"0.37.0\" />\r\n  </ItemGroup>\r\n\r\n  <ItemGroup>\r\n    <ProjectReference Include=\"..\\..\\Services\\NotifyMasterApi\\NotifyMasterApi.csproj\" />\r\n  </ItemGroup>\r\n\r\n  <ItemGroup>\r\n    <Folder Include=\"Components\\Layout\\\" />\r\n    <Folder Include=\"Components\\Shared\\\" />\r\n    <Folder Include=\"Pages\\Auth\\\" />\r\n    <Folder Include=\"Pages\\Admin\\\" />\r\n    <Folder Include=\"Services\\\" />\r\n    <Folder Include=\"Models\\\" />\r\n    <Folder Include=\"Hubs\\\" />\r\n    <Folder Include=\"wwwroot\\css\\\" />\r\n    <Folder Include=\"wwwroot\\js\\\" />\r\n    <Folder Include=\"wwwroot\\images\\\" />\r\n  </ItemGroup>\r\n\r\n</Project>\r\n"}]}