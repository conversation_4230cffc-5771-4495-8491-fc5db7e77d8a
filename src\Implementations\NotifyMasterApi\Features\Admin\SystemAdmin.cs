using FastEndpoints;
using NotifyMasterApi.Interfaces;
using NotifyMasterApi.Services;
using NotifyMasterApi.Documentation;
using System.Diagnostics;

namespace NotifyMasterApi.Features.Admin;

public class GetSystemStatusEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetSystemStatusEndpoint> _logger;

    public GetSystemStatusEndpoint(IPluginManager pluginManager, ILogger<GetSystemStatusEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigureAdminEndpoint(
            "GET",
            "/api/admin/system/status",
            "System Status Overview",
            "Get comprehensive system status information for administrative monitoring and troubleshooting.\n\n" +
            "## 🎯 System Overview\n" +
            "- **Application Status**: Core service health and uptime\n" +
            "- **Plugin Status**: All loaded plugins and their health\n" +
            "- **Resource Usage**: Memory, CPU, and disk utilization\n" +
            "- **Performance Metrics**: Response times and throughput\n" +
            "- **Error Rates**: Recent error statistics and trends\n\n" +
            "## 📊 Monitoring Data\n" +
            "- Real-time system metrics\n" +
            "- Plugin connectivity status\n" +
            "- Database performance indicators\n" +
            "- Queue depths and processing rates\n" +
            "- External service dependencies\n\n" +
            "## 🔍 Diagnostic Information\n" +
            "- Application version and build info\n" +
            "- Environment configuration\n" +
            "- Feature flags and capabilities\n" +
            "- Recent system events and logs\n" +
            "- Performance bottlenecks\n\n" +
            "## 🚨 Alert Conditions\n" +
            "- High memory usage (>80%)\n" +
            "- Plugin failures or disconnections\n" +
            "- Database connectivity issues\n" +
            "- Queue backlog warnings\n" +
            "- External service timeouts\n\n" +
            "## 📈 Historical Data\n" +
            "- System uptime statistics\n" +
            "- Performance trend analysis\n" +
            "- Error rate patterns\n" +
            "- Resource usage history",
            new[] { "Monitoring", "Diagnostics", "Operations" }
        );
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var enabledPlugins = plugins.Where(p => p.IsEnabled).ToList();

            var systemStatus = new
            {
                System = new
                {
                    Status = "Running",
                    Timestamp = DateTime.UtcNow,
                    Version = "2.0.0",
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                    LoadedPlugins = plugins.Count(),
                    EnabledPlugins = enabledPlugins.Count
                },
                Services = new
                {
                    Email = new
                    {
                        Available = enabledPlugins.Any(p => p.Type.ToString() == "Email"),
                        Providers = enabledPlugins.Where(p => p.Type.ToString() == "Email").Select(p => p.Provider).ToList()
                    },
                    SMS = new
                    {
                        Available = enabledPlugins.Any(p => p.Type.ToString() == "Sms"),
                        Providers = enabledPlugins.Where(p => p.Type.ToString() == "Sms").Select(p => p.Provider).ToList()
                    },
                    Push = new
                    {
                        Available = enabledPlugins.Any(p => p.Type.ToString() == "PushNotification"),
                        Providers = enabledPlugins.Where(p => p.Type.ToString() == "PushNotification").Select(p => p.Provider).ToList()
                    },
                    Messaging = new
                    {
                        Available = enabledPlugins.Any(p => p.Type.ToString() == "Messaging"),
                        Providers = enabledPlugins.Where(p => p.Type.ToString() == "Messaging").Select(p => p.Provider).ToList()
                    }
                },
                Infrastructure = new
                {
                    Database = new { Type = "InMemory", Status = "Active" },
                    Logging = new { Provider = "Serilog", Status = "Active" },
                    ApiDocumentation = new { Provider = "Scalar", Status = "Active" },
                    HealthChecks = new { Status = "Active", Endpoint = "/health/ready" }
                },
                Performance = new
                {
                    ProcessId = Environment.ProcessId,
                    WorkingSet = GC.GetTotalMemory(false),
                    StartTime = Process.GetCurrentProcess().StartTime,
                    Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime
                }
            };

            await SendOkAsync(systemStatus, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system status");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetSystemMetricsEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly INotificationLoggingService _loggingService;
    private readonly ILogger<GetSystemMetricsEndpoint> _logger;

    public GetSystemMetricsEndpoint(
        IPluginManager pluginManager,
        INotificationLoggingService loggingService,
        ILogger<GetSystemMetricsEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _loggingService = loggingService;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/admin/metrics");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get system metrics";
            s.Description = "Get comprehensive system metrics across all plugins";
            s.Responses[200] = "System metrics retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("System Admin");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var enabledPlugins = plugins.Where(p => p.IsEnabled).ToList();

            var pluginMetrics = new List<object>();
            foreach (var plugin in enabledPlugins)
            {
                try
                {
                    var metrics = await _pluginManager.GetPluginMetricsAsync(plugin.Name ?? "");
                    pluginMetrics.Add(new
                    {
                        PluginName = plugin.Name,
                        Provider = plugin.Provider,
                        Type = plugin.Type,
                        Metrics = metrics
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not get metrics for plugin {PluginName}", plugin.Name);
                }
            }

            var systemMetrics = new
            {
                Overview = new
                {
                    TotalPlugins = plugins.Count(),
                    ActivePlugins = enabledPlugins.Count,
                    PluginTypes = enabledPlugins.GroupBy(p => p.Type.ToString()).ToDictionary(g => g.Key ?? "Unknown", g => g.Count())
                },
                PluginMetrics = pluginMetrics,
                System = new
                {
                    Timestamp = DateTime.UtcNow,
                    CollectionPeriod = "Real-time",
                    MemoryUsage = GC.GetTotalMemory(false),
                    ProcessUptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime
                }
            };

            await SendOkAsync(systemMetrics, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system metrics");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetSystemConfigurationEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IConfiguration _configuration;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetSystemConfigurationEndpoint> _logger;

    public GetSystemConfigurationEndpoint(
        IConfiguration configuration,
        IPluginManager pluginManager,
        ILogger<GetSystemConfigurationEndpoint> logger)
    {
        _configuration = configuration;
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/admin/config");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get system configuration";
            s.Description = "Get current system configuration settings";
            s.Responses[200] = "Configuration retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("System Admin");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();

            var systemConfig = new
            {
                Application = new
                {
                    Name = "NotificationService",
                    Version = "2.0.0",
                    Environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? "Development",
                    Framework = ".NET 9.0"
                },
                Features = new
                {
                    PluginManagement = true,
                    MetricsTracking = true,
                    HealthMonitoring = true,
                    FeatureDetection = true,
                    FastEndpoints = true,
                    ScalarDocumentation = true
                },
                Plugins = new
                {
                    TotalLoaded = plugins.Count(),
                    EnabledCount = plugins.Count(p => p.IsEnabled),
                    ByType = plugins.GroupBy(p => p.Type.ToString()).ToDictionary(
                        g => g.Key ?? "Unknown",
                        g => new
                        {
                            Total = g.Count(),
                            Enabled = g.Count(p => p.IsEnabled),
                            Providers = g.Select(p => p.Provider).Distinct().ToList()
                        }
                    )
                },
                Infrastructure = new
                {
                    Database = new
                    {
                        Provider = "EntityFramework",
                        Type = "InMemory",
                        Status = "Active"
                    },
                    Logging = new
                    {
                        Provider = "Serilog",
                        Sinks = new[] { "Console", "Debug" },
                        Level = "Information"
                    },
                    Documentation = new
                    {
                        Provider = "Scalar",
                        OpenApiVersion = "3.0",
                        Endpoint = "/scalar/v1"
                    }
                },
                Endpoints = new
                {
                    Health = "/health/ready",
                    Plugins = "/api/plugins",
                    Features = "/api/system/features",
                    Documentation = "/scalar/v1",
                    Admin = "/api/admin"
                }
            };

            await SendOkAsync(systemConfig, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system configuration");
            await SendErrorsAsync(500, ct);
        }
    }
}
