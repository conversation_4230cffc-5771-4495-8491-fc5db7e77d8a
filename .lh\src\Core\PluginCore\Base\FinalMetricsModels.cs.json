{"sourceFile": "src/Core/PluginCore/Base/FinalMetricsModels.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751200978670, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751201013138, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -472,5 +472,97 @@\n         public DateTime GeneratedAt { get; set; }\n         public string? GeneratedBy { get; set; }\n     }\n \n-    \n\\ No newline at end of file\n+    \n+public class DeliveryResult\n+{\n+    public string DeliveryId { get; set; } = string.Empty;\n+    public string Status { get; set; } = string.Empty;\n+    public string Channel { get; set; } = string.Empty;\n+    public string Recipient { get; set; } = string.Empty;\n+    public string? ErrorCode { get; set; }\n+    public string? ErrorMessage { get; set; }\n+    public DateTime DeliveryTime { get; set; }\n+    public bool IsRetried { get; set; }\n+\n+    public static implicit operator DeliveryResultDto(DeliveryResult m) => new()\n+    {\n+        DeliveryId = m.DeliveryId,\n+        Status = m.Status,\n+        Channel = m.Channel,\n+        Recipient = m.Recipient,\n+        ErrorCode = m.ErrorCode,\n+        ErrorMessage = m.ErrorMessage,\n+        DeliveryTime = m.DeliveryTime,\n+        IsRetried = m.IsRetried\n+    };\n+\n+    public static implicit operator DeliveryResult(DeliveryResultDto dto) => new()\n+    {\n+        DeliveryId = dto.DeliveryId,\n+        Status = dto.Status,\n+        Channel = dto.Channel,\n+        Recipient = dto.Recipient,\n+        ErrorCode = dto.ErrorCode,\n+        ErrorMessage = dto.ErrorMessage,\n+        DeliveryTime = dto.DeliveryTime,\n+        IsRetried = dto.IsRetried\n+    };\n+}\n+\n+public class DeliveryResultDto\n+{\n+    public string DeliveryId { get; set; } = string.Empty;\n+    public string Status { get; set; } = string.Empty;\n+    public string Channel { get; set; } = string.Empty;\n+    public string Recipient { get; set; } = string.Empty;\n+    public string? ErrorCode { get; set; }\n+    public string? ErrorMessage { get; set; }\n+    public DateTime DeliveryTime { get; set; }\n+    public bool IsRetried { get; set; }\n+}\n+\n+\n+public class UsageMetrics\n+{\n+    public string GatewayId { get; set; } = string.Empty;\n+    public DateTime Start { get; set; }\n+    public DateTime End { get; set; }\n+    public int Sent { get; set; }\n+    public int Delivered { get; set; }\n+    public int Failed { get; set; }\n+    public decimal? Cost { get; set; }\n+\n+    public static implicit operator UsageMetricsDto(UsageMetrics m) => new()\n+    {\n+        GatewayId = m.GatewayId,\n+        Start = m.Start,\n+        End = m.End,\n+        Sent = m.Sent,\n+        Delivered = m.Delivered,\n+        Failed = m.Failed,\n+        Cost = m.Cost\n+    };\n+\n+    public static implicit operator UsageMetrics(UsageMetricsDto dto) => new()\n+    {\n+        GatewayId = dto.GatewayId,\n+        Start = dto.Start,\n+        End = dto.End,\n+        Sent = dto.Sent,\n+        Delivered = dto.Delivered,\n+        Failed = dto.Failed,\n+        Cost = dto.Cost\n+    };\n+}\n+\n+public class UsageMetricsDto\n+{\n+    public string GatewayId { get; set; } = string.Empty;\n+    public DateTime Start { get; set; }\n+    public DateTime End { get; set; }\n+    public int Sent { get; set; }\n+    public int Delivered { get; set; }\n+    public int Failed { get; set; }\n+    public decimal? Cost { get; set; }\n+}\n"}], "date": 1751200978670, "name": "Commit-0", "content": "\npublic class GatewayErrorEntry\n{\n    public string ErrorId { get; set; } = string.Empty;\n    public string Message { get; set; } = string.Empty;\n    public string Severity { get; set; } = string.Empty;\n    public DateTime Timestamp { get; set; }\n\n    public static implicit operator GatewayErrorEntryDto(GatewayErrorEntry m) => new()\n    {\n        ErrorId = m.ErrorId,\n        Message = m.Message,\n        Severity = m.Severity,\n        Timestamp = m.Timestamp\n    };\n\n    public static implicit operator GatewayErrorEntry(GatewayErrorEntryDto dto) => new()\n    {\n        ErrorId = dto.ErrorId,\n        Message = dto.Message,\n        Severity = dto.Severity,\n        Timestamp = dto.Timestamp\n    };\n}\n\npublic class GatewayErrorEntryDto\n{\n    public string ErrorId { get; set; } = string.Empty;\n    public string Message { get; set; } = string.Empty;\n    public string Severity { get; set; } = string.Empty;\n    public DateTime Timestamp { get; set; }\n}\n\n\npublic class PerformanceSnapshot\n{\n    public string SnapshotId { get; set; } = string.Empty;\n    public int MessagesPerSecond { get; set; }\n    public DateTime CapturedAt { get; set; }\n\n    public static implicit operator PerformanceSnapshotDto(PerformanceSnapshot m) => new()\n    {\n        SnapshotId = m.SnapshotId,\n        MessagesPerSecond = m.MessagesPerSecond,\n        CapturedAt = m.CapturedAt\n    };\n\n    public static implicit operator PerformanceSnapshot(PerformanceSnapshotDto dto) => new()\n    {\n        SnapshotId = dto.SnapshotId,\n        MessagesPerSecond = dto.MessagesPerSecond,\n        CapturedAt = dto.CapturedAt\n    };\n}\n\npublic class PerformanceSnapshotDto\n{\n    public string SnapshotId { get; set; } = string.Empty;\n    public int MessagesPerSecond { get; set; }\n    public DateTime CapturedAt { get; set; }\n}\n\n\npublic class SlaReport\n{\n    public string ReportId { get; set; } = string.Empty;\n    public double AvgDeliveryTimeMs { get; set; }\n    public double AvailabilityPercent { get; set; }\n    public DateTime ReportedAt { get; set; }\n\n    public static implicit operator SlaReportDto(SlaReport m) => new()\n    {\n        ReportId = m.ReportId,\n        AvgDeliveryTimeMs = m.AvgDeliveryTimeMs,\n        AvailabilityPercent = m.AvailabilityPercent,\n        ReportedAt = m.ReportedAt\n    };\n\n    public static implicit operator SlaReport(SlaReportDto dto) => new()\n    {\n        ReportId = dto.ReportId,\n        AvgDeliveryTimeMs = dto.AvgDeliveryTimeMs,\n        AvailabilityPercent = dto.AvailabilityPercent,\n        ReportedAt = dto.ReportedAt\n    };\n}\n\npublic class SlaReportDto\n{\n    public string ReportId { get; set; } = string.Empty;\n    public double AvgDeliveryTimeMs { get; set; }\n    public double AvailabilityPercent { get; set; }\n    public DateTime ReportedAt { get; set; }\n}\n\n\npublic class LatencyMetrics\n{\n    public string MetricId { get; set; } = string.Empty;\n    public double AuthMs { get; set; }\n    public double SendMs { get; set; }\n    public double CallbackMs { get; set; }\n    public DateTime CollectedAt { get; set; }\n\n    public static implicit operator LatencyMetricsDto(LatencyMetrics m) => new()\n    {\n        MetricId = m.MetricId,\n        AuthMs = m.AuthMs,\n        SendMs = m.SendMs,\n        CallbackMs = m.CallbackMs,\n        CollectedAt = m.CollectedAt\n    };\n\n    public static implicit operator LatencyMetrics(LatencyMetricsDto dto) => new()\n    {\n        MetricId = dto.MetricId,\n        AuthMs = dto.AuthMs,\n        SendMs = dto.SendMs,\n        CallbackMs = dto.CallbackMs,\n        CollectedAt = dto.CollectedAt\n    };\n}\n\npublic class LatencyMetricsDto\n{\n    public string MetricId { get; set; } = string.Empty;\n    public double AuthMs { get; set; }\n    public double SendMs { get; set; }\n    public double CallbackMs { get; set; }\n    public DateTime CollectedAt { get; set; }\n}\n\n\npublic class AnomalyDetectionResult\n{\n    public string AnomalyId { get; set; } = string.Empty;\n    public string Type { get; set; } = string.Empty;\n    public string Details { get; set; } = string.Empty;\n    public DateTime DetectedAt { get; set; }\n\n    public static implicit operator AnomalyDetectionResultDto(AnomalyDetectionResult m) => new()\n    {\n        AnomalyId = m.AnomalyId,\n        Type = m.Type,\n        Details = m.Details,\n        DetectedAt = m.DetectedAt\n    };\n\n    public static implicit operator AnomalyDetectionResult(AnomalyDetectionResultDto dto) => new()\n    {\n        AnomalyId = dto.AnomalyId,\n        Type = dto.Type,\n        Details = dto.Details,\n        DetectedAt = dto.DetectedAt\n    };\n}\n\npublic class AnomalyDetectionResultDto\n{\n    public string AnomalyId { get; set; } = string.Empty;\n    public string Type { get; set; } = string.Empty;\n    public string Details { get; set; } = string.Empty;\n    public DateTime DetectedAt { get; set; }\n}\n\n\npublic class GeneratedReport\n{\n    public string ReportId { get; set; } = string.Empty;\n    public string Name { get; set; } = string.Empty;\n    public string Format { get; set; } = string.Empty;\n    public string Status { get; set; } = string.Empty;\n    public DateTime CreatedAt { get; set; }\n\n    public static implicit operator GeneratedReportDto(GeneratedReport m) => new()\n    {\n        ReportId = m.ReportId,\n        Name = m.Name,\n        Format = m.Format,\n        Status = m.Status,\n        CreatedAt = m.CreatedAt\n    };\n\n    public static implicit operator GeneratedReport(GeneratedReportDto dto) => new()\n    {\n        ReportId = dto.ReportId,\n        Name = dto.Name,\n        Format = dto.Format,\n        Status = dto.Status,\n        CreatedAt = dto.CreatedAt\n    };\n}\n\npublic class GeneratedReportDto\n{\n    public string ReportId { get; set; } = string.Empty;\n    public string Name { get; set; } = string.Empty;\n    public string Format { get; set; } = string.Empty;\n    public string Status { get; set; } = string.Empty;\n    public DateTime CreatedAt { get; set; }\n}\n\n\npublic class MetricsReportOptions\n{\n    public string Type { get; set; } = string.Empty;\n    public string Format { get; set; } = string.Empty;\n    public string Scope { get; set; } = string.Empty;\n\n    public static implicit operator MetricsReportOptionsDto(MetricsReportOptions m) => new()\n    {\n        Type = m.Type,\n        Format = m.Format,\n        Scope = m.Scope\n    };\n\n    public static implicit operator MetricsReportOptions(MetricsReportOptionsDto dto) => new()\n    {\n        Type = dto.Type,\n        Format = dto.Format,\n        Scope = dto.Scope\n    };\n}\n\npublic class MetricsReportOptionsDto\n{\n    public string Type { get; set; } = string.Empty;\n    public string Format { get; set; } = string.Empty;\n    public string Scope { get; set; } = string.Empty;\n}\n\n\npublic class ConfigurationImpactRecord\n{\n    public string ChangeId { get; set; } = string.Empty;\n    public string Description { get; set; } = string.Empty;\n    public DateTime Timestamp { get; set; }\n\n    public static implicit operator ConfigurationImpactRecordDto(ConfigurationImpactRecord m) => new()\n    {\n        ChangeId = m.ChangeId,\n        Description = m.Description,\n        Timestamp = m.Timestamp\n    };\n\n    public static implicit operator ConfigurationImpactRecord(ConfigurationImpactRecordDto dto) => new()\n    {\n        ChangeId = dto.ChangeId,\n        Description = dto.Description,\n        Timestamp = dto.Timestamp\n    };\n}\n\npublic class ConfigurationImpactRecordDto\n{\n    public string ChangeId { get; set; } = string.Empty;\n    public string Description { get; set; } = string.Empty;\n    public DateTime Timestamp { get; set; }\n}\n public class GatewayStatusReport\n    {\n        public string Status { get; set; } = \"Unknown\";\n        public string? Description { get; set; }\n        public DateTime Timestamp { get; set; }\n        public string GatewayName { get; set; } = string.Empty;\n        public string PluginId { get; set; } = string.Empty;\n        public string PluginVersion { get; set; } = string.Empty;\n        public TimeSpan Uptime { get; set; }\n        public bool IsAuthenticated { get; set; }\n        public IDictionary<string, string>? Metadata { get; set; }\n        public string? Region { get; set; }\n        public string? Environment { get; set; }\n\n        public static implicit operator GatewayStatusReportDto(GatewayStatusReport m) => new()\n        {\n            Status = m.Status,\n            Description = m.Description,\n            Timestamp = m.Timestamp,\n            GatewayName = m.GatewayName,\n            PluginId = m.PluginId,\n            PluginVersion = m.PluginVersion,\n            UptimeTicks = m.Uptime.Ticks,\n            IsAuthenticated = m.IsAuthenticated,\n            Metadata = m.Metadata,\n            Region = m.Region,\n            Environment = m.Environment\n        };\n\n        public static implicit operator GatewayStatusReport(GatewayStatusReportDto dto) => new()\n        {\n            Status = dto.Status,\n            Description = dto.Description,\n            Timestamp = dto.Timestamp,\n            GatewayName = dto.GatewayName,\n            PluginId = dto.PluginId,\n            PluginVersion = dto.PluginVersion,\n            Uptime = TimeSpan.FromTicks(dto.UptimeTicks),\n            IsAuthenticated = dto.IsAuthenticated,\n            Metadata = dto.Metadata,\n            Region = dto.Region,\n            Environment = dto.Environment\n        };\n    }\n\n    public class GatewayStatusReportDto\n    {\n        public string Status { get; set; } = \"Unknown\";\n        public string? Description { get; set; }\n        public DateTime Timestamp { get; set; }\n        public string GatewayName { get; set; } = string.Empty;\n        public string PluginId { get; set; } = string.Empty;\n        public string PluginVersion { get; set; } = string.Empty;\n        public long UptimeTicks { get; set; }\n        public bool IsAuthenticated { get; set; }\n        public IDictionary<string, string>? Metadata { get; set; }\n        public string? Region { get; set; }\n        public string? Environment { get; set; }\n    }\n\n    public class DeliveryRecord\n    {\n        public string MessageId { get; set; } = string.Empty;\n        public string CorrelationId { get; set; } = string.Empty;\n        public string Status { get; set; } = string.Empty;\n        public string Recipient { get; set; } = string.Empty;\n        public string Channel { get; set; } = string.Empty;\n        public string SenderId { get; set; } = string.Empty;\n        public DateTime SentAt { get; set; }\n        public DateTime? DeliveredAt { get; set; }\n        public DateTime? ExpiredAt { get; set; }\n        public string? ErrorCode { get; set; }\n        public string? ErrorMessage { get; set; }\n        public string? Carrier { get; set; }\n        public string? Route { get; set; }\n        public bool IsRetried { get; set; }\n        public IDictionary<string, string>? Metadata { get; set; }\n\n        public static implicit operator DeliveryRecordDto(DeliveryRecord record) => new()\n        {\n            MessageId = record.MessageId,\n            CorrelationId = record.CorrelationId,\n            Status = record.Status,\n            Recipient = record.Recipient,\n            Channel = record.Channel,\n            SenderId = record.SenderId,\n            SentAt = record.SentAt,\n            DeliveredAt = record.DeliveredAt,\n            ExpiredAt = record.ExpiredAt,\n            ErrorCode = record.ErrorCode,\n            ErrorMessage = record.ErrorMessage,\n            Carrier = record.Carrier,\n            Route = record.Route,\n            IsRetried = record.IsRetried,\n            Metadata = record.Metadata\n        };\n\n        public static implicit operator DeliveryRecord(DeliveryRecordDto dto) => new()\n        {\n            MessageId = dto.MessageId,\n            CorrelationId = dto.CorrelationId,\n            Status = dto.Status,\n            Recipient = dto.Recipient,\n            Channel = dto.Channel,\n            SenderId = dto.SenderId,\n            SentAt = dto.SentAt,\n            DeliveredAt = dto.DeliveredAt,\n            ExpiredAt = dto.ExpiredAt,\n            ErrorCode = dto.ErrorCode,\n            ErrorMessage = dto.ErrorMessage,\n            Carrier = dto.Carrier,\n            Route = dto.Route,\n            IsRetried = dto.IsRetried,\n            Metadata = dto.Metadata\n        };\n    }\n\n    public class DeliveryRecordDto\n    {\n        public string MessageId { get; set; } = string.Empty;\n        public string CorrelationId { get; set; } = string.Empty;\n        public string Status { get; set; } = string.Empty;\n        public string Recipient { get; set; } = string.Empty;\n        public string Channel { get; set; } = string.Empty;\n        public string SenderId { get; set; } = string.Empty;\n        public DateTime SentAt { get; set; }\n        public DateTime? DeliveredAt { get; set; }\n        public DateTime? ExpiredAt { get; set; }\n        public string? ErrorCode { get; set; }\n        public string? ErrorMessage { get; set; }\n        public string? Carrier { get; set; }\n        public string? Route { get; set; }\n        public bool IsRetried { get; set; }\n        public IDictionary<string, string>? Metadata { get; set; }\n    }\n\n    public class UsageSummary\n    {\n        public string GatewayName { get; set; } = string.Empty;\n        public string PluginId { get; set; } = string.Empty;\n        public DateTime From { get; set; }\n        public DateTime To { get; set; }\n        public int Sent { get; set; }\n        public int Delivered { get; set; }\n        public int Failed { get; set; }\n        public int Queued { get; set; }\n        public int Expired { get; set; }\n        public int Retried { get; set; }\n        public decimal? EstimatedCost { get; set; }\n        public string? Currency { get; set; }\n        public IDictionary<string, int>? ChannelBreakdown { get; set; }\n        public IDictionary<string, int>? RegionBreakdown { get; set; }\n        public DateTime GeneratedAt { get; set; }\n        public string? GeneratedBy { get; set; }\n\n        public static implicit operator UsageSummaryDto(UsageSummary m) => new()\n        {\n            GatewayName = m.GatewayName,\n            PluginId = m.PluginId,\n            From = m.From,\n            To = m.To,\n            Sent = m.Sent,\n            Delivered = m.Delivered,\n            Failed = m.Failed,\n            Queued = m.Queued,\n            Expired = m.Expired,\n            Retried = m.Retried,\n            EstimatedCost = m.EstimatedCost,\n            Currency = m.Currency,\n            ChannelBreakdown = m.ChannelBreakdown,\n            RegionBreakdown = m.RegionBreakdown,\n            GeneratedAt = m.GeneratedAt,\n            GeneratedBy = m.GeneratedBy\n        };\n\n        public static implicit operator UsageSummary(UsageSummaryDto dto) => new()\n        {\n            GatewayName = dto.GatewayName,\n            PluginId = dto.PluginId,\n            From = dto.From,\n            To = dto.To,\n            Sent = dto.Sent,\n            Delivered = dto.Delivered,\n            Failed = dto.Failed,\n            Queued = dto.Queued,\n            Expired = dto.Expired,\n            Retried = dto.Retried,\n            EstimatedCost = dto.EstimatedCost,\n            Currency = dto.Currency,\n            ChannelBreakdown = dto.ChannelBreakdown,\n            RegionBreakdown = dto.RegionBreakdown,\n            GeneratedAt = dto.GeneratedAt,\n            GeneratedBy = dto.GeneratedBy\n        };\n    }\n\n    public class UsageSummaryDto\n    {\n        public string GatewayName { get; set; } = string.Empty;\n        public string PluginId { get; set; } = string.Empty;\n        public DateTime From { get; set; }\n        public DateTime To { get; set; }\n        public int Sent { get; set; }\n        public int Delivered { get; set; }\n        public int Failed { get; set; }\n        public int Queued { get; set; }\n        public int Expired { get; set; }\n        public int Retried { get; set; }\n        public decimal? EstimatedCost { get; set; }\n        public string? Currency { get; set; }\n        public IDictionary<string, int>? ChannelBreakdown { get; set; }\n        public IDictionary<string, int>? RegionBreakdown { get; set; }\n        public DateTime GeneratedAt { get; set; }\n        public string? GeneratedBy { get; set; }\n    }"}]}