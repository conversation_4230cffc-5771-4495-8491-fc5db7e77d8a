using PluginCore.Base;
using PluginCore.Interfaces;
using PluginCore.Models;

namespace PluginCore.Services;

/// <summary>
/// Interface for tenant-aware plugin management
/// </summary>
public interface ITenantAwarePluginManager : IPluginManager
{
    /// <summary>
    /// Gets plugins available to a specific tenant
    /// </summary>
    Task<IReadOnlyList<Plugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a plugin instance configured for a specific tenant
    /// </summary>
    Task<T?> GetTenantPluginAsync<T>(string tenantId, string pluginName, CancellationToken cancellationToken = default) where T : class, IPluginType;
    
    /// <summary>
    /// Configures a plugin for a specific tenant
    /// </summary>
    Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Enables a plugin for a specific tenant
    /// </summary>
    Task<OperationResult> EnableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Disables a plugin for a specific tenant
    /// </summary>
    Task<OperationResult> DisableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets tenant-specific plugin configuration
    /// </summary>
    Task<Dictionary<string, object>?> GetTenantPluginConfigurationAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets plugin usage metrics for a tenant
    /// </summary>
    Task<PluginMetrics?> GetTenantPluginMetricsAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if a tenant has reached plugin limits
    /// </summary>
    Task<bool> HasReachedPluginLimitAsync(string tenantId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Tenant-aware plugin manager implementation
/// </summary>
public class TenantAwarePluginManager : ITenantAwarePluginManager
{
    private readonly ILogger<TenantAwarePluginManager> _logger;
    private readonly IPluginManager _basePluginManager;
    private readonly ITenantService _tenantService;
    private readonly ITenantContext _tenantContext;
    private readonly Dictionary<string, Dictionary<string, object>> _tenantPluginInstances;
    private readonly object _lock = new();

    public TenantAwarePluginManager(
        ILogger<TenantAwarePluginManager> logger,
        IPluginManager basePluginManager,
        ITenantService tenantService,
        ITenantContext tenantContext)
    {
        _logger = logger;
        _basePluginManager = basePluginManager;
        _tenantService = tenantService;
        _tenantContext = tenantContext;
        _tenantPluginInstances = new Dictionary<string, Dictionary<string, object>>();
    }

    // Delegate base plugin manager methods
    public Task<OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default)
        => _basePluginManager.LoadPluginsAsync(pluginDirectory, cancellationToken);

    public Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)
        => _basePluginManager.LoadPluginAsync(pluginPath, cancellationToken);

    public Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default)
        => _basePluginManager.LoadPluginByNameAsync(pluginName, pluginDirectory, cancellationToken);

    public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType
        => _basePluginManager.GetPlugins<T>();

    public Task<Plugin?> GetPluginAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.GetPluginAsync(pluginName, cancellationToken);

    public IReadOnlyList<PluginManifest> GetPluginManifests()
        => _basePluginManager.GetPluginManifests();

    public PluginManifest? GetPluginManifest(string pluginName)
        => _basePluginManager.GetPluginManifest(pluginName);

    public Task<OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.UnloadPluginAsync(pluginName, cancellationToken);

    public Task<OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.ReloadPluginAsync(pluginName, cancellationToken);

    public Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default)
        => _basePluginManager.GetPluginStatusesAsync(cancellationToken);

    public Task<Dictionary<string, object>?> GetPluginConfigurationAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.GetPluginConfigurationAsync(pluginName, cancellationToken);

    public Task<OperationResult> ConfigurePluginAsync(string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)
        => _basePluginManager.ConfigurePluginAsync(pluginName, configuration, cancellationToken);

    public Task<PluginMetrics?> GetPluginMetricsAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.GetPluginMetricsAsync(pluginName, cancellationToken);

    public Task<object?> GetPluginHealthStatusAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.GetPluginHealthStatusAsync(pluginName, cancellationToken);

    // Tenant-aware methods
    public async Task<IReadOnlyList<Plugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get tenant plugin configurations
            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);
            var enabledPluginNames = tenantPlugins.Where(tp => tp.IsEnabled).Select(tp => tp.PluginName).ToHashSet();

            // Get all available plugins and filter by tenant configuration
            var allPlugins = new List<Plugin>();
            var manifests = GetPluginManifests();

            foreach (var manifest in manifests)
            {
                if (enabledPluginNames.Contains(manifest.Name))
                {
                    var plugin = await GetPluginAsync(manifest.Name, cancellationToken);
                    if (plugin != null)
                    {
                        allPlugins.Add(plugin);
                    }
                }
            }

            return allPlugins.AsReadOnly();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting plugins for tenant {TenantId}", tenantId);
            return Array.Empty<Plugin>();
        }
    }

    public async Task<T?> GetTenantPluginAsync<T>(string tenantId, string pluginName, CancellationToken cancellationToken = default) where T : class, IPluginType
    {
        try
        {
            // Check if plugin is enabled for tenant
            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);
            var tenantPlugin = tenantPlugins.FirstOrDefault(tp => tp.PluginName == pluginName && tp.IsEnabled);
            
            if (tenantPlugin == null)
            {
                _logger.LogWarning("Plugin {PluginName} is not enabled for tenant {TenantId}", pluginName, tenantId);
                return null;
            }

            // Get the base plugin instance
            var plugins = GetPlugins<T>();
            var plugin = plugins.FirstOrDefault(p => p.GetType().Name.Contains(pluginName));
            
            if (plugin == null)
            {
                _logger.LogWarning("Plugin {PluginName} not found or not of type {PluginType}", pluginName, typeof(T).Name);
                return null;
            }

            // Apply tenant-specific configuration if needed
            await ApplyTenantConfigurationAsync(plugin, tenantPlugin.Configuration);

            return plugin;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            return null;
        }
    }

    public async Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if tenant has reached plugin limits
            if (await HasReachedPluginLimitAsync(tenantId, cancellationToken))
            {
                return OperationResult.Failure("Tenant has reached plugin limit");
            }

            // Configure the plugin for the tenant
            await _tenantService.ConfigureTenantPluginAsync(tenantId, pluginName, configuration, cancellationToken);

            _logger.LogInformation("Plugin {PluginName} configured for tenant {TenantId}", pluginName, tenantId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            return OperationResult.Failure($"Failed to configure plugin: {ex.Message}");
        }
    }

    public async Task<OperationResult> EnableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if tenant has reached plugin limits
            if (await HasReachedPluginLimitAsync(tenantId, cancellationToken))
            {
                return OperationResult.Failure("Tenant has reached plugin limit");
            }

            // Enable the plugin for the tenant
            var configuration = new Dictionary<string, object> { ["enabled"] = true };
            await _tenantService.ConfigureTenantPluginAsync(tenantId, pluginName, configuration, cancellationToken);

            _logger.LogInformation("Plugin {PluginName} enabled for tenant {TenantId}", pluginName, tenantId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            return OperationResult.Failure($"Failed to enable plugin: {ex.Message}");
        }
    }

    public async Task<OperationResult> DisableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)
    {
        try
        {
            // Disable the plugin for the tenant
            var configuration = new Dictionary<string, object> { ["enabled"] = false };
            await _tenantService.ConfigureTenantPluginAsync(tenantId, pluginName, configuration, cancellationToken);

            _logger.LogInformation("Plugin {PluginName} disabled for tenant {TenantId}", pluginName, tenantId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            return OperationResult.Failure($"Failed to disable plugin: {ex.Message}");
        }
    }

    public async Task<Dictionary<string, object>?> GetTenantPluginConfigurationAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);
            var tenantPlugin = tenantPlugins.FirstOrDefault(tp => tp.PluginName == pluginName);
            
            return tenantPlugin?.Configuration;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant plugin configuration for {PluginName} in tenant {TenantId}", pluginName, tenantId);
            return null;
        }
    }

    public async Task<PluginMetrics?> GetTenantPluginMetricsAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get base plugin metrics and filter by tenant if needed
            var metrics = await GetPluginMetricsAsync(pluginName, cancellationToken);
            
            // In a real implementation, you would filter metrics by tenant
            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant plugin metrics for {PluginName} in tenant {TenantId}", pluginName, tenantId);
            return null;
        }
    }

    public async Task<bool> HasReachedPluginLimitAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _tenantService.GetTenantAsync(tenantId, cancellationToken);
            if (tenant == null) return true;

            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);
            var enabledPluginCount = tenantPlugins.Count(tp => tp.IsEnabled);

            return enabledPluginCount >= tenant.Limits.MaxPlugins;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking plugin limit for tenant {TenantId}", tenantId);
            return true; // Err on the side of caution
        }
    }

    private async Task ApplyTenantConfigurationAsync(object plugin, Dictionary<string, object> configuration)
    {
        // Apply tenant-specific configuration to the plugin instance
        // This would depend on the plugin's configuration interface
        
        if (plugin is IGatewayMessagePluginType gatewayPlugin)
        {
            // Apply configuration specific to gateway plugins
            // This is a simplified example
            foreach (var config in configuration)
            {
                // Apply configuration using reflection or a configuration interface
                var property = plugin.GetType().GetProperty(config.Key);
                if (property != null && property.CanWrite)
                {
                    try
                    {
                        property.SetValue(plugin, config.Value);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to set property {PropertyName} on plugin {PluginType}", 
                            config.Key, plugin.GetType().Name);
                    }
                }
            }
        }
    }
}

/// <summary>
/// Extension methods for tenant-aware plugin manager
/// </summary>
public static class TenantAwarePluginManagerExtensions
{
    /// <summary>
    /// Adds tenant-aware plugin manager to the service collection
    /// </summary>
    public static IServiceCollection AddTenantAwarePluginManager(this IServiceCollection services)
    {
        services.AddScoped<ITenantAwarePluginManager, TenantAwarePluginManager>();
        return services;
    }
}
