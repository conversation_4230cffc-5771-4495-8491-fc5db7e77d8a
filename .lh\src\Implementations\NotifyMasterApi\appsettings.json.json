{"sourceFile": "src/Implementations/NotifyMasterApi/appsettings.json", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751216027848, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751217153015, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,8 +12,11 @@\n     \"Audience\": \"NotifyMaster\",\r\n     \"ExpirationMinutes\": 60,\r\n     \"RefreshTokenExpirationDays\": 7\r\n   },\r\n+  \"ConnectionStrings\": {\r\n+    \"DefaultConnection\": \"Host=localhost;Database=notifymaster;Username=postgres;Password=*********"\r\n+  },\r\n   \"MultiTenancy\": {\r\n     \"DefaultTenant\": \"system\",\r\n     \"EnableSubdomainTenantResolution\": true,\r\n     \"EnableHeaderTenantResolution\": true,\r\n"}], "date": 1751216027848, "name": "Commit-0", "content": "{\r\n  \"Logging\": {\r\n    \"LogLevel\": {\r\n      \"Default\": \"Information\",\r\n      \"Microsoft.AspNetCore\": \"Warning\"\r\n    }\r\n  },\r\n  \"AllowedHosts\": \"*\",\r\n  \"Jwt\": {\r\n    \"SecretKey\": \"NotifyMaster-Super-Secret-Key-That-Should-Be-Changed-In-Production-And-Be-At-Least-256-Bits-Long\",\r\n    \"Issuer\": \"NotifyMaster\",\r\n    \"Audience\": \"NotifyMaster\",\r\n    \"ExpirationMinutes\": 60,\r\n    \"RefreshTokenExpirationDays\": 7\r\n  },\r\n  \"MultiTenancy\": {\r\n    \"DefaultTenant\": \"system\",\r\n    \"EnableSubdomainTenantResolution\": true,\r\n    \"EnableHeaderTenantResolution\": true,\r\n    \"TenantHeaderName\": \"X-Tenant-Id\"\r\n  },\r\n  \"ConnectionStrings\": {\r\n    \"DefaultConnection\": \"Host=localhost;Database=NotificationService;Username=postgres;Password=*********",\r\n    \"Redis\": \"localhost:6379\"\r\n  },\r\n  \"Services\": {\r\n    \"EmailService\": {\r\n      \"BaseUrl\": \"http://localhost:5001\"\r\n    },\r\n    \"SmsService\": {\r\n      \"BaseUrl\": \"http://localhost:5002\"\r\n    },\r\n    \"PushService\": {\r\n      \"BaseUrl\": \"http://localhost:5003\"\r\n    }\r\n  },\r\n  \"Serilog\": {\r\n    \"MinimumLevel\": {\r\n      \"Default\": \"Information\",\r\n      \"Override\": {\r\n        \"Microsoft\": \"Information\",\r\n        \"System\": \"Warning\"\r\n      }\r\n    }\r\n  },\r\n  \"ElasticSearch\": {\r\n    \"Uri\": \"http://localhost:9200\"\r\n  },\r\n  \"Plugins\": {\r\n    \"PluginsDirectory\": \"plugins\",\r\n    \"Kavenegar\": {\r\n      \"ApiKey\": \"your-kavenegar-api-key\",\r\n      \"SenderNumber\": \"your-sender-number\",\r\n      \"IsEnabled\": true,\r\n      \"TimeoutSeconds\": 30,\r\n      \"RetryCount\": 3\r\n    },\r\n    \"SMTP\": {\r\n      \"Host\": \"smtp.gmail.com\",\r\n      \"Port\": 587,\r\n      \"UseSsl\": true,\r\n      \"Username\": \"<EMAIL>\",\r\n      \"Password\": \"your-app-password\",\r\n      \"FromEmail\": \"<EMAIL>\",\r\n      \"FromName\": \"Notification Service\",\r\n      \"IsEnabled\": true,\r\n      \"TimeoutSeconds\": 30\r\n    },\r\n    \"Firebase\": {\r\n      \"ServiceAccountKeyPath\": \"path/to/firebase-service-account.json\",\r\n      \"ProjectId\": \"your-firebase-project-id\",\r\n      \"IsEnabled\": true,\r\n      \"TimeoutSeconds\": 30,\r\n      \"DryRun\": false\r\n    }\r\n  }\r\n}\r\n"}]}