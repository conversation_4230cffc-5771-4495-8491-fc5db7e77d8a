{"Version": 1, "Hash": "SrCuackmss6EzaI+Nq1nA6EvtUL1N/M1bczjCZhQ1qU=", "Source": "NotifyMasterApi", "BasePath": "/", "Mode": "Root", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "NotifyMasterApi\\wwwroot", "Source": "NotifyMasterApi", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\11qng3lslz-{0}-90y8c1ti9j-90y8c1ti9j.gz", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=90y8c1ti9j}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vj8lhhvi6b", "Integrity": "ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\index.html", "FileLength": 1066, "LastWriteTime": "2025-06-29T10:25:39+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\9sfek8a7hu-{0}-njhrp14sje-njhrp14sje.gz", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint=njhrp14sje}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0o7neg7o6u", "Integrity": "cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\favicon.ico", "FileLength": 135, "LastWriteTime": "2025-06-29T10:25:39+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\diop0m52io-{0}-z17xhknjbj-z17xhknjbj.gz", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "logo#[.{fingerprint=z17xhknjbj}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0x08ki3dwu", "Integrity": "XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\logo.svg", "FileLength": 1246, "LastWriteTime": "2025-06-29T10:25:39+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\jjzcpskrgx-{0}-gflvcc7cd0-gflvcc7cd0.gz", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "scalar-custom#[.{fingerprint=gflvcc7cd0}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\scalar-custom.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u9qba81k4x", "Integrity": "wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\scalar-custom.css", "FileLength": 2099, "LastWriteTime": "2025-06-29T10:25:39+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\favicon.ico", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "njhrp14sje", "Integrity": "bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 147, "LastWriteTime": "2025-06-29T08:27:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\index.html", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "90y8c1ti9j", "Integrity": "1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 3073, "LastWriteTime": "2025-06-29T09:48:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\logo.png", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\", "BasePath": "/", "RelativePath": "logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "psyieakcwl", "Integrity": "qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\logo.png", "FileLength": 132, "LastWriteTime": "2025-06-29T08:27:43+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\logo.svg", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\", "BasePath": "/", "RelativePath": "logo#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z17xhknjbj", "Integrity": "91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\logo.svg", "FileLength": 3545, "LastWriteTime": "2025-06-29T08:28:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\scalar-custom.css", "SourceId": "NotifyMasterApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\", "BasePath": "/", "RelativePath": "scalar-custom#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gflvcc7cd0", "Integrity": "PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\scalar-custom.css", "FileLength": 9032, "LastWriteTime": "2025-06-29T08:29:28+00:00"}], "Endpoints": [{"Route": "favicon.ico", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\9sfek8a7hu-{0}-njhrp14sje-njhrp14sje.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007352941176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=\""}, {"Name": "ETag", "Value": "W/\"bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "147"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:27:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI="}]}, {"Route": "favicon.ico.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\9sfek8a7hu-{0}-njhrp14sje-njhrp14sje.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc="}]}, {"Route": "favicon.njhrp14sje.ico", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\9sfek8a7hu-{0}-njhrp14sje-njhrp14sje.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.007352941176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=\""}, {"Name": "ETag", "Value": "W/\"bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "njhrp14sje"}, {"Name": "integrity", "Value": "sha256-bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.njhrp14sje.ico", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "147"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:27:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "njhrp14sje"}, {"Name": "integrity", "Value": "sha256-bdGaFbOBCGDhSY+BflKLTxm92HeqxsfR/+NLeWhbhRI="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.njhrp14sje.ico.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\9sfek8a7hu-{0}-njhrp14sje-njhrp14sje.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "135"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "njhrp14sje"}, {"Name": "integrity", "Value": "sha256-cPtQGwuLOP3yknIptvFb/DlBbJmDa6mJEt4dTbPqZnc="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "index.90y8c1ti9j.html", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\11qng3lslz-{0}-90y8c1ti9j-90y8c1ti9j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000937207123"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1066"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=\""}, {"Name": "ETag", "Value": "W/\"1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90y8c1ti9j"}, {"Name": "integrity", "Value": "sha256-1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.90y8c1ti9j.html", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 09:48:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90y8c1ti9j"}, {"Name": "integrity", "Value": "sha256-1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.90y8c1ti9j.html.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\11qng3lslz-{0}-90y8c1ti9j-90y8c1ti9j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1066"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "90y8c1ti9j"}, {"Name": "integrity", "Value": "sha256-ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\11qng3lslz-{0}-90y8c1ti9j-90y8c1ti9j.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000937207123"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1066"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=\""}, {"Name": "ETag", "Value": "W/\"1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 09:48:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1yHBj6UU1qmIszxvmDkdZT3MATd64T/mvLcXuY8Id7g="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\11qng3lslz-{0}-90y8c1ti9j-90y8c1ti9j.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1066"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ddwSujY+jKPG62Qub8Urh6BhLK0ISxg4kcga1Q4VcX8="}]}, {"Route": "logo.png", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "132"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:27:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ="}]}, {"Route": "logo.psyieakcwl.png", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "132"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:27:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "psyieakcwl"}, {"Name": "integrity", "Value": "sha256-qfYaDFGnPowo7bzL3DRSIG0kaN+TgoYZOnLAQ+gOeMQ="}, {"Name": "label", "Value": "logo.png"}]}, {"Route": "logo.svg", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\diop0m52io-{0}-z17xhknjbj-z17xhknjbj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000801924619"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=\""}, {"Name": "ETag", "Value": "W/\"91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM="}]}, {"Route": "logo.svg", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\logo.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3545"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:28:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM="}]}, {"Route": "logo.svg.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\diop0m52io-{0}-z17xhknjbj-z17xhknjbj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc="}]}, {"Route": "logo.z17xhknjbj.svg", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\diop0m52io-{0}-z17xhknjbj-z17xhknjbj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000801924619"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=\""}, {"Name": "ETag", "Value": "W/\"91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z17xhknjbj"}, {"Name": "integrity", "Value": "sha256-91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM="}, {"Name": "label", "Value": "logo.svg"}]}, {"Route": "logo.z17xhknjbj.svg", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\logo.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3545"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:28:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z17xhknjbj"}, {"Name": "integrity", "Value": "sha256-91JAngPo7LoJsobgzSBiRbuX2uHfBky0kgscCA4fjuM="}, {"Name": "label", "Value": "logo.svg"}]}, {"Route": "logo.z17xhknjbj.svg.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\diop0m52io-{0}-z17xhknjbj-z17xhknjbj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1246"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z17xhknjbj"}, {"Name": "integrity", "Value": "sha256-XL3rWwtJSZGjdlFeZh5560uGlChOOPPbsT6Mi2RYIRc="}, {"Name": "label", "Value": "logo.svg.gz"}]}, {"Route": "scalar-custom.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\jjzcpskrgx-{0}-gflvcc7cd0-gflvcc7cd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476190476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2099"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=\""}, {"Name": "ETag", "Value": "W/\"PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk="}]}, {"Route": "scalar-custom.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\scalar-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:29:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk="}]}, {"Route": "scalar-custom.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\jjzcpskrgx-{0}-gflvcc7cd0-gflvcc7cd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2099"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8="}]}, {"Route": "scalar-custom.gflvcc7cd0.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\jjzcpskrgx-{0}-gflvcc7cd0-gflvcc7cd0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000476190476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2099"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=\""}, {"Name": "ETag", "Value": "W/\"PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gflvcc7cd0"}, {"Name": "integrity", "Value": "sha256-PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk="}, {"Name": "label", "Value": "scalar-custom.css"}]}, {"Route": "scalar-custom.gflvcc7cd0.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\wwwroot\\scalar-custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9032"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 08:29:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gflvcc7cd0"}, {"Name": "integrity", "Value": "sha256-PCo7sRUDibsX4Ai1DEKxXIQGrwLk5wuNcae6fT+5imk="}, {"Name": "label", "Value": "scalar-custom.css"}]}, {"Route": "scalar-custom.gflvcc7cd0.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Services\\NotifyMasterApi\\obj\\Release\\net9.0\\compressed\\jjzcpskrgx-{0}-gflvcc7cd0-gflvcc7cd0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2099"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 10:25:39 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gflvcc7cd0"}, {"Name": "integrity", "Value": "sha256-wtMhaXQP6F8Z0aRZTL53rEmaDCxWoaOV5VHdO4cwCw8="}, {"Name": "label", "Value": "scalar-custom.css.gz"}]}]}