using System.ComponentModel.DataAnnotations;
using NotificationContract.Enums;

namespace NotifyMasterApi.Data.Entities;

public class NotificationMetrics
{
    [Key]
    public Guid Id { get; set; }
    
    [Required]
    public NotificationType Type { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Provider { get; set; } = string.Empty;
    
    [Required]
    public DateTime Date { get; set; }
    
    public int TotalSent { get; set; }
    
    public int TotalDelivered { get; set; }
    
    public int TotalFailed { get; set; }
    
    public int TotalRetries { get; set; }
    
    public double AverageResponseTime { get; set; }
    
    public double SuccessRate { get; set; }
    
    public double FailureRate { get; set; }
    
    [Required]
    public DateTime CreatedAt { get; set; }
    
    public DateTime UpdatedAt { get; set; }
}

public class NotificationError
{
    [Key]
    public Guid Id { get; set; }
    
    [Required]
    public NotificationType Type { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Provider { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string ErrorCode { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(1000)]
    public string ErrorMessage { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Recipient { get; set; }
    
    [MaxLength(100)]
    public string? MessageId { get; set; }
    
    public string? StackTrace { get; set; }
    
    public string? RequestData { get; set; }
    
    [Required]
    public DateTime OccurredAt { get; set; }
    
    public int Severity { get; set; } // 1=Low, 2=Medium, 3=High, 4=Critical
    
    public bool IsResolved { get; set; } = false;
    
    public DateTime? ResolvedAt { get; set; }
    
    [MaxLength(1000)]
    public string? Resolution { get; set; }
}
