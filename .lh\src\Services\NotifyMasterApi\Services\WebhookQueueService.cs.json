{"sourceFile": "src/Services/NotifyMasterApi/Services/WebhookQueueService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751192385492, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751192412908, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -50,9 +50,9 @@\n \r\n         return Task.FromResult(webhook.Id);\r\n     }\r\n \r\n-    public async Task<List<OutboundWebhookJob>> GetWebhooksAsync(string? tenantId = null, string? status = null)\r\n+    public Task<List<OutboundWebhookJob>> GetWebhooksAsync(string? tenantId = null, string? status = null)\r\n     {\r\n         var webhooks = _webhookStore.Values.AsEnumerable();\r\n \r\n         if (!string.IsNullOrEmpty(tenantId))\r\n@@ -64,15 +64,15 @@\n         {\r\n             webhooks = webhooks.Where(w => w.Status.Equals(status, StringComparison.OrdinalIgnoreCase));\r\n         }\r\n \r\n-        return webhooks.OrderByDescending(w => w.CreatedAt).ToList();\r\n+        return Task.FromResult(webhooks.OrderByDescending(w => w.CreatedAt).ToList());\r\n     }\r\n \r\n-    public async Task<OutboundWebhookJob?> GetWebhookAsync(string webhookId)\r\n+    public Task<OutboundWebhookJob?> GetWebhookAsync(string webhookId)\r\n     {\r\n         _webhookStore.TryGetValue(webhookId, out var webhook);\r\n-        return webhook;\r\n+        return Task.FromResult(webhook);\r\n     }\r\n \r\n     public async Task<bool> RetryWebhookAsync(string webhookId)\r\n     {\r\n"}], "date": 1751192385492, "name": "Commit-0", "content": "using Hangfire;\r\nusing Microsoft.Extensions.Logging;\r\nusing NotifyMasterApi.Infrastructure;\r\nusing System.Text;\r\nusing System.Text.Json;\r\n\r\nnamespace NotifyMasterApi.Services;\r\n\r\npublic interface IWebhookQueueService\r\n{\r\n    Task<string> QueueWebhookAsync(OutboundWebhookJob webhook);\r\n    Task<List<OutboundWebhookJob>> GetWebhooksAsync(string? tenantId = null, string? status = null);\r\n    Task<OutboundWebhookJob?> GetWebhookAsync(string webhookId);\r\n    Task<bool> RetryWebhookAsync(string webhookId);\r\n    Task<bool> CancelWebhookAsync(string webhookId);\r\n    Task<List<WebhookRetryResult>> GetWebhookHistoryAsync(string webhookId);\r\n}\r\n\r\npublic class WebhookQueueService : IWebhookQueueService\r\n{\r\n    private readonly IBackgroundJobClient _backgroundJobClient;\r\n    private readonly IHttpClientFactory _httpClientFactory;\r\n    private readonly ILogger<WebhookQueueService> _logger;\r\n    private readonly Dictionary<string, OutboundWebhookJob> _webhookStore = new();\r\n    private readonly Dictionary<string, List<WebhookRetryResult>> _webhookHistory = new();\r\n\r\n    public WebhookQueueService(\r\n        IBackgroundJobClient backgroundJobClient,\r\n        IHttpClientFactory httpClientFactory,\r\n        ILogger<WebhookQueueService> logger)\r\n    {\r\n        _backgroundJobClient = backgroundJobClient;\r\n        _httpClientFactory = httpClientFactory;\r\n        _logger = logger;\r\n    }\r\n\r\n    public Task<string> QueueWebhookAsync(OutboundWebhookJob webhook)\r\n    {\r\n        webhook.Id = Guid.NewGuid().ToString();\r\n        webhook.Status = \"Pending\";\r\n        webhook.CreatedAt = DateTime.UtcNow;\r\n\r\n        _webhookStore[webhook.Id] = webhook;\r\n        _webhookHistory[webhook.Id] = new List<WebhookRetryResult>();\r\n\r\n        // Queue the webhook for immediate processing\r\n        var jobId = _backgroundJobClient.Enqueue(() => ProcessWebhookAsync(webhook.Id));\r\n\r\n        _logger.LogInformation(\"Queued webhook {WebhookId} for {Url}\", webhook.Id, webhook.Url);\r\n\r\n        return Task.FromResult(webhook.Id);\r\n    }\r\n\r\n    public async Task<List<OutboundWebhookJob>> GetWebhooksAsync(string? tenantId = null, string? status = null)\r\n    {\r\n        var webhooks = _webhookStore.Values.AsEnumerable();\r\n\r\n        if (!string.IsNullOrEmpty(tenantId))\r\n        {\r\n            webhooks = webhooks.Where(w => w.TenantId == tenantId);\r\n        }\r\n\r\n        if (!string.IsNullOrEmpty(status))\r\n        {\r\n            webhooks = webhooks.Where(w => w.Status.Equals(status, StringComparison.OrdinalIgnoreCase));\r\n        }\r\n\r\n        return webhooks.OrderByDescending(w => w.CreatedAt).ToList();\r\n    }\r\n\r\n    public async Task<OutboundWebhookJob?> GetWebhookAsync(string webhookId)\r\n    {\r\n        _webhookStore.TryGetValue(webhookId, out var webhook);\r\n        return webhook;\r\n    }\r\n\r\n    public async Task<bool> RetryWebhookAsync(string webhookId)\r\n    {\r\n        if (!_webhookStore.TryGetValue(webhookId, out var webhook))\r\n        {\r\n            return false;\r\n        }\r\n\r\n        if (webhook.Status == \"Completed\")\r\n        {\r\n            return false; // Already completed\r\n        }\r\n\r\n        webhook.Status = \"Pending\";\r\n        webhook.NextRetryAt = null;\r\n        \r\n        var jobId = _backgroundJobClient.Enqueue(() => ProcessWebhookAsync(webhookId));\r\n        \r\n        _logger.LogInformation(\"Manually retrying webhook {WebhookId}\", webhookId);\r\n        \r\n        return true;\r\n    }\r\n\r\n    public async Task<bool> CancelWebhookAsync(string webhookId)\r\n    {\r\n        if (!_webhookStore.TryGetValue(webhookId, out var webhook))\r\n        {\r\n            return false;\r\n        }\r\n\r\n        webhook.Status = \"Cancelled\";\r\n        \r\n        _logger.LogInformation(\"Cancelled webhook {WebhookId}\", webhookId);\r\n        \r\n        return true;\r\n    }\r\n\r\n    public async Task<List<WebhookRetryResult>> GetWebhookHistoryAsync(string webhookId)\r\n    {\r\n        _webhookHistory.TryGetValue(webhookId, out var history);\r\n        return history ?? new List<WebhookRetryResult>();\r\n    }\r\n\r\n    [AutomaticRetry(Attempts = 0)] // We handle retries manually\r\n    public async Task ProcessWebhookAsync(string webhookId)\r\n    {\r\n        if (!_webhookStore.TryGetValue(webhookId, out var webhook))\r\n        {\r\n            _logger.LogWarning(\"Webhook {WebhookId} not found\", webhookId);\r\n            return;\r\n        }\r\n\r\n        if (webhook.Status == \"Cancelled\" || webhook.Status == \"Completed\")\r\n        {\r\n            return;\r\n        }\r\n\r\n        webhook.Status = \"Processing\";\r\n        \r\n        var result = await ExecuteWebhookAsync(webhook);\r\n        \r\n        if (!_webhookHistory.ContainsKey(webhookId))\r\n        {\r\n            _webhookHistory[webhookId] = new List<WebhookRetryResult>();\r\n        }\r\n        _webhookHistory[webhookId].Add(result);\r\n\r\n        if (result.Success)\r\n        {\r\n            webhook.Status = \"Completed\";\r\n            _logger.LogInformation(\"Webhook {WebhookId} completed successfully\", webhookId);\r\n        }\r\n        else\r\n        {\r\n            webhook.RetryCount++;\r\n            webhook.LastError = result.Error;\r\n\r\n            if (webhook.RetryCount >= webhook.MaxRetries)\r\n            {\r\n                webhook.Status = \"Failed\";\r\n                _logger.LogError(\"Webhook {WebhookId} failed permanently after {RetryCount} attempts\", \r\n                    webhookId, webhook.RetryCount);\r\n            }\r\n            else\r\n            {\r\n                webhook.Status = \"Pending\";\r\n                var delay = CalculateRetryDelay(webhook.RetryCount);\r\n                webhook.NextRetryAt = DateTime.UtcNow.Add(delay);\r\n                \r\n                // Schedule retry\r\n                _backgroundJobClient.Schedule(() => ProcessWebhookAsync(webhookId), delay);\r\n                \r\n                _logger.LogWarning(\"Webhook {WebhookId} failed, scheduling retry {RetryCount}/{MaxRetries} in {Delay}\", \r\n                    webhookId, webhook.RetryCount, webhook.MaxRetries, delay);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async Task<WebhookRetryResult> ExecuteWebhookAsync(OutboundWebhookJob webhook)\r\n    {\r\n        var result = new WebhookRetryResult();\r\n        var stopwatch = System.Diagnostics.Stopwatch.StartNew();\r\n\r\n        try\r\n        {\r\n            using var httpClient = _httpClientFactory.CreateClient();\r\n            httpClient.Timeout = TimeSpan.FromSeconds(30);\r\n\r\n            var request = new HttpRequestMessage(\r\n                new HttpMethod(webhook.Method), \r\n                webhook.Url);\r\n\r\n            // Add headers\r\n            foreach (var header in webhook.Headers)\r\n            {\r\n                request.Headers.TryAddWithoutValidation(header.Key, header.Value);\r\n            }\r\n\r\n            // Add payload\r\n            if (!string.IsNullOrEmpty(webhook.Payload))\r\n            {\r\n                request.Content = new StringContent(webhook.Payload, Encoding.UTF8, \"application/json\");\r\n            }\r\n\r\n            var response = await httpClient.SendAsync(request);\r\n            \r\n            result.StatusCode = (int)response.StatusCode;\r\n            result.Response = await response.Content.ReadAsStringAsync();\r\n            result.Success = response.IsSuccessStatusCode;\r\n            \r\n            if (!result.Success)\r\n            {\r\n                result.Error = $\"HTTP {result.StatusCode}: {result.Response}\";\r\n            }\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            result.Success = false;\r\n            result.Error = ex.Message;\r\n            result.StatusCode = 0;\r\n        }\r\n        finally\r\n        {\r\n            stopwatch.Stop();\r\n            result.Duration = stopwatch.Elapsed;\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    private TimeSpan CalculateRetryDelay(int retryCount)\r\n    {\r\n        // Exponential backoff: 30s, 1m, 2m, 4m, 8m, max 15m\r\n        var baseDelay = TimeSpan.FromSeconds(30);\r\n        var exponentialDelay = TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, retryCount - 1));\r\n        var maxDelay = TimeSpan.FromMinutes(15);\r\n        \r\n        return exponentialDelay > maxDelay ? maxDelay : exponentialDelay;\r\n    }\r\n}\r\n\r\npublic interface IEventStreamService\r\n{\r\n    Task PublishEventAsync(SystemEvent systemEvent);\r\n    Task<List<SystemEvent>> GetEventsAsync(string? tenantId = null, string? type = null, DateTime? since = null);\r\n    Task SubscribeToEventsAsync(string connectionId, string? tenantId = null);\r\n    Task UnsubscribeFromEventsAsync(string connectionId);\r\n}\r\n\r\npublic class EventStreamService : IEventStreamService\r\n{\r\n    private readonly ILogger<EventStreamService> _logger;\r\n    private readonly List<SystemEvent> _eventStore = new();\r\n    private readonly Dictionary<string, string?> _subscriptions = new(); // connectionId -> tenantId\r\n\r\n    public EventStreamService(ILogger<EventStreamService> logger)\r\n    {\r\n        _logger = logger;\r\n    }\r\n\r\n    public async Task PublishEventAsync(SystemEvent systemEvent)\r\n    {\r\n        systemEvent.Id = Guid.NewGuid().ToString();\r\n        systemEvent.Timestamp = DateTime.UtcNow;\r\n        \r\n        _eventStore.Add(systemEvent);\r\n        \r\n        // Keep only last 10000 events in memory\r\n        if (_eventStore.Count > 10000)\r\n        {\r\n            _eventStore.RemoveRange(0, _eventStore.Count - 10000);\r\n        }\r\n\r\n        _logger.LogDebug(\"Published event {EventId} of type {Type} for tenant {TenantId}\", \r\n            systemEvent.Id, systemEvent.Type, systemEvent.TenantId);\r\n\r\n        // In a real implementation, this would push to SignalR clients\r\n        await NotifySubscribersAsync(systemEvent);\r\n    }\r\n\r\n    public async Task<List<SystemEvent>> GetEventsAsync(string? tenantId = null, string? type = null, DateTime? since = null)\r\n    {\r\n        var events = _eventStore.AsEnumerable();\r\n\r\n        if (!string.IsNullOrEmpty(tenantId))\r\n        {\r\n            events = events.Where(e => e.TenantId == tenantId);\r\n        }\r\n\r\n        if (!string.IsNullOrEmpty(type))\r\n        {\r\n            events = events.Where(e => e.Type.Equals(type, StringComparison.OrdinalIgnoreCase));\r\n        }\r\n\r\n        if (since.HasValue)\r\n        {\r\n            events = events.Where(e => e.Timestamp >= since.Value);\r\n        }\r\n\r\n        return events.OrderByDescending(e => e.Timestamp).Take(1000).ToList();\r\n    }\r\n\r\n    public async Task SubscribeToEventsAsync(string connectionId, string? tenantId = null)\r\n    {\r\n        _subscriptions[connectionId] = tenantId;\r\n        _logger.LogDebug(\"Connection {ConnectionId} subscribed to events for tenant {TenantId}\", \r\n            connectionId, tenantId ?? \"all\");\r\n    }\r\n\r\n    public async Task UnsubscribeFromEventsAsync(string connectionId)\r\n    {\r\n        _subscriptions.Remove(connectionId);\r\n        _logger.LogDebug(\"Connection {ConnectionId} unsubscribed from events\", connectionId);\r\n    }\r\n\r\n    private async Task NotifySubscribersAsync(SystemEvent systemEvent)\r\n    {\r\n        // In a real implementation, this would use SignalR to notify connected clients\r\n        var relevantSubscriptions = _subscriptions.Where(s => \r\n            s.Value == null || s.Value == systemEvent.TenantId);\r\n\r\n        foreach (var subscription in relevantSubscriptions)\r\n        {\r\n            // SignalR notification would go here\r\n            _logger.LogDebug(\"Would notify connection {ConnectionId} of event {EventId}\", \r\n                subscription.Key, systemEvent.Id);\r\n        }\r\n    }\r\n}\r\n"}]}