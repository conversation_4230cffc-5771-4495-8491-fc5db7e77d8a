{"sourceFile": "src/Core/PluginCore/Base/NotificationResponse.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751199013341, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751199013341, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic class NotificationResponse\r\n{\r\n    public bool IsSuccess { get; set; }\r\n    public string? ErrorMessage { get; set; }\r\n    public string? Response { get; set; }\r\n    public Dictionary<string, object>? ResponseData { get; set; }\r\n    public Dictionary<string, object>? Metadata { get; set; }\r\n    public Dictionary<string, object>? Headers { get; set; }\r\n    public Dictionary<string, object>? QueryParameters { get; set; }\r\n    public Dictionary<string, object>? FormParameters { get; set; }\r\n    public Dictionary<string, object>? Cookies { get; set; }\r\n    public Dictionary<string, object>? Files { get; set; }\r\n    public Dictionary<string, object>? Session { get; set; }\r\n    public Dictionary<string, object>? ServerVariables { get; set; }\r\n    public Dictionary<string, object>? ClientVariables { get; set; }\r\n    public Dictionary<string, object>? CustomVariables { get; set; }\r\n    public Dictionary<string, object>? RequestData { get; set; }\r\n    public Dictionary<string, object>? ErrorData { get; set; }\r\n    public Dictionary<string, object>? LogData { get; set; }\r\n    public Dictionary<string, object>? CustomData { get; set; }\r\n    public string? MessageId { get; set; }\r\n    public string? ProviderMessageId { get; set; }\r\n    public string? Recipient { get; set; }\r\n    public string? Status { get; set; }\r\n    public string? Reason { get; set; }\r\n    public string? ProviderResponse { get; set; }\r\n    public string? ProviderErrorCode { get; set; }\r\n    public string? ProviderErrorMessage { get; set; }\r\n    public string? ProviderErrorDetails { get; set; }\r\n    public string? ProviderErrorResolution { get; set; }\r\n    public string? ProviderErrorDocumentation { get; set; }\r\n    public string? ProviderErrorSupport { get; set; }\r\n    public string? ProviderErrorContact { get; set; }\r\n    public string? ProviderErrorPhone { get; set; }\r\n    public string? ProviderErrorEmail { get; set; }\r\n    public string? ProviderErrorUrl { get; set; }\r\n    public string? ProviderErrorOther { get; set; }\r\n    public string? ProviderErrorAdditionalInfo { get; set; }\r\n\r\n    public static explicit operator NotificationResponse(MessageSendResult result)\r\n        => new NotificationResponse\r\n        {\r\n            IsSuccess = result.IsSuccess,\r\n            ErrorMessage = result.ErrorMessage,\r\n            ResponseData = result.ResponseData,\r\n            MessageId = result.MessageId,\r\n            ProviderMessageId = result.ProviderMessageId,\r\n            Recipient = result.Recipient,\r\n            Status = result.Status,\r\n            Reason = result.Reason\r\n        };\r\n\r\n        public static explicit operator NotificationResponse(MessageStatusInfo statusInfo)\r\n        => new NotificationResponse\r\n        {\r\n            IsSuccess = statusInfo.Status == \"delivered\",\r\n            ErrorMessage = statusInfo.Reason,\r\n            ResponseData = new Dictionary<string, object>\r\n            {\r\n                [\"status\"] = statusInfo.Status,\r\n                [\"reason\"] = statusInfo.Reason,\r\n                [\"providerResponse\"] = statusInfo.ProviderResponse,\r\n                [\"providerErrorCode\"] = statusInfo.ProviderErrorCode,\r\n                [\"providerErrorMessage\"] = statusInfo.ProviderErrorMessage,\r\n                [\"providerErrorDetails\"] = statusInfo.ProviderErrorDetails,\r\n                [\"providerErrorResolution\"] = statusInfo.ProviderErrorResolution,\r\n                [\"providerErrorDocumentation\"] = statusInfo.ProviderErrorDocumentation,\r\n                [\"providerErrorSupport\"] = statusInfo.ProviderErrorSupport,\r\n                [\"providerErrorContact\"] = statusInfo.ProviderErrorContact,\r\n                [\"providerErrorPhone\"] = statusInfo.ProviderErrorPhone,\r\n                [\"providerErrorEmail\"] = statusInfo.ProviderErrorEmail,\r\n                [\"providerErrorUrl\"] = statusInfo.ProviderErrorUrl,\r\n                [\"providerErrorOther\"] = statusInfo.ProviderErrorOther,\r\n                [\"providerErrorAdditionalInfo\"] = statusInfo.ProviderErrorAdditionalInfo\r\n            }\r\n        };\r\n\r\n}\r\n"}]}