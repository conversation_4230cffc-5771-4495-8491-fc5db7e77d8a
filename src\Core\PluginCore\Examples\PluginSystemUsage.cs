using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PluginCore.Extensions;
using PluginCore.Interfaces;
using PluginCore.Models;
using PluginCore.Services;

namespace PluginCore.Examples;

/// <summary>
/// Example demonstrating how to use the new gateway plugin system.
/// </summary>
public class PluginSystemUsageExample
{
    /// <summary>
    /// Example of setting up the plugin system in a console application.
    /// </summary>
    public static async Task ConsoleApplicationExample()
    {
        // Create service collection
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging();
        
        // Add the complete gateway plugin system
        services.AddCompleteGatewayPluginSystem(
            pluginDirectory: "plugins",
            autoLoad: true
        );

        // Build service provider
        var serviceProvider = services.BuildServiceProvider();
        
        // Get logger
        var logger = serviceProvider.GetRequiredService<ILogger<PluginSystemUsageExample>>();
        
        // If auto-load is disabled, load plugins manually
        // await serviceProvider.LoadPluginsAsync("plugins", logger);

        // Use the plugins
        await UsePluginsExample(serviceProvider, logger);
    }

    /// <summary>
    /// Example of setting up the plugin system in an ASP.NET Core application.
    /// </summary>
    public static void AspNetCoreExample(IServiceCollection services)
    {
        // Add the complete gateway plugin system
        services.AddCompleteGatewayPluginSystem(
            pluginDirectory: "plugins",
            autoLoad: true
        );

        // Or configure manually
        services.AddGatewayPluginSystem()
               .ConfigureGatewayPluginSystem(options =>
               {
                   options.PluginDirectory = "plugins";
                   options.AutoLoadPlugins = true;
                   options.EnableHealthMonitoring = true;
                   options.EnableMessageScheduling = true;
                   options.EnableMessageStorage = true;
               })
               .AddPluginLoader();
    }

    /// <summary>
    /// Example of using loaded plugins.
    /// </summary>
    public static async Task UsePluginsExample(IServiceProvider serviceProvider, ILogger logger)
    {
        try
        {
            // Get plugin manager
            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
            
            // Check plugin status
            var statuses = await pluginManager.GetPluginStatusesAsync();
            logger.LogInformation("Found {Count} loaded plugins", statuses.Count);
            
            foreach (var status in statuses)
            {
                logger.LogInformation("Plugin: {Name} v{Version} - Status: {Status}", 
                    status.Name, status.Version, status.Status);
            }

            // Get SMS plugins
            var smsPlugins = pluginManager.GetPlugins<ISmsPlugin>();
            if (smsPlugins.Any())
            {
                logger.LogInformation("Found {Count} SMS plugins", smsPlugins.Count);
                
                var smsPlugin = smsPlugins.First();
                await SendSmsExample(smsPlugin, logger);
            }

            // Get Email plugins
            var emailPlugins = pluginManager.GetPlugins<IEmailPlugin>();
            if (emailPlugins.Any())
            {
                logger.LogInformation("Found {Count} Email plugins", emailPlugins.Count);
                
                var emailPlugin = emailPlugins.First();
                await SendEmailExample(emailPlugin, logger);
            }

            // Get Push plugins
            var pushPlugins = pluginManager.GetPlugins<IPushPlugin>();
            if (pushPlugins.Any())
            {
                logger.LogInformation("Found {Count} Push plugins", pushPlugins.Count);
                
                var pushPlugin = pushPlugins.First();
                await SendPushExample(pushPlugin, logger);
            }

            // Schedule a message example
            await ScheduleMessageExample(smsPlugins.FirstOrDefault(), logger);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error using plugins");
        }
    }

    /// <summary>
    /// Example of sending an SMS message.
    /// </summary>
    private static async Task SendSmsExample(ISmsPlugin smsPlugin, ILogger logger)
    {
        try
        {
            var payload = new MessagePayload(
                Recipient: "+1234567890",
                Content: "Hello from the new plugin system!",
                From: "NotificationService"
            );

            var result = await smsPlugin.SendMessageAsync(payload);
            
            if (result.Status == "Sent")
            {
                logger.LogInformation("SMS sent successfully. Message ID: {MessageId}", result.MessageId);
            }
            else
            {
                logger.LogWarning("SMS failed to send. Status: {Status}", result.Status);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to send SMS");
        }
    }

    /// <summary>
    /// Example of sending an email message.
    /// </summary>
    private static async Task SendEmailExample(IEmailPlugin emailPlugin, ILogger logger)
    {
        try
        {
            var payload = new MessagePayload(
                Recipient: "<EMAIL>",
                Content: "Hello from the new plugin system!",
                Subject: "Test Email",
                From: "<EMAIL>"
            );

            var result = await emailPlugin.SendMessageAsync(payload);
            
            if (result.Status == "Sent")
            {
                logger.LogInformation("Email sent successfully. Message ID: {MessageId}", result.MessageId);
            }
            else
            {
                logger.LogWarning("Email failed to send. Status: {Status}", result.Status);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to send email");
        }
    }

    /// <summary>
    /// Example of sending a push notification.
    /// </summary>
    private static async Task SendPushExample(IPushPlugin pushPlugin, ILogger logger)
    {
        try
        {
            var payload = new MessagePayload(
                Recipient: "device_token_here",
                Content: "Hello from the new plugin system!",
                Subject: "Test Push Notification"
            );

            var result = await pushPlugin.SendMessageAsync(payload);
            
            if (result.Status == "Sent")
            {
                logger.LogInformation("Push notification sent successfully. Message ID: {MessageId}", result.MessageId);
            }
            else
            {
                logger.LogWarning("Push notification failed to send. Status: {Status}", result.Status);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to send push notification");
        }
    }

    /// <summary>
    /// Example of scheduling a message.
    /// </summary>
    private static async Task ScheduleMessageExample(ISmsPlugin? smsPlugin, ILogger logger)
    {
        if (smsPlugin == null)
        {
            logger.LogWarning("No SMS plugin available for scheduling example");
            return;
        }

        try
        {
            var payload = new MessagePayload(
                Recipient: "+1234567890",
                Content: "This is a scheduled message!",
                From: "NotificationService"
            );

            var scheduledTime = DateTimeOffset.UtcNow.AddMinutes(5);
            var result = await smsPlugin.ScheduleMessageAsync(payload, scheduledTime);
            
            if (result.Status == "Scheduled")
            {
                logger.LogInformation("Message scheduled successfully. Schedule ID: {ScheduleId}", result.ScheduledMessageId);

                // Cancel the scheduled message after 2 minutes (for demo purposes)
                await Task.Delay(TimeSpan.FromMinutes(2));
                var cancelResult = await smsPlugin.CancelScheduledMessageAsync(result.ScheduledMessageId);

                if (cancelResult.IsSuccess)
                {
                    logger.LogInformation("Scheduled message cancelled successfully");
                }
            }
            else
            {
                logger.LogWarning("Failed to schedule message: {Status}", result.Status);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to schedule message");
        }
    }

    /// <summary>
    /// Example of plugin health monitoring.
    /// </summary>
    public static async Task HealthMonitoringExample(IServiceProvider serviceProvider, ILogger logger)
    {
        try
        {
            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
            
            // Check health of all plugins
            var statuses = await pluginManager.GetPluginStatusesAsync();
            
            foreach (var status in statuses)
            {
                if (status.IsHealthy)
                {
                    logger.LogInformation("Plugin {Name} is healthy", status.Name);
                }
                else
                {
                    logger.LogWarning("Plugin {Name} is unhealthy: {ErrorMessage}", 
                        status.Name, status.ErrorMessage);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to check plugin health");
        }
    }

    /// <summary>
    /// Example of plugin discovery.
    /// </summary>
    public static void PluginDiscoveryExample(ILogger logger)
    {
        try
        {
            var pluginFiles = PluginDiscovery.DiscoverPluginFiles("plugins");
            
            logger.LogInformation("Discovered {Count} potential plugin files", pluginFiles.Count());
            
            foreach (var pluginFile in pluginFiles)
            {
                var fileInfo = PluginDiscovery.GetPluginFileInfo(pluginFile);
                if (fileInfo != null)
                {
                    logger.LogInformation("Plugin file: {FileName} ({Size} bytes, modified: {LastModified})",
                        fileInfo.FileName, fileInfo.Size, fileInfo.LastModified);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to discover plugins");
        }
    }
}
