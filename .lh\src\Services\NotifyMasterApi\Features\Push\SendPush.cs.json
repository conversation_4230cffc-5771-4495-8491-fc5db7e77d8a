{"sourceFile": "src/Services/NotifyMasterApi/Features/Push/SendPush.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751190696185, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751190696185, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing NotifyMasterApi.Gateways;\r\nusing NotificationContract.Models;\r\nusing NotifyMasterApi.Documentation;\r\nusing System.ComponentModel.DataAnnotations;\r\n\r\nnamespace NotifyMasterApi.Features.Push;\r\n\r\n/// <summary>\r\n/// Request model for sending a push notification\r\n/// </summary>\r\npublic class SendPushRequest\r\n{\r\n    /// <summary>\r\n    /// Device token for the target device (required)\r\n    /// </summary>\r\n    /// <example>device_token_here_1234567890abcdef</example>\r\n    [Required(ErrorMessage = \"Device token is required\")]\r\n    [StringLength(500, MinimumLength = 10, ErrorMessage = \"Device token must be between 10 and 500 characters\")]\r\n    public string DeviceToken { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Notification title (required)\r\n    /// </summary>\r\n    /// <example>New Message</example>\r\n    [Required(ErrorMessage = \"Notification title is required\")]\r\n    [StringLength(100, MinimumLength = 1, ErrorMessage = \"Title must be between 1 and 100 characters\")]\r\n    public string Title { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Notification message body (required)\r\n    /// </summary>\r\n    /// <example>You have a new notification</example>\r\n    [Required(ErrorMessage = \"Notification message is required\")]\r\n    [StringLength(500, MinimumLength = 1, ErrorMessage = \"Message must be between 1 and 500 characters\")]\r\n    public string Message { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// URL to an image to display in the notification (optional)\r\n    /// </summary>\r\n    /// <example>https://example.com/image.png</example>\r\n    [Url(ErrorMessage = \"Invalid image URL format\")]\r\n    public string? ImageUrl { get; set; }\r\n\r\n    /// <summary>\r\n    /// Custom data payload for the notification (optional)\r\n    /// </summary>\r\n    /// <example>{\"action\": \"open_chat\", \"chatId\": \"chat_123\"}</example>\r\n    public Dictionary<string, string>? Data { get; set; }\r\n\r\n    /// <summary>\r\n    /// Target platform (auto-detected if not specified)\r\n    /// </summary>\r\n    /// <example>ios, android, web</example>\r\n    public string? Platform { get; set; }\r\n\r\n    /// <summary>\r\n    /// Additional metadata for tracking and analytics\r\n    /// </summary>\r\n    /// <example>{\"campaign\": \"promotion\", \"userId\": \"12345\"}</example>\r\n    public Dictionary<string, object>? Metadata { get; set; }\r\n\r\n    /// <summary>\r\n    /// Notification priority level\r\n    /// </summary>\r\n    /// <example>high, normal, low</example>\r\n    public string? Priority { get; set; }\r\n\r\n    /// <summary>\r\n    /// Sound to play with the notification\r\n    /// </summary>\r\n    /// <example>default, custom_sound.wav</example>\r\n    public string? Sound { get; set; }\r\n\r\n    /// <summary>\r\n    /// Badge count for iOS notifications\r\n    /// </summary>\r\n    /// <example>1</example>\r\n    [Range(0, 999, ErrorMessage = \"Badge count must be between 0 and 999\")]\r\n    public int? Badge { get; set; }\r\n\r\n    /// <summary>\r\n    /// Time to live for the notification (in seconds)\r\n    /// </summary>\r\n    /// <example>3600</example>\r\n    [Range(1, 2419200, ErrorMessage = \"TTL must be between 1 second and 28 days\")]\r\n    public int? TimeToLive { get; set; }\r\n}\r\n\r\n/// <summary>\r\n/// Response model for push notification sending operation\r\n/// </summary>\r\npublic class SendPushResponse\r\n{\r\n    /// <summary>\r\n    /// Indicates whether the push notification was sent successfully\r\n    /// </summary>\r\n    /// <example>true</example>\r\n    public bool Success { get; set; }\r\n\r\n    /// <summary>\r\n    /// Unique identifier for the sent notification (available when successful)\r\n    /// </summary>\r\n    /// <example>push_def456ghi789</example>\r\n    public string? MessageId { get; set; }\r\n\r\n    /// <summary>\r\n    /// Error message if the operation failed\r\n    /// </summary>\r\n    /// <example>Invalid device token</example>\r\n    public string? Error { get; set; }\r\n\r\n    /// <summary>\r\n    /// Timestamp when the operation was completed\r\n    /// </summary>\r\n    /// <example>2024-01-15T10:30:00Z</example>\r\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n\r\n    /// <summary>\r\n    /// Push notification provider used for sending\r\n    /// </summary>\r\n    /// <example>FCM, APNS, OneSignal</example>\r\n    public string? Provider { get; set; }\r\n\r\n    /// <summary>\r\n    /// Platform that received the notification\r\n    /// </summary>\r\n    /// <example>ios, android, web</example>\r\n    public string? Platform { get; set; }\r\n\r\n    /// <summary>\r\n    /// Delivery status information\r\n    /// </summary>\r\n    /// <example>queued, sent, delivered, failed</example>\r\n    public string? DeliveryStatus { get; set; }\r\n\r\n    /// <summary>\r\n    /// Additional response data from the provider\r\n    /// </summary>\r\n    public Dictionary<string, object>? ProviderResponse { get; set; }\r\n}\r\n\r\npublic class SendPushEndpoint : Endpoint<SendPushRequest, SendPushResponse>\r\n{\r\n    private readonly IPushGateway _pushGateway;\r\n    private readonly ILogger<SendPushEndpoint> _logger;\r\n\r\n    public SendPushEndpoint(IPushGateway pushGateway, ILogger<SendPushEndpoint> logger)\r\n    {\r\n        _pushGateway = pushGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        this.ConfigureNotificationEndpoint(\r\n            \"POST\",\r\n            \"/api/push/send\",\r\n            \"Send Push Notification\",\r\n            \"Send a push notification to mobile devices and web browsers through configured push notification providers.\\n\\n\" +\r\n            \"## 🎯 Features\\n\" +\r\n            \"- **Multi-Platform Support**: iOS, Android, and Web push notifications\\n\" +\r\n            \"- **Rich Notifications**: Images, custom sounds, and interactive elements\\n\" +\r\n            \"- **Smart Delivery**: Automatic platform detection and optimization\\n\" +\r\n            \"- **Delivery Tracking**: Real-time delivery status and analytics\\n\" +\r\n            \"- **Batch Processing**: Send to multiple devices efficiently\\n\\n\" +\r\n            \"## 📋 Provider Support\\n\" +\r\n            \"- **Firebase Cloud Messaging (FCM)**: Android and Web\\n\" +\r\n            \"- **Apple Push Notification Service (APNS)**: iOS\\n\" +\r\n            \"- **OneSignal**: Cross-platform solution\\n\" +\r\n            \"- **Amazon SNS**: AWS-based push notifications\\n\" +\r\n            \"- **Custom Push Plugins**: Extensible provider system\\n\\n\" +\r\n            \"## 📱 Platform Features\\n\" +\r\n            \"### iOS (APNS)\\n- Rich media notifications with images\\n- Custom sounds and badge counts\\n- Silent notifications for background updates\\n- Notification categories and actions\\n\\n\" +\r\n            \"### Android (FCM)\\n- High priority messaging\\n- Notification channels and groups\\n- Custom icons and colors\\n- Direct reply and action buttons\\n\\n\" +\r\n            \"### Web Push\\n- Browser notifications for PWAs\\n- Service worker integration\\n- Offline message queuing\\n- Click tracking and analytics\\n\\n\" +\r\n            \"## ⚡ Rate Limits\\n\" +\r\n            \"- **Default**: 1000 notifications/minute per API key\\n\" +\r\n            \"- **Burst**: Up to 5000 notifications in 10 seconds\\n\" +\r\n            \"- **Daily**: 100,000 notifications per day (configurable)\\n\\n\" +\r\n            \"## 🔒 Security & Privacy\\n\" +\r\n            \"- Device token validation and encryption\\n- Message content filtering\\n- User consent tracking\\n- GDPR compliance features\",\r\n            \"Push\",\r\n            new[] { \"Core Messaging\", \"Mobile Notifications\", \"Real-time\" }\r\n        );\r\n    }\r\n\r\n    public override async Task HandleAsync(SendPushRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            _logger.LogInformation(\"Sending push notification to device {DeviceToken}\", req.DeviceToken);\r\n\r\n            var pushRequest = new PushMessageRequest\r\n            {\r\n                DeviceToken = req.DeviceToken,\r\n                Title = req.Title,\r\n                Message = req.Message,\r\n                ImageUrl = req.ImageUrl,\r\n                Data = req.Data,\r\n                Platform = req.Platform,\r\n                Metadata = req.Metadata\r\n            };\r\n\r\n            var result = await _pushGateway.SendAsync(pushRequest);\r\n\r\n            if (result.IsSuccess)\r\n            {\r\n                await SendOkAsync(new SendPushResponse\r\n                {\r\n                    Success = true,\r\n                    MessageId = result.MessageId\r\n                }, ct);\r\n            }\r\n            else\r\n            {\r\n                await SendAsync(new SendPushResponse\r\n                {\r\n                    Success = false,\r\n                    Error = result.ErrorMessage\r\n                }, 400, ct);\r\n            }\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error sending push notification\");\r\n            await SendAsync(new SendPushResponse\r\n            {\r\n                Success = false,\r\n                Error = \"Internal server error\"\r\n            }, 500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class SendBulkPushRequest\r\n{\r\n    public List<SendPushRequest> Messages { get; set; } = new();\r\n}\r\n\r\npublic class SendBulkPushResponse\r\n{\r\n    public bool Success { get; set; }\r\n    public int SuccessCount { get; set; }\r\n    public int FailureCount { get; set; }\r\n    public List<SendPushResponse> Results { get; set; } = new();\r\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n}\r\n\r\npublic class SendBulkPushEndpoint : Endpoint<SendBulkPushRequest, SendBulkPushResponse>\r\n{\r\n    private readonly IPushGateway _pushGateway;\r\n    private readonly ILogger<SendBulkPushEndpoint> _logger;\r\n\r\n    public SendBulkPushEndpoint(IPushGateway pushGateway, ILogger<SendBulkPushEndpoint> logger)\r\n    {\r\n        _pushGateway = pushGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/push/send/bulk\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Send bulk push notifications\";\r\n            s.Description = \"Send multiple push notifications through available push plugins\";\r\n            s.Responses[200] = \"Bulk push notifications processed\";\r\n            s.Responses[400] = \"Invalid request\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Push\");\r\n    }\r\n\r\n    public override async Task HandleAsync(SendBulkPushRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            _logger.LogInformation(\"Sending bulk push notifications to {Count} devices\", req.Messages.Count);\r\n\r\n            var bulkRequest = new BulkPushRequest\r\n            {\r\n                Messages = req.Messages.Select(m => new PushMessageRequest\r\n                {\r\n                    DeviceToken = m.DeviceToken,\r\n                    Title = m.Title,\r\n                    Message = m.Message,\r\n                    ImageUrl = m.ImageUrl,\r\n                    Data = m.Data,\r\n                    Platform = m.Platform,\r\n                    Metadata = m.Metadata\r\n                }).ToList()\r\n            };\r\n\r\n            var result = await _pushGateway.SendBulkPushAsync(bulkRequest);\r\n\r\n            var response = new SendBulkPushResponse\r\n            {\r\n                Success = result.IsSuccess,\r\n                SuccessCount = result.SuccessCount,\r\n                FailureCount = result.FailureCount,\r\n                Results = result.Results?.Select(r => new SendPushResponse\r\n                {\r\n                    Success = r.IsSuccess,\r\n                    MessageId = r.MessageId,\r\n                    Error = r.ErrorMessage\r\n                }).ToList() ?? new List<SendPushResponse>()\r\n            };\r\n\r\n            await SendOkAsync(response, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error sending bulk push notifications\");\r\n            await SendAsync(new SendBulkPushResponse\r\n            {\r\n                Success = false,\r\n                FailureCount = req.Messages.Count\r\n            }, 500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetPushStatusRequest\r\n{\r\n    public string MessageId { get; set; } = string.Empty;\r\n}\r\n\r\npublic class GetPushStatusEndpoint : Endpoint<GetPushStatusRequest, object>\r\n{\r\n    private readonly IPushGateway _pushGateway;\r\n    private readonly ILogger<GetPushStatusEndpoint> _logger;\r\n\r\n    public GetPushStatusEndpoint(IPushGateway pushGateway, ILogger<GetPushStatusEndpoint> logger)\r\n    {\r\n        _pushGateway = pushGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/push/status\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get push notification status\";\r\n            s.Description = \"Get the status of a push notification\";\r\n            s.Responses[200] = \"Push status retrieved successfully\";\r\n            s.Responses[404] = \"Message not found\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Push\");\r\n    }\r\n\r\n    public override async Task HandleAsync(GetPushStatusRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var status = await _pushGateway.GetMessageStatusAsync(req.MessageId);\r\n            await SendOkAsync(status, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting push status for {MessageId}\", req.MessageId);\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetPushPlatformsEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly IPushGateway _pushGateway;\r\n    private readonly ILogger<GetPushPlatformsEndpoint> _logger;\r\n\r\n    public GetPushPlatformsEndpoint(IPushGateway pushGateway, ILogger<GetPushPlatformsEndpoint> logger)\r\n    {\r\n        _pushGateway = pushGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/push/platforms\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get push platforms\";\r\n            s.Description = \"Get list of available push notification platforms\";\r\n            s.Responses[200] = \"Platforms retrieved successfully\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Push\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var platforms = await _pushGateway.GetPlatformsAsync();\r\n            await SendOkAsync(platforms, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting push platforms\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n"}]}