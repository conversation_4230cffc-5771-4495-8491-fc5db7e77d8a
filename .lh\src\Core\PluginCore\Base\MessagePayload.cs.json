{"sourceFile": "src/Core/PluginCore/Base/MessagePayload.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751199068108, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751199068108, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic class MessagePayload \r\n{\r\n    public string? MessageId { get; set; }\r\n    public string? CorrelationId { get; set; }\r\n    public string? UserId { get; set; }\r\n    public string? TenantId { get; set; }\r\n    public string? Channel { get; set; }\r\n    public string? Provider { get; set; }\r\n    public string? ProviderMessageId { get; set; }\r\n    public string? ProviderResponse { get; set; }\r\n    public string? ProviderErrorCode { get; set; }\r\n    public string? ProviderErrorMessage { get; set; }\r\n    public string? ProviderErrorDetails { get; set; }\r\n    public string? ProviderErrorResolution { get; set; }\r\n    public string? ProviderErrorDocumentation { get; set; }\r\n    public string? ProviderErrorSupport { get; set; }\r\n    public string? ProviderErrorContact { get; set; }\r\n    public string? ProviderErrorPhone { get; set; }\r\n    public string? ProviderErrorEmail { get; set; }\r\n    public string? ProviderErrorUrl { get; set; }\r\n    public string? ProviderErrorOther { get; set; }\r\n    public string? ProviderErrorAdditionalInfo { get; set; }\r\n    public string? ProviderErrorTimestamp { get; set; }\r\n\r\n    public string? TemplateId { get; set; }\r\n    public Dictionary<string, string>? TemplateData { get; set; }\r\n    public Recipient? Recipient { get; set; }\r\n    public string? Subject { get; set; }\r\n    public string? Content { get; set; }\r\n    public string? MessageType { get; set; }\r\n    public string? MessageCategory { get; set; }\r\n    public string? MessagePriority { get; set; }\r\n    public string? MessageExpiration { get; set; }\r\n    public string? MessageDeliveryWindow { get; set; }\r\n    public string? MessageDeliveryAttempts { get; set; }\r\n    public string? MessageDeliveryRetryDelay { get; set; }\r\n    public string? MessageDeliveryRetryAttempts { get; set; }\r\n    public string? MessageDeliveryRetryBackoff { get; set; }\r\n    public string? MessageDeliveryRetryJitter { get; set; }\r\n}\r\n"}]}