using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Base;
using System.Text.Json;
using System.Collections.Concurrent;

namespace Plugin.MultiTenancy;

public class MultiTenancyPlugin : BaseNotificationPlugin
{
    private readonly ILogger<MultiTenancyPlugin> _logger;
    private readonly ConcurrentDictionary<string, TenantConfiguration> _tenantConfigurations = new();
    private readonly ConcurrentDictionary<string, TenantMetrics> _tenantMetrics = new();
    private bool _isInitialized = false;

    public MultiTenancyPlugin(ILogger<MultiTenancyPlugin> logger)
    {
        _logger = logger;
    }

    public override PluginInfo PluginInfo => new(
        Name: "MultiTenancy",
        Version: "1.0.0",
        Description: "Multi-tenancy plugin providing isolated configuration, storage, and service scope per tenant",
        Type: PluginContract.Enums.PluginType.Messaging, // Using Messaging as closest fit
        Author: "NotificationService Team",
        IsEnabled: true
    );

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);

        // Load default tenant configurations
        var defaultTenantId = configuration["MultiTenancy:DefaultTenantId"] ?? "default";
        var enableTenantIsolation = configuration.GetValue<bool>("MultiTenancy:EnableTenantIsolation", true);

        // Initialize default tenant
        await CreateTenantAsync(new TenantConfiguration
        {
            TenantId = defaultTenantId,
            Name = "Default Tenant",
            IsActive = true,
            Settings = new Dictionary<string, object>
            {
                ["EnableTenantIsolation"] = enableTenantIsolation,
                ["MaxApiCallsPerHour"] = 10000,
                ["AllowedFeatures"] = new[] { "Email", "SMS", "Push", "Messaging" }
            }
        });

        _isInitialized = true;
        _logger.LogInformation("MultiTenancy plugin initialized successfully");
    }

    public override async Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default)
    {
        // This plugin doesn't send notifications directly, but manages tenant context
        return new MultiTenancyResponse(false, ErrorMessage: "MultiTenancy plugin is for tenant management only");
    }

    public async Task<bool> CreateTenantAsync(TenantConfiguration tenantConfig)
    {
        try
        {
            if (_tenantConfigurations.ContainsKey(tenantConfig.TenantId))
            {
                _logger.LogWarning("Tenant {TenantId} already exists", tenantConfig.TenantId);
                return false;
            }

            _tenantConfigurations[tenantConfig.TenantId] = tenantConfig;
            _tenantMetrics[tenantConfig.TenantId] = new TenantMetrics
            {
                TenantId = tenantConfig.TenantId,
                CreatedAt = DateTime.UtcNow,
                LastActivity = DateTime.UtcNow
            };

            _logger.LogInformation("Created tenant {TenantId}: {TenantName}", tenantConfig.TenantId, tenantConfig.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant {TenantId}", tenantConfig.TenantId);
            return false;
        }
    }

    public async Task<bool> UpdateTenantAsync(string tenantId, TenantConfiguration tenantConfig)
    {
        try
        {
            if (!_tenantConfigurations.ContainsKey(tenantId))
            {
                _logger.LogWarning("Tenant {TenantId} not found for update", tenantId);
                return false;
            }

            _tenantConfigurations[tenantId] = tenantConfig;
            _logger.LogInformation("Updated tenant {TenantId}", tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant {TenantId}", tenantId);
            return false;
        }
    }

    public async Task<bool> DeleteTenantAsync(string tenantId)
    {
        try
        {
            var removed = _tenantConfigurations.TryRemove(tenantId, out _);
            _tenantMetrics.TryRemove(tenantId, out _);

            if (removed)
            {
                _logger.LogInformation("Deleted tenant {TenantId}", tenantId);
            }
            else
            {
                _logger.LogWarning("Tenant {TenantId} not found for deletion", tenantId);
            }

            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant {TenantId}", tenantId);
            return false;
        }
    }

    public async Task<TenantConfiguration?> GetTenantAsync(string tenantId)
    {
        _tenantConfigurations.TryGetValue(tenantId, out var tenant);
        return tenant;
    }

    public async Task<List<TenantConfiguration>> GetAllTenantsAsync()
    {
        return _tenantConfigurations.Values.ToList();
    }

    public async Task<TenantMetrics?> GetTenantMetricsAsync(string tenantId)
    {
        _tenantMetrics.TryGetValue(tenantId, out var metrics);
        return metrics;
    }

    public async Task<bool> ValidateTenantAccessAsync(string tenantId, string feature)
    {
        if (!_tenantConfigurations.TryGetValue(tenantId, out var tenant))
        {
            return false;
        }

        if (!tenant.IsActive)
        {
            return false;
        }

        // Check if feature is allowed for tenant
        if (tenant.Settings.TryGetValue("AllowedFeatures", out var allowedFeaturesObj) &&
            allowedFeaturesObj is string[] allowedFeatures)
        {
            return allowedFeatures.Contains(feature, StringComparer.OrdinalIgnoreCase);
        }

        return true; // Default to allow if no restrictions
    }

    public async Task TrackTenantActivityAsync(string tenantId, string activity, Dictionary<string, object>? metadata = null)
    {
        if (_tenantMetrics.TryGetValue(tenantId, out var metrics))
        {
            metrics.LastActivity = DateTime.UtcNow;
            metrics.TotalRequests++;
            
            if (metadata != null)
            {
                foreach (var kvp in metadata)
                {
                    metrics.CustomMetrics[kvp.Key] = kvp.Value;
                }
            }
        }
    }

    protected override string GetProviderName() => "MultiTenancy";

    protected override List<ConfigurationField> GetConfigurationFields()
    {
        return new List<ConfigurationField>
        {
            new("DefaultTenantId", "string", "Default tenant identifier", false, false, "default"),
            new("EnableTenantIsolation", "boolean", "Enable tenant isolation", false, false, true),
            new("MaxTenantsAllowed", "number", "Maximum number of tenants allowed", false, false, 100),
            new("TenantStorageType", "string", "Storage type for tenant data", false, false, "InMemory")
        };
    }

    protected override Dictionary<string, object> GetCustomMetrics()
    {
        return new Dictionary<string, object>
        {
            ["total_tenants"] = _tenantConfigurations.Count,
            ["active_tenants"] = _tenantConfigurations.Values.Count(t => t.IsActive),
            ["inactive_tenants"] = _tenantConfigurations.Values.Count(t => !t.IsActive),
            ["last_tenant_activity"] = _tenantMetrics.Values.Max(m => m.LastActivity),
            ["total_tenant_requests"] = _tenantMetrics.Values.Sum(m => m.TotalRequests)
        };
    }
}

// Multi-tenancy specific models
public class TenantConfiguration
{
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
    public Dictionary<string, object> Settings { get; set; } = new();
    public Dictionary<string, string> ConnectionStrings { get; set; } = new();
    public List<string> AllowedFeatures { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TenantMetrics
{
    public string TenantId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public Dictionary<string, long> FeatureUsage { get; set; } = new();
    public Dictionary<string, object> CustomMetrics { get; set; } = new();
}

public record MultiTenancyRequest(
    string TenantId,
    string Action,
    Dictionary<string, object>? Data = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Action, Metadata);

public record MultiTenancyResponse(
    bool IsSuccess,
    string? TenantId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
