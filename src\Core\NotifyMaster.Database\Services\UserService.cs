using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PluginCore.Interfaces;
using PluginCore.Models;

namespace NotifyMaster.Database.Services;

/// <summary>
/// Implementation of user management service using Entity Framework
/// </summary>
public class UserService : IUserService
{
    private readonly NotifyMasterDbContext _context;
    private readonly IAuthenticationService _authService;
    private readonly ILogger<UserService> _logger;

    public UserService(
        NotifyMasterDbContext context, 
        IAuthenticationService authService,
        ILogger<UserService> logger)
    {
        _context = context;
        _authService = authService;
        _logger = logger;
    }

    public async Task<User?> GetUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Include(u => u.Tenant)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .Include(u => u.Permissions).ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", userId);
            return null;
        }
    }

    public async Task<User?> GetUserByEmailAsync(string tenantId, string email, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Include(u => u.Tenant)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .Include(u => u.Permissions).ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.TenantId == tenantId && u.Email == email, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by email {Email} in tenant {TenantId}", email, tenantId);
            return null;
        }
    }

    public async Task<IReadOnlyList<User>> GetTenantUsersAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Where(u => u.TenantId == tenantId)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .OrderBy(u => u.Username)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for tenant {TenantId}", tenantId);
            return Array.Empty<User>();
        }
    }

    public async Task<OperationResult<User>> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user already exists
            var existingUser = await _context.Users
                .FirstOrDefaultAsync(u => u.TenantId == request.TenantId && 
                                         (u.Email == request.Email || u.Username == request.Username), 
                                   cancellationToken);
            
            if (existingUser != null)
            {
                return OperationResult<User>.Failure("A user with this email or username already exists");
            }

            // Check tenant limits
            var tenant = await _context.Tenants.FindAsync(request.TenantId);
            if (tenant == null)
            {
                return OperationResult<User>.Failure("Tenant not found");
            }

            var currentUserCount = await _context.Users.CountAsync(u => u.TenantId == request.TenantId, cancellationToken);
            if (currentUserCount >= tenant.Limits.MaxUsers)
            {
                return OperationResult<User>.Failure("Tenant has reached maximum user limit");
            }

            var user = new User
            {
                Id = Guid.NewGuid().ToString(),
                TenantId = request.TenantId,
                Username = request.Username,
                Email = request.Email,
                FirstName = request.FirstName,
                LastName = request.LastName,
                PasswordHash = _authService.HashPassword(request.Password),
                Status = request.Status,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy,
                Profile = request.Profile
            };

            _context.Users.Add(user);

            // Assign roles if provided
            if (request.RoleIds.Any())
            {
                var roles = await _context.Roles
                    .Where(r => request.RoleIds.Contains(r.Id))
                    .ToListAsync(cancellationToken);

                foreach (var role in roles)
                {
                    _context.UserRoles.Add(new UserRole
                    {
                        UserId = user.Id,
                        RoleId = role.Id,
                        TenantId = request.TenantId,
                        AssignedAt = DateTime.UtcNow,
                        AssignedBy = request.CreatedBy
                    });
                }
            }

            await _context.SaveChangesAsync(cancellationToken);

            // Update tenant usage
            tenant.Usage.CurrentUsers = currentUserCount + 1;
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created user {UserId} in tenant {TenantId}", user.Id, request.TenantId);
            return OperationResult<User>.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {Username} in tenant {TenantId}", request.Username, request.TenantId);
            return OperationResult<User>.Failure($"Failed to create user: {ex.Message}");
        }
    }

    public async Task<OperationResult<User>> UpdateUserAsync(string userId, UpdateUserRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult<User>.Failure("User not found");
            }

            // Update properties if provided
            if (!string.IsNullOrEmpty(request.Username))
            {
                // Check if username is already taken
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.TenantId == user.TenantId && u.Username == request.Username && u.Id != userId, cancellationToken);
                
                if (existingUser != null)
                {
                    return OperationResult<User>.Failure("Username is already taken");
                }
                
                user.Username = request.Username;
            }

            if (!string.IsNullOrEmpty(request.Email))
            {
                // Check if email is already taken
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.TenantId == user.TenantId && u.Email == request.Email && u.Id != userId, cancellationToken);
                
                if (existingUser != null)
                {
                    return OperationResult<User>.Failure("Email is already taken");
                }
                
                user.Email = request.Email;
            }

            if (!string.IsNullOrEmpty(request.FirstName))
                user.FirstName = request.FirstName;

            if (!string.IsNullOrEmpty(request.LastName))
                user.LastName = request.LastName;

            if (request.Status.HasValue)
                user.Status = request.Status.Value;

            if (request.Profile != null)
                user.Profile = request.Profile;

            user.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated user {UserId}", userId);
            return OperationResult<User>.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId}", userId);
            return OperationResult<User>.Failure($"Failed to update user: {ex.Message}");
        }
    }

    public async Task<OperationResult> DeleteUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            _context.Users.Remove(user);
            await _context.SaveChangesAsync(cancellationToken);

            // Update tenant usage
            var tenant = await _context.Tenants.FindAsync(user.TenantId);
            if (tenant != null)
            {
                tenant.Usage.CurrentUsers = Math.Max(0, tenant.Usage.CurrentUsers - 1);
                await _context.SaveChangesAsync(cancellationToken);
            }

            _logger.LogInformation("Deleted user {UserId}", userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", userId);
            return OperationResult.Failure($"Failed to delete user: {ex.Message}");
        }
    }

    public async Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default)
    {
        return await _authService.AuthenticateAsync(tenantId, email, password, cancellationToken);
    }

    public async Task<OperationResult> ChangePasswordAsync(string userId, string currentPassword, string newPassword, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            if (!_authService.VerifyPassword(currentPassword, user.PasswordHash))
            {
                return OperationResult.Failure("Current password is incorrect");
            }

            user.PasswordHash = _authService.HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Changed password for user {UserId}", userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user {UserId}", userId);
            return OperationResult.Failure($"Failed to change password: {ex.Message}");
        }
    }

    public async Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? assignedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            var role = await _context.Roles.FindAsync(roleId);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            var existingUserRole = await _context.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId, cancellationToken);

            if (existingUserRole != null)
            {
                return OperationResult.Failure("User already has this role");
            }

            _context.UserRoles.Add(new UserRole
            {
                UserId = userId,
                RoleId = roleId,
                TenantId = user.TenantId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = assignedBy
            });

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Assigned role {RoleId} to user {UserId}", roleId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role {RoleId} to user {UserId}", roleId, userId);
            return OperationResult.Failure($"Failed to assign role: {ex.Message}");
        }
    }

    public async Task<OperationResult> RemoveRoleAsync(string userId, string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var userRole = await _context.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId, cancellationToken);

            if (userRole == null)
            {
                return OperationResult.Failure("User does not have this role");
            }

            _context.UserRoles.Remove(userRole);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Removed role {RoleId} from user {UserId}", roleId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role {RoleId} from user {UserId}", roleId, userId);
            return OperationResult.Failure($"Failed to remove role: {ex.Message}");
        }
    }

    public async Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? grantedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return OperationResult.Failure("Permission not found");
            }

            var existingUserPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId, cancellationToken);

            if (existingUserPermission != null)
            {
                return OperationResult.Failure("User already has this permission");
            }

            _context.UserPermissions.Add(new UserPermission
            {
                UserId = userId,
                PermissionId = permissionId,
                TenantId = user.TenantId,
                GrantedAt = DateTime.UtcNow,
                GrantedBy = grantedBy
            });

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Granted permission {PermissionId} to user {UserId}", permissionId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error granting permission {PermissionId} to user {UserId}", permissionId, userId);
            return OperationResult.Failure($"Failed to grant permission: {ex.Message}");
        }
    }

    public async Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var userPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId, cancellationToken);

            if (userPermission == null)
            {
                return OperationResult.Failure("User does not have this permission");
            }

            _context.UserPermissions.Remove(userPermission);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Revoked permission {PermissionId} from user {UserId}", permissionId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission {PermissionId} from user {UserId}", permissionId, userId);
            return OperationResult.Failure($"Failed to revoke permission: {ex.Message}");
        }
    }

    public async Task<bool> HasPermissionAsync(string userId, string tenantId, string resource, string action, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check direct user permissions
            var hasDirectPermission = await _context.UserPermissions
                .Include(up => up.Permission)
                .AnyAsync(up => up.UserId == userId && 
                               up.Permission.Resource == resource && 
                               up.Permission.Action == action, cancellationToken);

            if (hasDirectPermission) return true;

            // Check role-based permissions
            var hasRolePermission = await _context.UserRoles
                .Include(ur => ur.Role)
                .ThenInclude(r => r.Permissions)
                .ThenInclude(rp => rp.Permission)
                .Where(ur => ur.UserId == userId)
                .SelectMany(ur => ur.Role.Permissions)
                .AnyAsync(rp => rp.Permission.Resource == resource && 
                               rp.Permission.Action == action, cancellationToken);

            return hasRolePermission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Resource}:{Action} for user {UserId}", resource, action, userId);
            return false;
        }
    }

    public async Task<bool> HasRoleAsync(string userId, string tenantId, string roleName, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.UserRoles
                .Include(ur => ur.Role)
                .AnyAsync(ur => ur.UserId == userId && ur.Role.Name == roleName, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role {RoleName} for user {UserId}", roleName, userId);
            return false;
        }
    }

    public async Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = new List<Permission>();

            // Get direct permissions
            var directPermissions = await _context.UserPermissions
                .Include(up => up.Permission)
                .Where(up => up.UserId == userId)
                .Select(up => up.Permission)
                .ToListAsync(cancellationToken);

            permissions.AddRange(directPermissions);

            // Get role-based permissions
            var rolePermissions = await _context.UserRoles
                .Include(ur => ur.Role)
                .ThenInclude(r => r.Permissions)
                .ThenInclude(rp => rp.Permission)
                .Where(ur => ur.UserId == userId)
                .SelectMany(ur => ur.Role.Permissions)
                .Select(rp => rp.Permission)
                .ToListAsync(cancellationToken);

            permissions.AddRange(rolePermissions);

            // Remove duplicates
            return permissions.DistinctBy(p => p.Id).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for user {UserId}", userId);
            return Array.Empty<Permission>();
        }
    }

    public async Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.UserRoles
                .Include(ur => ur.Role)
                .Where(ur => ur.UserId == userId)
                .Select(ur => ur.Role)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for user {UserId}", userId);
            return Array.Empty<Role>();
        }
    }
}
