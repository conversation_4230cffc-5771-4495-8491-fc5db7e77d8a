{"name": "Mailgun", "version": "1.0.0", "description": "Mailgun email service plugin for sending transactional and marketing emails", "author": "NotificationService Team", "type": "Email", "provider": "Mailgun", "assemblyName": "Plugin.Email.Mailgun.dll", "entryPoint": "Plugin.Email.Mailgun.MailgunPlugin", "isEnabled": true, "priority": 50, "supportedFeatures": ["SendEmail", "HtmlEmail", "PlainTextEmail", "Attachments", "CustomHeaders", "EmailTracking", "HealthCheck", "AdminConfig", "Metrics", "DomainStats"], "configuration": {"apiKey": {"type": "string", "description": "Mailgun API Key", "isRequired": true, "isSecret": true}, "domain": {"type": "string", "description": "Mailgun Domain", "isRequired": true, "isSecret": false}, "baseUrl": {"type": "string", "description": "Mailgun API Base URL", "isRequired": false, "isSecret": false, "defaultValue": "https://api.mailgun.net/v3"}, "defaultFromEmail": {"type": "string", "description": "Default sender email address", "isRequired": false, "isSecret": false}, "defaultFromName": {"type": "string", "description": "Default sender name", "isRequired": false, "isSecret": false}}, "endpoints": {"send": "https://api.mailgun.net/v3/{domain}/messages", "stats": "https://api.mailgun.net/v3/{domain}/stats/total", "domain": "https://api.mailgun.net/v3/{domain}"}, "rateLimit": {"requestsPerSecond": 10, "requestsPerMinute": 600, "requestsPerHour": 36000}, "adminFeatures": {"configurationManagement": true, "metricsViewing": true, "healthMonitoring": true, "domainManagement": true, "statisticsViewing": true}}