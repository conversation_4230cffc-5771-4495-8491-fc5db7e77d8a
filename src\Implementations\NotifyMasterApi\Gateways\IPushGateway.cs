using PushNotificationContract.Models;
using NotificationContract.Models;

namespace NotifyMasterApi.Gateways;

public interface IPushGateway
{
    Task<PushResponse> SendAsync(PushMessageRequest request);
    Task<BulkPushResponse> SendBulkAsync(BulkPushRequest request);
    Task<PushResponse> ResendMessageAsync(string messageId);
    Task<object> GetPlatformsAsync();
    Task<ServiceResult> ConfigurePlatformAsync(string platform, object configuration);
    Task<object> TestPlatformAsync(string platform, string? testToken = null);
    Task<ServiceResult> UpdatePlatformStatusAsync(string platform, bool enabled);
    Task<object> CleanupDeviceTokensAsync(string? platform = null, bool dryRun = true);
    Task<object> GetIosCertificateStatusAsync();
    Task<ServiceResult> UpdateIosCertificateAsync(Microsoft.AspNetCore.Http.IFormFile certificate);
    Task<object> GetDeliveryRateMetricsAsync(string? platform = null);
    Task<object> GetDeviceTokenMetricsAsync(string? platform = null);

    // Additional methods expected by controllers
    Task<PushResponse> SendPushAsync(PushMessageRequest request);
    Task<BulkPushResponse> SendBulkPushAsync(BulkPushRequest request);
    Task<object> GetMessageStatusAsync(string messageId);
    Task<object> GetMessageHistoryAsync(string? userId = null, int page = 1, int pageSize = 50);
    Task<object> GetAvailableProviders();
    Task<ServiceResult> SwitchProvider(string provider);
    Task<object> SendTestMessageAsync(string provider, string? testToken = null);
    Task<ServiceResult> ReloadProviders();
    Task<ServiceResult> UpdateProviderConfiguration(string provider, object configuration);
    Task<object> GetSummaryMetricsAsync();
    Task<object> GetDetailedMetricsAsync(DateTime? from = null, DateTime? to = null);
    Task<object> GetErrorMetricsAsync();
    Task<object> GetMonthlyStatisticsAsync(int year, int month);
}
