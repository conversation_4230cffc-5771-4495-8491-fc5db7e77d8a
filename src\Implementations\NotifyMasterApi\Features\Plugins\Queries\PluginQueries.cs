using MediatR;
using NotifyMasterApi.Models;

namespace NotifyMasterApi.Features.Plugins.Queries;

/// <summary>
/// Query to get all plugins
/// </summary>
public record GetPluginsQuery(string? Type = null, bool? IsLoaded = null) : IRequest<PluginListResponse>;

/// <summary>
/// Query to get a specific plugin
/// </summary>
public record GetPluginQuery(string Name) : IRequest<Plugin?>;

/// <summary>
/// Query to get plugin health status
/// </summary>
public record GetPluginHealthQuery() : IRequest<PluginHealthResponse>;

/// <summary>
/// Query to get specific plugin health
/// </summary>
public record GetPluginHealthByNameQuery(string Name) : IRequest<PluginHealth?>;
