{"name": "MultiTenancy", "version": "1.0.0", "description": "Multi-tenancy plugin providing isolated configuration, storage, and service scope per tenant", "author": "NotificationService Team", "type": "Messaging", "provider": "MultiTenancy", "assemblyName": "Plugin.MultiTenancy.dll", "entryPoint": "Plugin.MultiTenancy.MultiTenancyPlugin", "isEnabled": true, "priority": 100, "supportedFeatures": ["TenantManagement", "TenantIsolation", "TenantMetrics", "TenantConfiguration", "AccessControl", "FeatureToggling", "AdminConfig", "Metrics"], "configuration": {"defaultTenantId": {"type": "string", "description": "Default tenant identifier", "isRequired": false, "isSecret": false, "defaultValue": "default"}, "enableTenantIsolation": {"type": "boolean", "description": "Enable tenant isolation", "isRequired": false, "isSecret": false, "defaultValue": true}, "maxTenantsAllowed": {"type": "number", "description": "Maximum number of tenants allowed", "isRequired": false, "isSecret": false, "defaultValue": 100}, "tenantStorageType": {"type": "string", "description": "Storage type for tenant data (InMemory, Database, Redis)", "isRequired": false, "isSecret": false, "defaultValue": "InMemory"}}, "endpoints": {"tenants": "/api/tenants", "tenant_config": "/api/tenants/{tenantId}/config", "tenant_metrics": "/api/tenants/{tenantId}/metrics"}, "rateLimit": {"requestsPerSecond": 100, "requestsPerMinute": 6000, "requestsPerHour": 360000}, "adminFeatures": {"configurationManagement": true, "metricsViewing": true, "healthMonitoring": true, "tenantManagement": true, "accessControl": true, "featureToggling": true}, "tenantFeatures": {"isolatedConfiguration": true, "isolatedStorage": true, "isolatedMetrics": true, "featureAccess": true, "rateLimiting": true, "customBranding": true}}