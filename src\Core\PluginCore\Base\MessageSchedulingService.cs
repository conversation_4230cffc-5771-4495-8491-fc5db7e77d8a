using PluginCore.Models;
using Microsoft.Extensions.Logging;

namespace PluginCore.Base;

/// <summary>
/// Service interface for scheduling messages when gateways don't have native scheduling support.
/// </summary>
public interface IMessageSchedulingService
{
    /// <summary>
    /// Schedules a message for future delivery.
    /// </summary>
    Task<MessageScheduleResult> ScheduleMessageAsync(
        string gatewayId,
        MessagePayload payload,
        DateTimeOffset scheduledTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancels a scheduled message.
    /// </summary>
    Task<OperationResult> CancelScheduledMessageAsync(
        string scheduledMessageId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all scheduled messages for a gateway.
    /// </summary>
    Task<IReadOnlyList<ScheduledMessage>> GetScheduledMessagesAsync(
        string gatewayId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets scheduled messages that are ready to be sent.
    /// </summary>
    Task<IReadOnlyList<ScheduledMessage>> GetPendingMessagesAsync(
        DateTimeOffset cutoffTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Marks a scheduled message as sent.
    /// </summary>
    Task<OperationResult> MarkMessageAsSentAsync(
        string scheduledMessageId,
        MessageSendResult sendResult,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Marks a scheduled message as failed.
    /// </summary>
    Task<OperationResult> MarkMessageAsFailedAsync(
        string scheduledMessageId,
        string errorMessage,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the scheduled time for a message.
    /// </summary>
    Task<OperationResult> RescheduleMessageAsync(
        string scheduledMessageId,
        DateTimeOffset newScheduledTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the status of a scheduled message.
    /// </summary>
    Task<ScheduledMessageStatus?> GetScheduledMessageStatusAsync(
        string scheduledMessageId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents a scheduled message.
/// </summary>
public record ScheduledMessage(
    string Id,
    string GatewayId,
    MessagePayload Payload,
    DateTimeOffset ScheduledTime,
    DateTimeOffset CreatedAt,
    ScheduledMessageStatus Status,
    string? ErrorMessage = null,
    MessageSendResult? SendResult = null,
    int RetryCount = 0);

/// <summary>
/// Status of a scheduled message.
/// </summary>
public enum ScheduledMessageStatus
{
    Pending,
    Sent,
    Failed,
    Cancelled,
    Expired
}

/// <summary>
/// Default implementation of the message scheduling service.
/// Uses in-memory storage by default, but can be extended for persistent storage.
/// </summary>
public class DefaultMessageSchedulingService : IMessageSchedulingService
{
    private readonly Dictionary<string, ScheduledMessage> _scheduledMessages = new();
    private readonly object _lock = new();
    private readonly ILogger? _logger;

    public DefaultMessageSchedulingService(ILogger? logger = null)
    {
        _logger = logger;
    }

    public Task<MessageScheduleResult> ScheduleMessageAsync(
        string gatewayId,
        MessagePayload payload,
        DateTimeOffset scheduledTime,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var scheduledMessageId = $"sched_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid():N}";
            
            var scheduledMessage = new ScheduledMessage(
                Id: scheduledMessageId,
                GatewayId: gatewayId,
                Payload: payload,
                ScheduledTime: scheduledTime,
                CreatedAt: DateTimeOffset.UtcNow,
                Status: ScheduledMessageStatus.Pending
            );

            _scheduledMessages[scheduledMessageId] = scheduledMessage;
            
            _logger?.LogInformation("Message scheduled with ID {ScheduledMessageId} for {ScheduledTime}", 
                scheduledMessageId, scheduledTime);

            return Task.FromResult(new MessageScheduleResult(
                ScheduledMessageId: scheduledMessageId,
                ScheduledTime: scheduledTime,
                Status: "Scheduled"
            ));
        }
    }

    public Task<OperationResult> CancelScheduledMessageAsync(
        string scheduledMessageId,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (!_scheduledMessages.TryGetValue(scheduledMessageId, out var message))
            {
                return Task.FromResult(new OperationResult(false, "Scheduled message not found"));
            }

            if (message.Status != ScheduledMessageStatus.Pending)
            {
                return Task.FromResult(new OperationResult(false, $"Cannot cancel message with status {message.Status}"));
            }

            _scheduledMessages[scheduledMessageId] = message with { Status = ScheduledMessageStatus.Cancelled };
            
            _logger?.LogInformation("Scheduled message {ScheduledMessageId} cancelled", scheduledMessageId);
            
            return Task.FromResult(new OperationResult(true, "Message cancelled successfully"));
        }
    }

    public Task<IReadOnlyList<ScheduledMessage>> GetScheduledMessagesAsync(
        string gatewayId,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var messages = _scheduledMessages.Values
                .Where(m => m.GatewayId == gatewayId)
                .OrderBy(m => m.ScheduledTime)
                .ToList()
                .AsReadOnly();

            return Task.FromResult(messages);
        }
    }

    public Task<IReadOnlyList<ScheduledMessage>> GetPendingMessagesAsync(
        DateTimeOffset cutoffTime,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var pendingMessages = _scheduledMessages.Values
                .Where(m => m.Status == ScheduledMessageStatus.Pending && m.ScheduledTime <= cutoffTime)
                .OrderBy(m => m.ScheduledTime)
                .ToList()
                .AsReadOnly();

            return Task.FromResult(pendingMessages);
        }
    }

    public Task<OperationResult> MarkMessageAsSentAsync(
        string scheduledMessageId,
        MessageSendResult sendResult,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (!_scheduledMessages.TryGetValue(scheduledMessageId, out var message))
            {
                return Task.FromResult(new OperationResult(false, "Scheduled message not found"));
            }

            _scheduledMessages[scheduledMessageId] = message with 
            { 
                Status = ScheduledMessageStatus.Sent,
                SendResult = sendResult
            };

            _logger?.LogInformation("Scheduled message {ScheduledMessageId} marked as sent", scheduledMessageId);
            
            return Task.FromResult(new OperationResult(true, "Message marked as sent"));
        }
    }

    public Task<OperationResult> MarkMessageAsFailedAsync(
        string scheduledMessageId,
        string errorMessage,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (!_scheduledMessages.TryGetValue(scheduledMessageId, out var message))
            {
                return Task.FromResult(new OperationResult(false, "Scheduled message not found"));
            }

            _scheduledMessages[scheduledMessageId] = message with 
            { 
                Status = ScheduledMessageStatus.Failed,
                ErrorMessage = errorMessage,
                RetryCount = message.RetryCount + 1
            };

            _logger?.LogWarning("Scheduled message {ScheduledMessageId} marked as failed: {ErrorMessage}", 
                scheduledMessageId, errorMessage);
            
            return Task.FromResult(new OperationResult(true, "Message marked as failed"));
        }
    }

    public Task<OperationResult> RescheduleMessageAsync(
        string scheduledMessageId,
        DateTimeOffset newScheduledTime,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (!_scheduledMessages.TryGetValue(scheduledMessageId, out var message))
            {
                return Task.FromResult(new OperationResult(false, "Scheduled message not found"));
            }

            if (message.Status != ScheduledMessageStatus.Pending)
            {
                return Task.FromResult(new OperationResult(false, $"Cannot reschedule message with status {message.Status}"));
            }

            _scheduledMessages[scheduledMessageId] = message with { ScheduledTime = newScheduledTime };
            
            _logger?.LogInformation("Scheduled message {ScheduledMessageId} rescheduled to {NewScheduledTime}", 
                scheduledMessageId, newScheduledTime);
            
            return Task.FromResult(new OperationResult(true, "Message rescheduled successfully"));
        }
    }

    public Task<ScheduledMessageStatus?> GetScheduledMessageStatusAsync(
        string scheduledMessageId,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (_scheduledMessages.TryGetValue(scheduledMessageId, out var message))
            {
                return Task.FromResult<ScheduledMessageStatus?>(message.Status);
            }

            return Task.FromResult<ScheduledMessageStatus?>(null);
        }
    }
}
