{"sourceFile": "src/Implementations/NotifyMasterApi/Ioc/Configurator.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1751212146286, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751212163380, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -115,9 +115,15 @@\n             .UseInMemoryStorage());\r\n \r\n         services.AddHangfireServer();\r\n \r\n-        // Add library services\r\n+        // Add new PluginCore system (plugins are optional)\r\n+        services.AddCompleteGatewayPluginSystem(\r\n+            pluginDirectory: \"plugins\",\r\n+            autoLoad: false  // Don't auto-load, make plugins optional\r\n+        );\r\n+\r\n+        // Add library services (fallback when no plugins)\r\n         services.AddEmailService(configuration);\r\n         services.AddSmsService();\r\n         services.AddPushNotificationService();\r\n \r\n@@ -125,10 +131,8 @@\n         services.AddScoped<IEmailGateway, EmailGateway>();\r\n         services.AddScoped<ISmsGateway, SmsGateway>();\r\n         services.AddScoped<IPushGateway, PushGateway>();\r\n         services.AddScoped<INotificationLoggingService, NotificationLoggingService>();\r\n-        services.AddScoped<IPluginManager, RuntimePluginManager>();\r\n-        services.AddScoped<IPluginDetectionService, PluginDetectionService>();\r\n \r\n         // Add advanced services\r\n         services.AddScoped<IWebhookQueueService, WebhookQueueService>();\r\n         services.AddScoped<IEventStreamService, EventStreamService>();\r\n"}, {"date": 1751212723659, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -29,8 +29,11 @@\n     {\r\n         // Add FastEndpoints\r\n         services.AddFastEndpoints();\r\n \r\n+        // Add MediatR\r\n+        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Program).Assembly));\r\n+\r\n         // Add authorization services\r\n         services.AddAuthorization();\r\n \r\n         // Add OpenAPI with Scalar\r\n"}, {"date": 1751214850386, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -144,8 +144,11 @@\n         services.AddScoped<ITemplateRenderingService, TemplateRenderingService>();\r\n         services.AddScoped<ISchedulingService, SchedulingService>();\r\n         services.AddScoped<ISetupService, SetupService>();\r\n \r\n+        // Add startup service\r\n+        services.AddStartupService();\r\n+\r\n         // Add HttpClient for plugins and webhooks\r\n         services.AddHttpClient();\r\n \r\n         // Add health checks\r\n"}, {"date": 1751214925948, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,8 +19,9 @@\n using EmailService.Library.Extensions;\r\n using SmsService.Library.Extensions;\r\n using PushNotificationService.Library.Extensions;\r\n using PluginCore.Extensions;\r\n+using NotifyMasterApi.Services;\r\n \r\n namespace NotifyMasterApi.Ioc;\r\n \r\n public static class Configurator\r\n"}], "date": 1751212146286, "name": "Commit-0", "content": "using System.Reflection;\r\nusing System.Text.Json.Serialization;\r\nusing Microsoft.AspNetCore.Diagnostics.HealthChecks;\r\nusing Microsoft.Extensions.Diagnostics.HealthChecks;\r\nusing Microsoft.EntityFrameworkCore;\r\nusing Microsoft.OpenApi.Models;\r\nusing NotifyMasterApi.Data;\r\nusing NotifyMasterApi.Services;\r\nusing NotifyMasterApi.Gateways;\r\nusing NotifyMasterApi.Interfaces;\r\nusing NotifyMasterApi.Features.Setup;\r\nusing Serilog;\r\nusing Serilog.Events;\r\nusing Hangfire;\r\nusing Hangfire.InMemory;\r\nusing FastEndpoints;\r\nusing Scalar.AspNetCore;\r\nusing Hangfire.Dashboard;\r\nusing EmailService.Library.Extensions;\r\nusing SmsService.Library.Extensions;\r\nusing PushNotificationService.Library.Extensions;\r\nusing PluginCore.Extensions;\r\n\r\nnamespace NotifyMasterApi.Ioc;\r\n\r\npublic static class Configurator\r\n{\r\n    public static void InjectService(this IServiceCollection services , IConfiguration configuration)\r\n    {\r\n        // Add FastEndpoints\r\n        services.AddFastEndpoints();\r\n\r\n        // Add authorization services\r\n        services.AddAuthorization();\r\n\r\n        // Add OpenAPI with Scalar\r\n        services.AddOpenApi(options =>\r\n        {\r\n            options.AddDocumentTransformer((document, context, cancellationToken) =>\r\n            {\r\n                document.Info = new()\r\n                {\r\n                    Title = \"NotificationService API\",\r\n                    Version = \"v2.0.0\",\r\n                    Description = \"\"\"\r\n                        # 🔔 NotificationService API\r\n\r\n                        A comprehensive notification service supporting **Email**, **SMS**, and **Push notifications** with a powerful plugin architecture.\r\n\r\n                        ## 🚀 Features\r\n\r\n                        - **Multi-Channel Support**: Email, SMS, Push Notifications, and App Messaging\r\n                        - **Plugin Architecture**: Extensible with custom providers\r\n                        - **Real-time Processing**: Background job processing with Hangfire\r\n                        - **Health Monitoring**: Built-in health checks and metrics\r\n                        - **Multi-Tenancy**: Support for multiple tenants and whitelabeling\r\n                        - **Template Engine**: Dynamic message templating\r\n                        - **Webhook Support**: Real-time event notifications\r\n\r\n                        ## 🔧 Quick Start\r\n\r\n                        1. **Send an Email**: `POST /api/email/send`\r\n                        2. **Send SMS**: `POST /api/sms/send`\r\n                        3. **Send Push Notification**: `POST /api/push/send`\r\n                        4. **Check Health**: `GET /health/ready`\r\n                        5. **View Plugins**: `GET /api/plugins`\r\n\r\n                        ## 📚 Documentation\r\n\r\n                        - **API Reference**: This interactive documentation\r\n                        - **Hangfire Dashboard**: [/hangfire](/hangfire) - Background job monitoring\r\n                        - **Health Checks**: [/health/ready](/health/ready) - System health status\r\n\r\n                        ## 🔐 Authentication\r\n\r\n                        Most endpoints require authentication. Use the **Authorize** button above to configure your API key.\r\n\r\n                        ## 💡 Support\r\n\r\n                        For support and questions, visit our [GitHub repository](https://github.com/notificationservice).\r\n                        \"\"\",\r\n                    Contact = new()\r\n                    {\r\n                        Name = \"NotificationService Team\",\r\n                        Url = new Uri(\"https://github.com/notificationservice\"),\r\n                        Email = \"<EMAIL>\"\r\n                    },\r\n                    License = new()\r\n                    {\r\n                        Name = \"MIT License\",\r\n                        Url = new Uri(\"https://opensource.org/licenses/MIT\")\r\n                    }\r\n                };\r\n\r\n                document.Servers = new List<Microsoft.OpenApi.Models.OpenApiServer>\r\n                {\r\n                    new() { Url = \"http://localhost:5120\", Description = \"🔧 Development Server\" },\r\n                    new() { Url = \"https://api.notificationservice.com\", Description = \"🚀 Production Server\" }\r\n                };\r\n\r\n                return Task.CompletedTask;\r\n            });\r\n        });\r\n        services.AddEndpointsApiExplorer();\r\n\r\n        // Add Entity Framework with In-Memory database (simplified)\r\n        services.AddDbContext<NotificationDbContext>(options =>\r\n            options.UseInMemoryDatabase(\"NotificationService\"));\r\n\r\n        // Add Hangfire for background jobs\r\n        services.AddHangfire(config => config\r\n            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)\r\n            .UseSimpleAssemblyNameTypeSerializer()\r\n            .UseRecommendedSerializerSettings()\r\n            .UseInMemoryStorage());\r\n\r\n        services.AddHangfireServer();\r\n\r\n        // Add library services\r\n        services.AddEmailService(configuration);\r\n        services.AddSmsService();\r\n        services.AddPushNotificationService();\r\n\r\n        // Add simplified services\r\n        services.AddScoped<IEmailGateway, EmailGateway>();\r\n        services.AddScoped<ISmsGateway, SmsGateway>();\r\n        services.AddScoped<IPushGateway, PushGateway>();\r\n        services.AddScoped<INotificationLoggingService, NotificationLoggingService>();\r\n        services.AddScoped<IPluginManager, RuntimePluginManager>();\r\n        services.AddScoped<IPluginDetectionService, PluginDetectionService>();\r\n\r\n        // Add advanced services\r\n        services.AddScoped<IWebhookQueueService, WebhookQueueService>();\r\n        services.AddScoped<IEventStreamService, EventStreamService>();\r\n        services.AddScoped<IDeadLetterQueueService, DeadLetterQueueService>();\r\n        services.AddScoped<IValidationService, ValidationService>();\r\n        services.AddScoped<ITemplateRenderingService, TemplateRenderingService>();\r\n        services.AddScoped<ISchedulingService, SchedulingService>();\r\n        services.AddScoped<ISetupService, SetupService>();\r\n\r\n        // Add HttpClient for plugins and webhooks\r\n        services.AddHttpClient();\r\n\r\n        // Add health checks\r\n        services.AddHealthChecks()\r\n            .AddCheck(\"self\", () => HealthCheckResult.Healthy(), tags: new[] { \"ready\" });\r\n\r\n        AddLogging(configuration);\r\n    }\r\n    \r\n    public static void ConfigurePipeline(this WebApplication app)\r\n    {\r\n        // Configure Hangfire Dashboard\r\n        app.UseHangfireDashboard(\"/hangfire\", new DashboardOptions\r\n        {\r\n            Authorization = new[] { new HangfireAuthorizationFilter() }\r\n        });\r\n\r\n        // Configure FastEndpoints\r\n        app.UseFastEndpoints();\r\n\r\n        // Configure static files for assets\r\n        app.UseStaticFiles();\r\n\r\n        // Configure default files\r\n        app.UseDefaultFiles(new DefaultFilesOptions\r\n        {\r\n            DefaultFileNames = { \"index.html\" }\r\n        });\r\n\r\n        // Configure OpenAPI and Scalar UI with custom branding\r\n        app.MapOpenApi();\r\n        app.MapScalarApiReference(options =>\r\n        {\r\n            options\r\n                .WithTitle(\"🔔 NotificationService API\")\r\n                .WithTheme(ScalarTheme.Purple)\r\n                .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient)\r\n                .WithFavicon(\"/favicon.ico\")\r\n                .WithCustomCss(File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), \"wwwroot\", \"scalar-custom.css\")));\r\n        });\r\n\r\n        // Add API endpoint for project configuration\r\n        app.MapGet(\"/api/config\", () => new\r\n        {\r\n            Name = \"NotificationService\",\r\n            Version = \"2.0.0\",\r\n            Description = \"Modern plugin-based notification service with comprehensive admin and metrics\",\r\n            Architecture = \"FastEndpoints + Plugin System\",\r\n            Features = new[] {\r\n                \"Email\", \"SMS\", \"Push\", \"WhatsApp\", \"Slack\",\r\n                \"Plugin Management\", \"Multi-Tenancy\", \"Health Monitoring\",\r\n                \"Metrics & Analytics\", \"Feature Detection\"\r\n            },\r\n            Endpoints = new\r\n            {\r\n                Health = \"/health/ready\",\r\n                Email = \"/api/email/send\",\r\n                SMS = \"/api/sms/send\",\r\n                Push = \"/api/push/send\",\r\n                Plugins = \"/api/plugins\",\r\n                Tenants = \"/api/tenants\",\r\n                Features = \"/api/system/features\",\r\n                Admin = \"/api/admin\",\r\n                Documentation = \"/scalar/v1\"\r\n            },\r\n            Plugins = new\r\n            {\r\n                Email = new[] { \"SendGrid\", \"Mailgun\" },\r\n                SMS = new[] { \"Twilio\", \"BulkSMS\", \"Clickatel\" },\r\n                Push = new[] { \"Firebase FCM\" },\r\n                Messaging = new[] { \"WhatsApp Business\", \"Slack\" },\r\n                System = new[] { \"MultiTenancy\" }\r\n            }\r\n        }).WithTags(\"Configuration\");\r\n\r\n        // Health checks\r\n        app.MapHealthChecks(\"/health/ready\", new HealthCheckOptions()\r\n        {\r\n            Predicate = (check) => check.Tags.Contains(\"ready\"),\r\n        });\r\n        app.MapHealthChecks(\"/health/live\", new HealthCheckOptions());\r\n\r\n        app.UseHttpsRedirection();\r\n        app.UseAuthorization();\r\n    }\r\n    \r\n    private static void AddLogging(IConfiguration configuration)\r\n    {\r\n        Log.Logger = new LoggerConfiguration()\r\n            .MinimumLevel.Override(\"Microsoft\", LogEventLevel.Information)\r\n            .Enrich.FromLogContext()\r\n            .WriteTo.Console()\r\n            .WriteTo.Debug()\r\n            .ReadFrom.Configuration(configuration)\r\n            .CreateLogger();\r\n    }\r\n}\r\n\r\npublic class HangfireAuthorizationFilter : IDashboardAuthorizationFilter\r\n{\r\n    public bool Authorize(DashboardContext context)\r\n    {\r\n        // In production, implement proper authorization\r\n        // For now, allow all access in development\r\n        return true;\r\n    }\r\n}"}]}