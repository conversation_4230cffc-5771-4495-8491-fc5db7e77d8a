{"sourceFile": "src/Core/PluginCore/Extensions/ServiceCollectionExtensions.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751193465221, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751193572385, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,11 +15,17 @@\n     }\r\n     public static IServiceCollection AddEmailService(this IServiceCollection services, IConfiguration configuration)\r\n     {\r\n         services.Configure<EmailServiceSettings>(configuration.GetSection(\"EmailService\"));\r\n-        \r\n+\r\n         services.AddScoped<IEmailService, EmailSenderService>();\r\n         services.AddScoped<ISmtpClient, SmtpClientService>();\r\n+\r\n+        return services;\r\n+    }\r\n+    public static IServiceCollection AddPushNotificationService(this IServiceCollection services)\r\n+    {\r\n+        services.AddScoped<IPushNotificationService, PushNotificationServiceImplementation>();\r\n         \r\n         return services;\r\n     }\r\n }\r\n"}, {"date": 1751193653234, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,8 +24,14 @@\n     }\r\n     public static IServiceCollection AddPushNotificationService(this IServiceCollection services)\r\n     {\r\n         services.AddScoped<IPushNotificationService, PushNotificationServiceImplementation>();\r\n+\r\n+        return services;\r\n+    }\r\n+    public static IServiceCollection AddSmsService(this IServiceCollection services)\r\n+    {\r\n+        services.AddScoped<ISmsService, SmsServiceImplementation>();\r\n         \r\n         return services;\r\n     }\r\n }\r\n"}], "date": 1751193465221, "name": "Commit-0", "content": "using Microsoft.Extensions.DependencyInjection;\r\nusing PluginContract.Interfaces;\r\nusing PluginCore.Services;\r\n\r\nnamespace PluginCore.Extensions;\r\n\r\npublic static class ServiceCollectionExtensions\r\n{\r\n    public static IServiceCollection AddPluginSystem(this IServiceCollection services)\r\n    {\r\n        services.AddSingleton<IPluginManager, PluginManager>();\r\n        services.AddScoped<NotificationService>();\r\n\r\n        return services;\r\n    }\r\n    public static IServiceCollection AddEmailService(this IServiceCollection services, IConfiguration configuration)\r\n    {\r\n        services.Configure<EmailServiceSettings>(configuration.GetSection(\"EmailService\"));\r\n        \r\n        services.AddScoped<IEmailService, EmailSenderService>();\r\n        services.AddScoped<ISmtpClient, SmtpClientService>();\r\n        \r\n        return services;\r\n    }\r\n}\r\n"}]}