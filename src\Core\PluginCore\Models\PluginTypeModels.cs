// Location: PluginCore/Models/GatewayModels.cs

using System.ComponentModel.DataAnnotations;

namespace PluginCore.Models;

// --- Core Primitives ---

/// <summary>
/// A universal response for operations that may succeed or fail, providing detailed error information.
/// </summary>
/// <param name="Errors">Additional error details, if applicable.</param>
/// <param name="Message">A human-readable message describing the result.</param>
/// <param name="IsSuccess">Indicates whether the operation was successful.</param
public record OperationResult(bool IsSuccess, string? Message = null, IEnumerable<ErrorDetail>? Errors = null);

/// <summary>
/// A structured error detail used in OperationResult.
/// </summary>
/// <param name="Code">A unique code identifying the error.</param>
/// <param name="Description">A human-readable description of the error.</param>
/// <param name="Target">The target of the error (e.g., a field name).</param>
public record ErrorDetail(string Code, string Description, string? Target = null);

// --- Message-Related Models ---

/// <summary>
/// Defines the complete payload for sending a single message, used across multiple methods.
/// </summary>
/// <param name="Recipient">The intended recipient of the message.</param>
/// <param name="Content">The content of the message.</param>
/// <param name="From">The sender of the message (optional).</param>
/// <param name="Subject">The subject of the message (for email, SMS, etc.).</param>
/// <param name="Headers">Additional headers or metadata for the message.</param>

public record MessagePayload(string Recipient,string Content,string? From = null,string? Subject = null, Dictionary<string, string>? Headers = null, string? CorrelationId = null);

/// <summary>
/// The result of a successful message send operation.
/// </summary>
/// <param name="MessageId">The unique identifier for the sent message.</param>
/// <param name="Timestamp">The timestamp of the send operation.</param>
/// <param name="Status">The status of the message (e.g., sent, queued).</param>
/// <param name="CorrelationId">An optional correlation ID for tracking.</param>
public record MessageSendResult(string MessageId, DateTimeOffset Timestamp, string Status, string? CorrelationId = null);

/// <summary>
/// The result of a message scheduling operation.
/// </summary>
/// <param name="ScheduledMessageId">The unique identifier for the scheduled message.</param>
/// <param name="ScheduledTime">The scheduled time of the message.</param>
/// <param name="Status">The status of the scheduling operation.</param>
public record MessageScheduleResult(string ScheduledMessageId, DateTimeOffset ScheduledTime, string Status);

/// <summary>
/// Contains the real-time status of a message.
/// </summary>
/// <param name="MessageId">The unique identifier for the message.</param>
/// <param name="Status">The current status of the message.</param>
/// <param name="LastUpdated">The timestamp of the last status update.</param>
/// <param name="Reason">An optional reason for the current status.</param>
public record MessageStatusInfo(string MessageId, string Status, DateTimeOffset LastUpdated, string? Reason = null);

/// <summary>
/// Contains a provider-issued delivery receipt.
/// </summary>
/// <param name="MessageId">The unique identifier for the message.</param>
/// <param name="Status">The status of the message as reported by the provider.</param>
/// <param name="Timestamp">The timestamp of the delivery receipt.</param>
/// <param name="ProviderCode">An optional code provided by the provider for the status.</param>
public record DeliveryReceipt(string MessageId, string Status, DateTimeOffset Timestamp, string? ProviderCode = null);

/// <summary>
/// The response from a raw payload send.
/// </summary>
/// <param name="IsSuccess">Indicates whether the operation was successful.</param>
/// <param name="StatusCode">The HTTP status code of the response.</param>
/// <param name="Body">The body of the response.</param>
/// <param name="Errors">Additional error details, if applicable.</param>
public record RawGatewayResponse(bool IsSuccess, int StatusCode, string Body, IEnumerable<ErrorDetail>? Errors = null);

/// <summary>
/// A message prepared for sending but not yet dispatched, used for previews or dry runs.
/// </summary>
/// <param name="RenderedPayload">The final, rendered payload to be sent.</param>
/// <param name="FinalHeaders">The final headers to be included with the message.</param>
/// <param name="Validation">The result of any validation performed on the message.</param>
public record PreparedMessage(object RenderedPayload, Dictionary<string, string> FinalHeaders, ValidationResult Validation);


// --- Metrics-Related Models ---

/// <summary>
/// A comprehensive report on the gateway's current operational status.
/// </summary>
/// <param name="IsHealthy">Indicates whether the gateway is healthy.</param>
/// <param name="CurrentLatencyMs">The current latency in milliseconds.</param>
/// <param name="Message">A human-readable message describing the status.</param>
/// <param name="ConnectivityStatus">The status of each connectivity check.</param>
public record GatewayStatusReport(bool IsHealthy, double CurrentLatencyMs, string Message, Dictionary<string, string> ConnectivityStatus);

/// <summary>
/// The result of a message delivery report query.
/// </summary>
/// <param name="MessageId">The unique identifier for the message.</param>
/// <param name="Status">The current status of the message.</param>
/// <param name="Timestamp">The timestamp of the last status update.</param>
/// <param name="FailureReason">The reason for failure, if applicable.</param>
public record DeliveryResult(string MessageId, string Status, DateTimeOffset Timestamp, string? FailureReason = null);

/// <summary>
/// A summary of usage metrics over a specified period.
/// </summary>
/// <param name="MessagesSent">The total number of messages sent.</param>
/// <param name="MessagesFailed">The total number of messages that failed.</param>
/// <param name="MessagesQueued">The total number of messages queued.</param>
/// <param name="EstimatedCost">The estimated cost of operations.</param>
public record UsageMetrics(int MessagesSent, int MessagesFailed, int MessagesQueued, decimal EstimatedCost);

/// <summary>
/// A single entry in the gateway's error log.
/// </summary>
/// <param name="ErrorId">The unique identifier for the error.</param>
/// <param name="ErrorCode">The code associated with the error.</param>
/// <param name="Description">A human-readable description of the error.</param>
/// <param name="Severity">The severity of the error (e.g., low, medium, high).</param>
/// <param name="Timestamp">The timestamp of the error entry.</param>
public record GatewayErrorEntry(string ErrorId, string ErrorCode, string Description, string Severity, DateTimeOffset Timestamp);

/// <summary>
/// A snapshot of system throughput.
/// </summary>
/// <param name="MessagesPerMinute">The number of messages processed per minute.</param>
/// <param name="AverageDeliveryTimeMs">The average delivery time in milliseconds.</param>
/// <param name="Resolution">The time resolution of the snapshot (e.g., 1 minute, 5 minutes).</param>
public record PerformanceSnapshot(int MessagesPerMinute, double AverageDeliveryTimeMs, TimeSpan Resolution);

/// <summary>
/// A report on Service Level Agreement (SLA) compliance.
/// </summary>
/// <param name="AvailabilityPercentage">The percentage of time the service was available.</param>
/// <param name="AverageDeliveryTimeMs">The average delivery time in milliseconds.</param>
/// <param name="Breaches">The number of SLA breaches.</param>
public record SlaReport(double AvailabilityPercentage, double AverageDeliveryTimeMs, int Breaches);

/// <summary>
/// A breakdown of latencies for different gateway operations.
/// </summary>
/// <param name="AuthenticationMs">The average latency for authentication operations.</param>
/// <param name="SendApiCallMs">The average latency for sending API calls.</param>
/// <param name="DeliveryCallbackMs">The average latency for delivery callbacks.</param>
public record LatencyMetrics(double AuthenticationMs, double SendApiCallMs, double DeliveryCallbackMs);

/// <summary>
/// Data for analyzing traffic trends.
/// </summary>
/// <param name="Period">The time period for the trend.</param>
/// <param name="Granularity">The granularity of the trend (e.g., hourly, daily).</param>
/// <param name="TotalMessages">The total number of messages during the period.</param>
/// <param name="Delivered">The number of messages delivered.</param>
/// <param name="Failed">The number of messages failed.</param>

public record TrafficTrend(DateTime Period, string Granularity, int TotalMessages, int Delivered, int Failed, double AvgLatencyMs);

/// <summary>
/// The result of anomaly detection analysis.
/// </summary>
/// <param name="AnomalyId">The unique identifier for the anomaly.</param>
/// <param name="DetectedAt">The timestamp when the anomaly was detected.</param>
/// <param name="Description">A human-readable description of the anomaly.</param>
/// <param name="Severity">The severity of the anomaly (e.g., low, medium, high).</param>
public record AnomalyDetectionResult(string AnomalyId, DateTime DetectedAt, string Description, string Severity);

/// <summary>
/// Represents a generated, downloadable report.
/// </summary>
/// <param name="FileName">The name of the report file.</param>
/// <param name="MimeType">The MIME type of the report.</param>
/// <param name="Content">The content of the report.</param>
public record GeneratedReport(string FileName, string MimeType, byte[] Content);

/// <summary>
/// Information about a single retry attempt.
/// </summary>
/// <param name="MessageId">The unique identifier for the message.</param>
/// <param name="RetryCount">The number of the retry attempt.</param>
/// <param name="ErrorCode">The error code that triggered the retry.</param>
/// <param name="Outcome">The outcome of the retry attempt.</param>
/// <param name="AttemptedAt">The timestamp of the retry attempt.</param>
public record RetryAttemptInfo(string MessageId, int RetryCount, string ErrorCode, string Outcome, DateTime AttemptedAt);

/// <summary>
/// A record of a configuration change and its observed impact.
/// </summary>
/// <param name="ChangeId">The unique identifier for the configuration change.</param>
/// <param name="ChangedAt">The timestamp of the configuration change.</param>
/// <param name="Description">A human-readable description of the change.</param>
/// <param name="ImpactAnalysis">The analysis of the impact of the change.</param>
public record ConfigurationImpactRecord(string ChangeId, DateTime ChangedAt, string Description, string ImpactAnalysis);

/// <summary>
/// A Recipient record where all fields are optional to support different gateway types.
/// </summary>
/// <param name="Email">The email address of the recipient.</param>
/// <param name="PhoneNumber">The phone number of the recipient.</param>
/// <param name="DeviceToken">The device token of the recipient for push notifications.</param>
/// <param name="UserId">The user ID of the recipient for web app notifications.</param>
/// <param name="Address">The generic address of the recipient (fallback for any gateway type).</param>
/// <param name="ReceptorName">The name of the recipient.</param>
/// <param name="PersonalizationData">The personalization data of the recipient.</param>
public record Recipient(
    string? Email=null,
    string? PhoneNumber=null,
    string? DeviceToken=null,
    string? UserId=null,
    string? Address=null,
    string? ReceptorName=null,
    Dictionary<string, string>? PersonalizationData=null
);

/// <summary>
/// Options for generating a metrics report.
/// </summary>
/// <param name="Format">The format of the report (e.g., csv, pdf).</param>
/// <param name="Language">The language of the report (e.g., en-US, fr-FR).</param>
public record MetricsReportOptions(string Format = "csv", string Language = "en-US");

public record GatewayConfiguration(
    string Key,
    string DisplayName,
    string? Value,
    string? Description,
    bool IsSensitive,
    bool IsRequired,
    string ValueType = "string",
    string? DefaultValue = null,
    List<string>? AllowedValues = null,
    Dictionary<string, string>? Metadata = null
);
public record PluginManifest(string Name, string Version, string Description, string Author, string Type, string Provider, string AssemblyName, string EntryPoint, List<PluginDependency> Dependencies, Dictionary<string, PluginConfigurationItem> Configuration, List<string> SupportedFeatures, string MinimumFrameworkVersion = "net9.0", bool IsEnabled = true, int Priority = 100, Dictionary<string, object> Metadata);

public record PluginDependency(string Name, string Version, bool IsRequired = true);

public record PluginConfigurationItem(string Type, string Description, List<string>? ValidationRules = null, object? DefaultValue = null, bool IsRequired = true, bool IsSecret = false);

