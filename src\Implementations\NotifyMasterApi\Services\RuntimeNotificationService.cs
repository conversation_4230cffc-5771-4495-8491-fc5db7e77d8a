using NotifyMasterApi.Interfaces;
using SmsContract.Models;
using EmailContract.Models;
using PushNotificationContract.Models;

namespace NotifyMasterApi.Services;

/// <summary>
/// Service that routes notifications to runtime-loaded plugins
/// </summary>
public class RuntimeNotificationService
{
    private readonly ILogger<RuntimeNotificationService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public RuntimeNotificationService(
        ILogger<RuntimeNotificationService> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// Send SMS using runtime-loaded SMS plugins
    /// </summary>
    public async Task<object?> SendSmsAsync(SendSmsRequest request, string? preferredProvider = null)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                var smsPlugins = await runtimeManager.GetPluginsByTypeAsync("SMS");
                
                // Try preferred provider first
                if (!string.IsNullOrEmpty(preferredProvider))
                {
                    var preferredPlugin = smsPlugins.FirstOrDefault(p => 
                        p.Provider.Equals(preferredProvider, StringComparison.OrdinalIgnoreCase));
                    
                    if (preferredPlugin != null)
                    {
                        _logger.LogInformation("Sending SMS via preferred provider: {Provider}", preferredProvider);
                        return await preferredPlugin.SendNotificationAsync(request);
                    }
                }

                // Use first available SMS plugin
                var plugin = smsPlugins.FirstOrDefault();
                if (plugin != null)
                {
                    _logger.LogInformation("Sending SMS via plugin: {PluginName}", plugin.Name);
                    return await plugin.SendNotificationAsync(request);
                }
                else
                {
                    _logger.LogWarning("No SMS plugins available");
                    return CreateErrorResponse("No SMS plugins available");
                }
            }

            return CreateErrorResponse("Plugin manager not available");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS");
            return CreateErrorResponse(ex.Message);
        }
    }

    /// <summary>
    /// Send Email using runtime-loaded Email plugins
    /// </summary>
    public async Task<object?> SendEmailAsync(SendEmailRequest request, string? preferredProvider = null)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                var emailPlugins = await runtimeManager.GetPluginsByTypeAsync("Email");
                
                // Try preferred provider first
                if (!string.IsNullOrEmpty(preferredProvider))
                {
                    var preferredPlugin = emailPlugins.FirstOrDefault(p => 
                        p.Provider.Equals(preferredProvider, StringComparison.OrdinalIgnoreCase));
                    
                    if (preferredPlugin != null)
                    {
                        _logger.LogInformation("Sending Email via preferred provider: {Provider}", preferredProvider);
                        return await preferredPlugin.SendNotificationAsync(request);
                    }
                }

                // Use first available Email plugin
                var plugin = emailPlugins.FirstOrDefault();
                if (plugin != null)
                {
                    _logger.LogInformation("Sending Email via plugin: {PluginName}", plugin.Name);
                    return await plugin.SendNotificationAsync(request);
                }
                else
                {
                    _logger.LogWarning("No Email plugins available");
                    return CreateErrorResponse("No Email plugins available");
                }
            }

            return CreateErrorResponse("Plugin manager not available");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending Email");
            return CreateErrorResponse(ex.Message);
        }
    }

    /// <summary>
    /// Send Push notification using runtime-loaded Push plugins
    /// </summary>
    public async Task<object?> SendPushAsync(SendPushMessageRequest request, string? preferredProvider = null)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                var pushPlugins = await runtimeManager.GetPluginsByTypeAsync("Push");
                
                // Try preferred provider first
                if (!string.IsNullOrEmpty(preferredProvider))
                {
                    var preferredPlugin = pushPlugins.FirstOrDefault(p => 
                        p.Provider.Equals(preferredProvider, StringComparison.OrdinalIgnoreCase));
                    
                    if (preferredPlugin != null)
                    {
                        _logger.LogInformation("Sending Push notification via preferred provider: {Provider}", preferredProvider);
                        return await preferredPlugin.SendNotificationAsync(request);
                    }
                }

                // Use first available Push plugin
                var plugin = pushPlugins.FirstOrDefault();
                if (plugin != null)
                {
                    _logger.LogInformation("Sending Push notification via plugin: {PluginName}", plugin.Name);
                    return await plugin.SendNotificationAsync(request);
                }
                else
                {
                    _logger.LogWarning("No Push plugins available");
                    return CreateErrorResponse("No Push plugins available");
                }
            }

            return CreateErrorResponse("Plugin manager not available");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending Push notification");
            return CreateErrorResponse(ex.Message);
        }
    }

    /// <summary>
    /// Get available providers for a specific notification type
    /// </summary>
    public async Task<List<string>> GetAvailableProvidersAsync(string notificationType)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                var plugins = await runtimeManager.GetPluginsByTypeAsync(notificationType);
                return plugins.Select(p => p.Provider).ToList();
            }

            return new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available providers for {NotificationType}", notificationType);
            return new List<string>();
        }
    }

    /// <summary>
    /// Get plugin health status
    /// </summary>
    public async Task<Dictionary<string, bool>> GetPluginHealthStatusAsync()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                return await runtimeManager.GetPluginsHealthStatusAsync();
            }

            return new Dictionary<string, bool>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting plugin health status");
            return new Dictionary<string, bool>();
        }
    }

    /// <summary>
    /// Invoke a specific method on a plugin
    /// </summary>
    public async Task<object?> InvokePluginMethodAsync(string pluginName, string methodName, params object[] parameters)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                return await runtimeManager.InvokePluginMethodAsync(pluginName, methodName, parameters);
            }

            return CreateErrorResponse("Plugin manager not available");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking plugin method {MethodName} on {PluginName}", methodName, pluginName);
            return CreateErrorResponse(ex.Message);
        }
    }

    private static object CreateErrorResponse(string errorMessage)
    {
        return new
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.UtcNow
        };
    }
}
