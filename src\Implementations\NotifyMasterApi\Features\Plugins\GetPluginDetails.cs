using FastEndpoints;
using NotifyMasterApi.Interfaces;
using PluginContract.Models;

namespace NotifyMasterApi.Features.Plugins;

public class GetPluginDetailsRequest
{
    public string PluginName { get; set; } = string.Empty;
}

public class GetPluginDetailsResponse
{
    public PluginDetailInfo? Plugin { get; set; }
    public PluginAdminInfo? AdminInfo { get; set; }
    public PluginMetrics? Metrics { get; set; }
    public object? HealthStatus { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class PluginDetailInfo
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public DateTime? LoadedAt { get; set; }
    public List<string> SupportedFeatures { get; set; } = new();
}

public class GetPluginDetailsEndpoint : Endpoint<GetPluginDetailsRequest, GetPluginDetailsResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginDetailsEndpoint> _logger;

    public GetPluginDetailsEndpoint(IPluginManager pluginManager, ILogger<GetPluginDetailsEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/plugins/{pluginName}");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get detailed plugin information";
            s.Description = "Retrieve detailed information about a specific plugin including admin info and metrics";
            s.Responses[200] = "Plugin details retrieved successfully";
            s.Responses[404] = "Plugin not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Plugin Management");
    }

    public override async Task HandleAsync(GetPluginDetailsRequest req, CancellationToken ct)
    {
        try
        {
            var plugin = await _pluginManager.GetPluginAsync(req.PluginName);
            if (plugin == null)
            {
                await SendNotFoundAsync(ct);
                return;
            }

            // Get additional plugin information
            var adminInfo = await GetPluginAdminInfoSafely(req.PluginName);
            var metrics = await GetPluginMetricsSafely(req.PluginName);
            var healthStatus = await GetPluginHealthSafely(req.PluginName);

            var pluginDetail = new PluginDetailInfo
            {
                Name = plugin.Name ?? "Unknown",
                Version = plugin.Version ?? "Unknown",
                Description = plugin.Description ?? "No description available",
                Type = plugin.Type.ToString(),
                Provider = plugin.Provider ?? "Unknown",
                Author = plugin.Author ?? "Unknown",
                IsEnabled = plugin.IsEnabled,
                LoadedAt = plugin.LoadedAt,
                SupportedFeatures = new List<string>() // This would come from manifest
            };

            await SendOkAsync(new GetPluginDetailsResponse
            {
                Plugin = pluginDetail,
                AdminInfo = adminInfo,
                Metrics = metrics,
                HealthStatus = healthStatus
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving plugin details for {PluginName}", req.PluginName);
            await SendErrorsAsync(500, ct);
        }
    }

    private async Task<PluginAdminInfo?> GetPluginAdminInfoSafely(string pluginName)
    {
        try
        {
            var config = await _pluginManager.GetPluginConfigurationAsync(pluginName);
            // Convert to PluginAdminInfo - this is a simplified version
            return new PluginAdminInfo(
                pluginName,
                "1.0.0", // Would get from actual plugin
                "Unknown", // Would get from actual plugin
                true, // Would get from actual plugin
                new Dictionary<string, object>(),
                new List<ConfigurationField>(),
                DateTime.UtcNow,
                "Active"
            );
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not get admin info for plugin {PluginName}", pluginName);
            return null;
        }
    }

    private async Task<PluginMetrics?> GetPluginMetricsSafely(string pluginName)
    {
        try
        {
            var metrics = await _pluginManager.GetPluginMetricsAsync(pluginName);
            // This would need to be converted from the object returned by the plugin manager
            return new PluginMetrics(
                pluginName,
                DateTime.UtcNow.AddHours(-1),
                DateTime.UtcNow,
                0, 0, 0, 0, 0, 0,
                new Dictionary<string, long>(),
                new Dictionary<string, object>()
            );
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not get metrics for plugin {PluginName}", pluginName);
            return null;
        }
    }

    private async Task<object?> GetPluginHealthSafely(string pluginName)
    {
        try
        {
            return await _pluginManager.GetPluginHealthStatusAsync(pluginName);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not get health status for plugin {PluginName}", pluginName);
            return new { status = "unknown", error = ex.Message };
        }
    }
}
