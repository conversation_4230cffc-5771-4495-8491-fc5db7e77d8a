using FastEndpoints;
using MediatR;
using NotifyMasterApi.Features.Plugins.Queries;
using NotifyMasterApi.Models;

namespace NotifyMasterApi.Features.Plugins;

public class GetPluginDetailsRequest
{
    public string PluginName { get; set; } = string.Empty;
}

public class GetPluginDetailsEndpoint : Endpoint<GetPluginDetailsRequest, Plugin?>
{
    private readonly IMediator _mediator;

    public GetPluginDetailsEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Get("/api/plugins/{pluginName}");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get Plugin Details";
            s.Description = "Get detailed information about a specific plugin";
            s.Response<Plugin>(200, "Plugin details retrieved successfully");
            s.Response(404, "Plugin not found");
        });
    }

    public override async Task HandleAsync(GetPluginDetailsRequest req, CancellationToken ct)
    {
        var query = new GetPluginQuery(req.PluginName);
        var result = await _mediator.Send(query, ct);

        if (result == null)
        {
            await SendNotFoundAsync(ct);
            return;
        }

        await SendOkAsync(result, ct);
    }

}
