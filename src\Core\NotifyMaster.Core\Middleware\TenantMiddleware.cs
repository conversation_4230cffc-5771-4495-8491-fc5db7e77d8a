// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Middleware;

/// <summary>
/// Middleware that resolves and sets tenant context for each request
/// </summary>
public class TenantMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantMiddleware> _logger;

    public TenantMiddleware(RequestDelegate next, ILogger<TenantMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Skip tenant resolution for health checks and static files
            if (ShouldSkipTenantResolution(context))
            {
                await _next(context);
                return;
            }

            // Resolve tenant
            var tenantResolutionService = context.RequestServices.GetRequiredService<ITenantResolutionService>();
            var tenant = await tenantResolutionService.ResolveTenantAsync(context);

            if (tenant != null)
            {
                // Set tenant in context
                context.Items["Tenant"] = tenant;
                context.Items["TenantId"] = tenant.Id;
                context.Items["TenantDomain"] = tenant.Domain;

                _logger.LogDebug("Resolved tenant {TenantId} ({TenantDomain}) for request {RequestPath}", 
                    tenant.Id, tenant.Domain, context.Request.Path);

                // Check if tenant is active
                if (tenant.Status != TenantStatus.Active)
                {
                    _logger.LogWarning("Request to inactive tenant {TenantId} ({Status})", tenant.Id, tenant.Status);
                    
                    if (tenant.Status == TenantStatus.Suspended)
                    {
                        context.Response.StatusCode = 403;
                        await context.Response.WriteAsync("Tenant is suspended");
                        return;
                    }
                }
            }
            else
            {
                _logger.LogWarning("Could not resolve tenant for request {RequestPath}", context.Request.Path);
                
                // For API endpoints, return 400 if no tenant found
                if (context.Request.Path.StartsWithSegments("/api"))
                {
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsync("Tenant not found");
                    return;
                }
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in tenant middleware");
            
            // Continue with request even if tenant resolution fails
            await _next(context);
        }
    }

    private static bool ShouldSkipTenantResolution(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        
        if (string.IsNullOrEmpty(path))
            return false;

        // Skip for health checks
        if (path.StartsWith("/health") || path.StartsWith("/ping"))
            return true;

        // Skip for static files
        if (path.StartsWith("/css") || path.StartsWith("/js") || path.StartsWith("/images") || 
            path.StartsWith("/favicon") || path.StartsWith("/robots.txt"))
            return true;

        // Skip for setup endpoints
        if (path.StartsWith("/api/setup"))
            return true;

        // Skip for Hangfire dashboard
        if (path.StartsWith("/hangfire"))
            return true;

        // Skip for Swagger/OpenAPI
        if (path.StartsWith("/swagger") || path.StartsWith("/openapi"))
            return true;

        return false;
    }
}

/// <summary>
/// Extension methods for tenant middleware
/// </summary>
public static class TenantMiddlewareExtensions
{
    /// <summary>
    /// Adds tenant middleware to the pipeline
    /// </summary>
    public static IApplicationBuilder UseTenantMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<TenantMiddleware>();
    }
}

/// <summary>
/// Helper class to access tenant context
/// </summary>
public static class TenantContext
{
    /// <summary>
    /// Gets the current tenant from HTTP context
    /// </summary>
    public static Tenant? GetCurrentTenant(HttpContext context)
    {
        return context.Items["Tenant"] as Tenant;
    }

    /// <summary>
    /// Gets the current tenant ID from HTTP context
    /// </summary>
    public static string? GetCurrentTenantId(HttpContext context)
    {
        return context.Items["TenantId"] as string;
    }

    /// <summary>
    /// Gets the current tenant domain from HTTP context
    /// </summary>
    public static string? GetCurrentTenantDomain(HttpContext context)
    {
        return context.Items["TenantDomain"] as string;
    }

    /// <summary>
    /// Sets the current tenant in HTTP context
    /// </summary>
    public static void SetCurrentTenant(HttpContext context, Tenant tenant)
    {
        context.Items["Tenant"] = tenant;
        context.Items["TenantId"] = tenant.Id;
        context.Items["TenantDomain"] = tenant.Domain;
    }
}
