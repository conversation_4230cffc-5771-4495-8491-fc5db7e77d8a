/* NotificationService API - Custom Scalar UI Styling */

:root {
    /* Brand Colors */
    --ns-primary: #1976d2;
    --ns-primary-dark: #1565c0;
    --ns-primary-darker: #0d47a1;
    --ns-accent: #dc004e;
    --ns-accent-dark: #c51162;
    
    /* Neutral Colors */
    --ns-gray-50: #f8fafc;
    --ns-gray-100: #f1f5f9;
    --ns-gray-200: #e2e8f0;
    --ns-gray-300: #cbd5e1;
    --ns-gray-400: #94a3b8;
    --ns-gray-500: #64748b;
    --ns-gray-600: #475569;
    --ns-gray-700: #334155;
    --ns-gray-800: #1e293b;
    --ns-gray-900: #0f172a;
    
    /* Success/Error Colors */
    --ns-success: #10b981;
    --ns-warning: #f59e0b;
    --ns-error: #ef4444;
    --ns-info: #3b82f6;
    
    /* Typography */
    --ns-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --ns-font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    
    /* Spacing */
    --ns-space-xs: 4px;
    --ns-space-sm: 8px;
    --ns-space-md: 16px;
    --ns-space-lg: 24px;
    --ns-space-xl: 32px;
    
    /* Border Radius */
    --ns-radius-sm: 6px;
    --ns-radius-md: 8px;
    --ns-radius-lg: 12px;
    --ns-radius-xl: 16px;
    
    /* Shadows */
    --ns-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --ns-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --ns-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --ns-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
}

/* Override Scalar CSS Variables */
:root {
    --scalar-color-1: var(--ns-primary);
    --scalar-color-2: var(--ns-primary-dark);
    --scalar-color-3: var(--ns-primary-darker);
    --scalar-color-accent: var(--ns-accent);
    --scalar-radius: var(--ns-radius-md);
    --scalar-radius-lg: var(--ns-radius-lg);
    --scalar-font: var(--ns-font-family);
    --scalar-font-code: var(--ns-font-mono);
}

/* Main App Container */
.scalar-app {
    --scalar-sidebar-width: 320px;
    font-family: var(--ns-font-family);
    background: var(--ns-gray-50);
}

/* Header Styling */
.scalar-header {
    background: linear-gradient(135deg, var(--ns-primary) 0%, var(--ns-primary-dark) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: var(--ns-shadow-md);
}

/* Logo and Branding */
.scalar-logo {
    display: flex;
    align-items: center;
    gap: var(--ns-space-md);
    font-weight: 600;
    font-size: 18px;
    color: white;
    text-decoration: none;
}

.scalar-logo::before {
    content: '';
    width: 32px;
    height: 32px;
    background: url('/logo.svg') no-repeat center;
    background-size: contain;
    border-radius: var(--ns-radius-sm);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Sidebar Styling */
.scalar-sidebar {
    background: linear-gradient(180deg, var(--ns-gray-50) 0%, var(--ns-gray-100) 100%);
    border-right: 1px solid var(--ns-gray-200);
    box-shadow: var(--ns-shadow-sm);
}

/* Content Area */
.scalar-content {
    background: #ffffff;
    min-height: 100vh;
}

/* HTTP Method Badges */
.scalar-method-get {
    background: linear-gradient(135deg, var(--ns-success) 0%, #059669 100%);
    color: white;
    font-weight: 500;
    border-radius: var(--ns-radius-sm);
    box-shadow: var(--ns-shadow-sm);
}

.scalar-method-post {
    background: linear-gradient(135deg, var(--ns-info) 0%, var(--ns-primary) 100%);
    color: white;
    font-weight: 500;
    border-radius: var(--ns-radius-sm);
    box-shadow: var(--ns-shadow-sm);
}

.scalar-method-put {
    background: linear-gradient(135deg, var(--ns-warning) 0%, #d97706 100%);
    color: white;
    font-weight: 500;
    border-radius: var(--ns-radius-sm);
    box-shadow: var(--ns-shadow-sm);
}

.scalar-method-delete {
    background: linear-gradient(135deg, var(--ns-error) 0%, #dc2626 100%);
    color: white;
    font-weight: 500;
    border-radius: var(--ns-radius-sm);
    box-shadow: var(--ns-shadow-sm);
}

.scalar-method-patch {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    font-weight: 500;
    border-radius: var(--ns-radius-sm);
    box-shadow: var(--ns-shadow-sm);
}

/* Endpoint Cards */
.scalar-endpoint-card {
    border: 1px solid var(--ns-gray-200);
    border-radius: var(--ns-radius-lg);
    box-shadow: var(--ns-shadow-sm);
    transition: all 0.2s ease;
    background: white;
    margin-bottom: var(--ns-space-md);
}

.scalar-endpoint-card:hover {
    box-shadow: var(--ns-shadow-lg);
    transform: translateY(-2px);
    border-color: var(--ns-primary);
}

/* Code Blocks */
.scalar-code-block {
    border-radius: var(--ns-radius-md);
    border: 1px solid var(--ns-gray-200);
    background: var(--ns-gray-50);
    font-family: var(--ns-font-mono);
    box-shadow: var(--ns-shadow-sm);
}

/* Buttons */
.scalar-button-primary {
    background: linear-gradient(135deg, var(--ns-primary) 0%, var(--ns-primary-dark) 100%);
    border: none;
    border-radius: var(--ns-radius-md);
    font-weight: 500;
    font-family: var(--ns-font-family);
    transition: all 0.2s ease;
    box-shadow: var(--ns-shadow-sm);
}

.scalar-button-primary:hover {
    background: linear-gradient(135deg, var(--ns-primary-dark) 0%, var(--ns-primary-darker) 100%);
    transform: translateY(-1px);
    box-shadow: var(--ns-shadow-md);
}

.scalar-button-secondary {
    background: white;
    border: 1px solid var(--ns-gray-300);
    border-radius: var(--ns-radius-md);
    color: var(--ns-gray-700);
    font-weight: 500;
    transition: all 0.2s ease;
}

.scalar-button-secondary:hover {
    border-color: var(--ns-primary);
    color: var(--ns-primary);
    box-shadow: var(--ns-shadow-sm);
}

/* Tags */
.scalar-tag {
    background: linear-gradient(135deg, var(--ns-accent) 0%, var(--ns-accent-dark) 100%);
    color: white;
    border-radius: var(--ns-radius-sm);
    font-weight: 500;
    font-size: 12px;
    padding: var(--ns-space-xs) var(--ns-space-sm);
    box-shadow: var(--ns-shadow-sm);
}

/* Search Input */
.scalar-search-input {
    border-radius: var(--ns-radius-md);
    border: 1px solid var(--ns-gray-300);
    transition: all 0.2s ease;
    font-family: var(--ns-font-family);
    background: white;
    box-shadow: var(--ns-shadow-sm);
}

.scalar-search-input:focus {
    border-color: var(--ns-primary);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    outline: none;
}

/* Navigation */
.scalar-nav-item {
    border-radius: var(--ns-radius-sm);
    transition: all 0.2s ease;
    margin-bottom: var(--ns-space-xs);
}

.scalar-nav-item:hover {
    background: var(--ns-gray-100);
    transform: translateX(2px);
}

.scalar-nav-item.active {
    background: linear-gradient(135deg, var(--ns-primary) 0%, var(--ns-primary-dark) 100%);
    color: white;
    box-shadow: var(--ns-shadow-sm);
}

/* Response Status Codes */
.scalar-status-200 { color: var(--ns-success); font-weight: 600; }
.scalar-status-201 { color: var(--ns-success); font-weight: 600; }
.scalar-status-400 { color: var(--ns-warning); font-weight: 600; }
.scalar-status-401 { color: var(--ns-error); font-weight: 600; }
.scalar-status-403 { color: var(--ns-error); font-weight: 600; }
.scalar-status-404 { color: var(--ns-error); font-weight: 600; }
.scalar-status-500 { color: var(--ns-error); font-weight: 600; }

/* Custom Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.scalar-endpoint-card {
    animation: fadeInUp 0.3s ease-out;
}

.scalar-nav-item {
    animation: slideInLeft 0.2s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .scalar-app {
        --scalar-sidebar-width: 280px;
    }
    
    .scalar-logo {
        font-size: 16px;
        gap: var(--ns-space-sm);
    }
    
    .scalar-logo::before {
        width: 28px;
        height: 28px;
    }
}

@media (max-width: 480px) {
    .scalar-app {
        --scalar-sidebar-width: 100%;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --ns-gray-50: #0f172a;
        --ns-gray-100: #1e293b;
        --ns-gray-200: #334155;
        --ns-gray-300: #475569;
        --ns-gray-400: #64748b;
        --ns-gray-500: #94a3b8;
        --ns-gray-600: #cbd5e1;
        --ns-gray-700: #e2e8f0;
        --ns-gray-800: #f1f5f9;
        --ns-gray-900: #f8fafc;
    }
    
    .scalar-content {
        background: var(--ns-gray-100);
        color: var(--ns-gray-800);
    }
    
    .scalar-endpoint-card {
        background: var(--ns-gray-200);
        border-color: var(--ns-gray-300);
    }
}

/* Print Styles */
@media print {
    .scalar-sidebar {
        display: none;
    }
    
    .scalar-content {
        margin-left: 0;
        box-shadow: none;
    }
    
    .scalar-endpoint-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--ns-gray-300);
    }
}
