using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using NotifyMasterApi.Data;

namespace NotifyMasterApi.Services;

/// <summary>
/// In-memory database service for fallback when main database is not available
/// </summary>
public class InMemoryDatabaseService
{
    private readonly ILogger<InMemoryDatabaseService> _logger;
    private readonly ConcurrentDictionary<string, object> _inMemoryData = new();
    private readonly ConcurrentQueue<string> _logs = new();
    private bool _isUsingInMemory = false;

    public InMemoryDatabaseService(ILogger<InMemoryDatabaseService> logger)
    {
        _logger = logger;
    }

    public bool IsUsingInMemoryFallback => _isUsingInMemory;

    public void EnableInMemoryFallback()
    {
        _isUsingInMemory = true;
        _logger.LogWarning("🔄 Database fallback activated - Using in-memory storage");
        _logger.LogInformation("📝 All data will be stored in memory and lost on restart");
        
        // Log the fallback activation
        LogOperation("SYSTEM", "Database fallback activated", "In-memory storage enabled");
    }

    public void LogOperation(string operation, string details, string result)
    {
        var logEntry = $"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}] {operation}: {details} -> {result}";
        _logs.Enqueue(logEntry);
        
        // Keep only last 1000 log entries to prevent memory issues
        while (_logs.Count > 1000)
        {
            _logs.TryDequeue(out _);
        }
        
        _logger.LogInformation("📝 In-Memory Log: {LogEntry}", logEntry);
    }

    public void StoreNotificationLog(string notificationId, string type, string recipient, string status, string? errorMessage = null)
    {
        var logData = new
        {
            Id = notificationId,
            Type = type,
            Recipient = recipient,
            Status = status,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.UtcNow
        };

        _inMemoryData[$"notification_{notificationId}"] = logData;
        LogOperation("NOTIFICATION_LOG", $"Stored {type} notification to {recipient}", status);
    }

    public void StorePluginLog(string pluginId, string action, string status, string? details = null)
    {
        var logData = new
        {
            PluginId = pluginId,
            Action = action,
            Status = status,
            Details = details,
            Timestamp = DateTime.UtcNow
        };

        _inMemoryData[$"plugin_{pluginId}_{DateTime.UtcNow.Ticks}"] = logData;
        LogOperation("PLUGIN_LOG", $"Plugin {pluginId} {action}", status);
    }

    public IEnumerable<object> GetNotificationLogs(int limit = 100)
    {
        return _inMemoryData
            .Where(kvp => kvp.Key.StartsWith("notification_"))
            .Take(limit)
            .Select(kvp => kvp.Value);
    }

    public IEnumerable<object> GetPluginLogs(int limit = 100)
    {
        return _inMemoryData
            .Where(kvp => kvp.Key.StartsWith("plugin_"))
            .Take(limit)
            .Select(kvp => kvp.Value);
    }

    public IEnumerable<string> GetSystemLogs(int limit = 100)
    {
        return _logs.TakeLast(limit);
    }

    public Dictionary<string, object> GetDatabaseStatus()
    {
        return new Dictionary<string, object>
        {
            ["IsUsingInMemoryFallback"] = _isUsingInMemory,
            ["TotalStoredItems"] = _inMemoryData.Count,
            ["NotificationLogs"] = _inMemoryData.Count(kvp => kvp.Key.StartsWith("notification_")),
            ["PluginLogs"] = _inMemoryData.Count(kvp => kvp.Key.StartsWith("plugin_")),
            ["SystemLogs"] = _logs.Count,
            ["MemoryUsageApproximate"] = $"{_inMemoryData.Count * 1024} bytes (estimated)",
            ["FallbackActivatedAt"] = _isUsingInMemory ? DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") : "Not activated"
        };
    }

    public void ClearData()
    {
        var itemCount = _inMemoryData.Count;
        _inMemoryData.Clear();
        LogOperation("CLEAR_DATA", $"Cleared {itemCount} items from in-memory storage", "Success");
    }

    public bool TryGetData<T>(string key, out T? data) where T : class
    {
        if (_inMemoryData.TryGetValue(key, out var value) && value is T typedValue)
        {
            data = typedValue;
            return true;
        }
        
        data = null;
        return false;
    }

    public void StoreData(string key, object data)
    {
        _inMemoryData[key] = data;
        LogOperation("STORE_DATA", $"Stored data with key: {key}", "Success");
    }
}

/// <summary>
/// Extension methods for database fallback configuration
/// </summary>
public static class DatabaseFallbackExtensions
{
    public static IServiceCollection AddDatabaseWithFallback(this IServiceCollection services, IConfiguration configuration)
    {
        // Add in-memory database service
        services.AddSingleton<InMemoryDatabaseService>();

        // Try to configure main database with fallback
        try
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            
            if (string.IsNullOrEmpty(connectionString))
            {
                Console.WriteLine("⚠️  No database connection string found - using in-memory fallback");
                return ConfigureInMemoryFallback(services);
            }

            // Test database connection
            services.AddDbContext<NotificationDbContext>(options =>
            {
                options.UseNpgsql(connectionString);
                options.EnableSensitiveDataLogging(false);
                options.EnableServiceProviderCaching(true);
            });

            Console.WriteLine("✅ Database connection configured successfully");
            return services;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️  Database configuration failed: {ex.Message}");
            Console.WriteLine("🔄 Falling back to in-memory database");
            return ConfigureInMemoryFallback(services);
        }
    }

    private static IServiceCollection ConfigureInMemoryFallback(IServiceCollection services)
    {
        // Configure in-memory database
        services.AddDbContext<NotificationDbContext>(options =>
        {
            options.UseInMemoryDatabase("NotifyMasterApi_InMemory");
            options.EnableSensitiveDataLogging(true);
        });

        // Activate the fallback service
        services.AddSingleton<IHostedService, DatabaseFallbackActivationService>();

        return services;
    }
}

/// <summary>
/// Service to activate database fallback on startup
/// </summary>
public class DatabaseFallbackActivationService : IHostedService
{
    private readonly InMemoryDatabaseService _inMemoryService;
    private readonly ILogger<DatabaseFallbackActivationService> _logger;

    public DatabaseFallbackActivationService(
        InMemoryDatabaseService inMemoryService,
        ILogger<DatabaseFallbackActivationService> logger)
    {
        _inMemoryService = inMemoryService;
        _logger = logger;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _inMemoryService.EnableInMemoryFallback();
        _logger.LogWarning("🔄 Database fallback service started - All data will be stored in memory");
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("🛑 Database fallback service stopped");
        return Task.CompletedTask;
    }
}
