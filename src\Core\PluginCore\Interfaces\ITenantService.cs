using PluginCore.Models;

namespace PluginCore.Interfaces;

/// <summary>
/// Interface for tenant management service
/// </summary>
public interface ITenantService
{
    /// <summary>
    /// Gets a tenant by ID
    /// </summary>
    Task<Tenant?> GetTenantAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a tenant by domain
    /// </summary>
    Task<Tenant?> GetTenantByDomainAsync(string domain, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all tenants with pagination
    /// </summary>
    Task<IReadOnlyList<Tenant>> GetTenantsAsync(int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Creates a new tenant
    /// </summary>
    Task<OperationResult<Tenant>> CreateTenantAsync(CreateTenantRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing tenant
    /// </summary>
    Task<OperationResult<Tenant>> UpdateTenantAsync(string tenantId, UpdateTenantRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a tenant
    /// </summary>
    Task<OperationResult> DeleteTenantAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Suspends a tenant
    /// </summary>
    Task<OperationResult> SuspendTenantAsync(string tenantId, string reason, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Activates a suspended tenant
    /// </summary>
    Task<OperationResult> ActivateTenantAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates tenant usage statistics
    /// </summary>
    Task<OperationResult> UpdateTenantUsageAsync(string tenantId, TenantUsage usage, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if tenant has reached usage limits
    /// </summary>
    Task<bool> HasReachedLimitAsync(string tenantId, string limitType, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets tenant plugins
    /// </summary>
    Task<IReadOnlyList<TenantPlugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Configures a plugin for a tenant
    /// </summary>
    Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);
}

/// <summary>
/// Request model for creating a tenant
/// </summary>
public class CreateTenantRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Domain { get; set; } = string.Empty;
    public TenantPlan Plan { get; set; } = TenantPlan.Basic;
    public Dictionary<string, object> Settings { get; set; } = new();
    public TenantLimits? CustomLimits { get; set; }
    public string? CreatedBy { get; set; }
}

/// <summary>
/// Request model for updating a tenant
/// </summary>
public class UpdateTenantRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Domain { get; set; }
    public TenantPlan? Plan { get; set; }
    public TenantStatus? Status { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public TenantLimits? Limits { get; set; }
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Interface for user management service
/// </summary>
public interface IUserService
{
    /// <summary>
    /// Gets a user by ID
    /// </summary>
    Task<User?> GetUserAsync(string userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a user by email within a tenant
    /// </summary>
    Task<User?> GetUserByEmailAsync(string tenantId, string email, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets users for a tenant with pagination
    /// </summary>
    Task<IReadOnlyList<User>> GetTenantUsersAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Creates a new user
    /// </summary>
    Task<OperationResult<User>> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing user
    /// </summary>
    Task<OperationResult<User>> UpdateUserAsync(string userId, UpdateUserRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a user
    /// </summary>
    Task<OperationResult> DeleteUserAsync(string userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Authenticates a user
    /// </summary>
    Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Changes user password
    /// </summary>
    Task<OperationResult> ChangePasswordAsync(string userId, string currentPassword, string newPassword, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Assigns a role to a user
    /// </summary>
    Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? assignedBy = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Removes a role from a user
    /// </summary>
    Task<OperationResult> RemoveRoleAsync(string userId, string roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Grants a permission to a user
    /// </summary>
    Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? grantedBy = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Revokes a permission from a user
    /// </summary>
    Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if user has a specific permission
    /// </summary>
    Task<bool> HasPermissionAsync(string userId, string tenantId, string resource, string action, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if user has a specific role
    /// </summary>
    Task<bool> HasRoleAsync(string userId, string tenantId, string roleName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all permissions for a user
    /// </summary>
    Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all roles for a user
    /// </summary>
    Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string tenantId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Request model for creating a user
/// </summary>
public class CreateUserRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string Password { get; set; } = string.Empty;
    public UserStatus Status { get; set; } = UserStatus.Active;
    public Dictionary<string, object> Profile { get; set; } = new();
    public List<string> RoleIds { get; set; } = new();
    public string? CreatedBy { get; set; }
}

/// <summary>
/// Request model for updating a user
/// </summary>
public class UpdateUserRequest
{
    public string? Username { get; set; }
    public string? Email { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public UserStatus? Status { get; set; }
    public Dictionary<string, object>? Profile { get; set; }
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Authentication result
/// </summary>
public class AuthenticationResult
{
    public User User { get; set; } = null!;
    public string Token { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public string RefreshToken { get; set; } = string.Empty;
    public IReadOnlyList<Role> Roles { get; set; } = Array.Empty<Role>();
    public IReadOnlyList<Permission> Permissions { get; set; } = Array.Empty<Permission>();
}
