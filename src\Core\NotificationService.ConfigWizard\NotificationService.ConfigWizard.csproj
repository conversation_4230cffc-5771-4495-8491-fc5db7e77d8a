<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyName>NotificationService.ConfigWizard</AssemblyName>
        <RootNamespace>NotificationService.ConfigWizard</RootNamespace>
        <PublishSingleFile>true</PublishSingleFile>
        <SelfContained>true</SelfContained>
        <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
        <PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
        <PackageReference Include="Spectre.Console" Version="0.49.1" />
        <PackageReference Include="System.Text.Json" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Services\NotifyMasterApi\NotifyMasterApi.csproj" />
    </ItemGroup>

</Project>
