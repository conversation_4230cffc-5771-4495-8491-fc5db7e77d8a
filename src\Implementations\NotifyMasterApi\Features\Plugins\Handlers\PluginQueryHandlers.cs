using MediatR;
using PluginCore.Interfaces;
using NotifyMasterApi.Features.Plugins.Queries;
using NotifyMasterApi.Models;

namespace NotifyMasterApi.Features.Plugins.Handlers;

/// <summary>
/// Handler for getting all plugins
/// </summary>
public class GetPluginsQueryHandler : IRequestHandler<GetPluginsQuery, PluginListResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginsQueryHandler> _logger;

    public GetPluginsQueryHandler(IPluginManager pluginManager, ILogger<GetPluginsQueryHandler> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<PluginListResponse> Handle(GetPluginsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var statuses = await _pluginManager.GetPluginStatusesAsync(cancellationToken);
            var manifests = _pluginManager.GetPluginManifests();

            var filteredStatuses = statuses.AsEnumerable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.Type))
            {
                filteredStatuses = filteredStatuses.Where(s =>
                    s.Type.Equals(request.Type, StringComparison.OrdinalIgnoreCase));
            }

            if (request.IsLoaded.HasValue)
            {
                filteredStatuses = filteredStatuses.Where(s => s.IsLoaded == request.IsLoaded.Value);
            }

            // Convert to API models
            var plugins = filteredStatuses.Select(status =>
            {
                var manifest = manifests.FirstOrDefault(m => m.Name == status.Name);
                return new Plugin
                {
                    Name = status.Name,
                    Version = status.Version,
                    Type = status.Type,
                    Provider = manifest?.Provider ?? "Unknown",
                    Status = status.Status,
                    IsLoaded = status.IsLoaded,
                    IsHealthy = status.IsHealthy,
                    LastChecked = status.LastChecked.DateTime,
                    ErrorMessage = status.ErrorMessage,
                    Features = manifest?.SupportedFeatures ?? new List<string>()
                };
            }).ToList();

            return new PluginListResponse
            {
                Plugins = plugins,
                Total = plugins.Count,
                Loaded = plugins.Count(p => p.IsLoaded),
                Healthy = plugins.Count(p => p.IsHealthy)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving plugins");
            return new PluginListResponse();
        }
    }
}

/// <summary>
/// Handler for getting a specific plugin
/// </summary>
public class GetPluginQueryHandler : IRequestHandler<GetPluginQuery, Plugin?>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginQueryHandler> _logger;

    public GetPluginQueryHandler(IPluginManager pluginManager, ILogger<GetPluginQueryHandler> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<Plugin?> Handle(GetPluginQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var statuses = await _pluginManager.GetPluginStatusesAsync(cancellationToken);
            var status = statuses.FirstOrDefault(s => s.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase));
            
            if (status == null)
                return null;

            var manifest = _pluginManager.GetPluginManifest(request.Name);

            return new Plugin
            {
                Name = status.Name,
                Version = status.Version,
                Type = status.Type,
                Provider = manifest?.Provider ?? "Unknown",
                Status = status.Status,
                IsLoaded = status.IsLoaded,
                IsHealthy = status.IsHealthy,
                LastChecked = status.LastChecked.DateTime,
                ErrorMessage = status.ErrorMessage,
                Features = manifest?.SupportedFeatures ?? new List<string>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving plugin {PluginName}", request.Name);
            return null;
        }
    }
}

/// <summary>
/// Handler for getting plugin health status
/// </summary>
public class GetPluginHealthQueryHandler : IRequestHandler<GetPluginHealthQuery, PluginHealthResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginHealthQueryHandler> _logger;

    public GetPluginHealthQueryHandler(IPluginManager pluginManager, ILogger<GetPluginHealthQueryHandler> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<PluginHealthResponse> Handle(GetPluginHealthQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var statuses = await _pluginManager.GetPluginStatusesAsync(cancellationToken);

            var healthList = statuses.Select(status => new PluginHealth
            {
                Name = status.Name,
                IsHealthy = status.IsHealthy,
                Status = status.Status,
                LastChecked = status.LastChecked.DateTime,
                ErrorMessage = status.ErrorMessage
            }).ToList();

            return new PluginHealthResponse
            {
                Plugins = healthList,
                Total = healthList.Count,
                Healthy = healthList.Count(h => h.IsHealthy),
                Unhealthy = healthList.Count(h => !h.IsHealthy)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving plugin health status");
            return new PluginHealthResponse();
        }
    }
}

/// <summary>
/// Handler for getting specific plugin health
/// </summary>
public class GetPluginHealthByNameQueryHandler : IRequestHandler<GetPluginHealthByNameQuery, PluginHealth?>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginHealthByNameQueryHandler> _logger;

    public GetPluginHealthByNameQueryHandler(IPluginManager pluginManager, ILogger<GetPluginHealthByNameQueryHandler> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<PluginHealth?> Handle(GetPluginHealthByNameQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var statuses = await _pluginManager.GetPluginStatusesAsync(cancellationToken);
            var status = statuses.FirstOrDefault(s => s.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase));

            if (status == null)
                return null;

            return new PluginHealth
            {
                Name = status.Name,
                IsHealthy = status.IsHealthy,
                Status = status.Status,
                LastChecked = status.LastChecked.DateTime,
                ErrorMessage = status.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving health for plugin {PluginName}", request.Name);
            return null;
        }
    }
}
