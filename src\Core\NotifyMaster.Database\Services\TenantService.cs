using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PluginCore.Interfaces;
using PluginCore.Models;

namespace NotifyMaster.Database.Services;

/// <summary>
/// Implementation of tenant management service using Entity Framework
/// </summary>
public class TenantService : ITenantService
{
    private readonly NotifyMasterDbContext _context;
    private readonly ILogger<TenantService> _logger;

    public TenantService(NotifyMasterDbContext context, ILogger<TenantService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Tenant?> GetTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Tenants
                .Include(t => t.Users)
                .Include(t => t.Plugins)
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant {TenantId}", tenantId);
            return null;
        }
    }

    public async Task<Tenant?> GetTenantByDomainAsync(string domain, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Tenants
                .Include(t => t.Users)
                .Include(t => t.Plugins)
                .FirstOrDefaultAsync(t => t.Domain == domain, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by domain {Domain}", domain);
            return null;
        }
    }

    public async Task<IReadOnlyList<Tenant>> GetTenantsAsync(int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Tenants
                .OrderBy(t => t.Name)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenants");
            return Array.Empty<Tenant>();
        }
    }

    public async Task<OperationResult<Tenant>> CreateTenantAsync(CreateTenantRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if domain already exists
            var existingTenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Domain == request.Domain, cancellationToken);
            
            if (existingTenant != null)
            {
                return OperationResult<Tenant>.Failure("A tenant with this domain already exists");
            }

            var tenant = new Tenant
            {
                Id = Guid.NewGuid().ToString(),
                Name = request.Name,
                Description = request.Description,
                Domain = request.Domain,
                Plan = request.Plan,
                Status = TenantStatus.Active,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy,
                Settings = request.Settings,
                Limits = request.CustomLimits ?? GetDefaultLimits(request.Plan),
                Usage = new TenantUsage()
            };

            _context.Tenants.Add(tenant);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created tenant {TenantId} with domain {Domain}", tenant.Id, tenant.Domain);
            return OperationResult<Tenant>.Success(tenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant {TenantName}", request.Name);
            return OperationResult<Tenant>.Failure($"Failed to create tenant: {ex.Message}");
        }
    }

    public async Task<OperationResult<Tenant>> UpdateTenantAsync(string tenantId, UpdateTenantRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                return OperationResult<Tenant>.Failure("Tenant not found");
            }

            // Update properties if provided
            if (!string.IsNullOrEmpty(request.Name))
                tenant.Name = request.Name;
            
            if (!string.IsNullOrEmpty(request.Description))
                tenant.Description = request.Description;
            
            if (!string.IsNullOrEmpty(request.Domain))
            {
                // Check if new domain already exists
                var existingTenant = await _context.Tenants
                    .FirstOrDefaultAsync(t => t.Domain == request.Domain && t.Id != tenantId, cancellationToken);
                
                if (existingTenant != null)
                {
                    return OperationResult<Tenant>.Failure("A tenant with this domain already exists");
                }
                
                tenant.Domain = request.Domain;
            }
            
            if (request.Plan.HasValue)
                tenant.Plan = request.Plan.Value;
            
            if (request.Status.HasValue)
                tenant.Status = request.Status.Value;
            
            if (request.Settings != null)
                tenant.Settings = request.Settings;
            
            if (request.Limits != null)
                tenant.Limits = request.Limits;

            tenant.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated tenant {TenantId}", tenantId);
            return OperationResult<Tenant>.Success(tenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant {TenantId}", tenantId);
            return OperationResult<Tenant>.Failure($"Failed to update tenant: {ex.Message}");
        }
    }

    public async Task<OperationResult> DeleteTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                return OperationResult.Failure("Tenant not found");
            }

            _context.Tenants.Remove(tenant);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted tenant {TenantId}", tenantId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant {TenantId}", tenantId);
            return OperationResult.Failure($"Failed to delete tenant: {ex.Message}");
        }
    }

    public async Task<OperationResult> SuspendTenantAsync(string tenantId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                return OperationResult.Failure("Tenant not found");
            }

            tenant.Status = TenantStatus.Suspended;
            tenant.UpdatedAt = DateTime.UtcNow;
            
            // Store suspension reason in settings
            tenant.Settings["SuspensionReason"] = reason;
            tenant.Settings["SuspendedAt"] = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Suspended tenant {TenantId} with reason: {Reason}", tenantId, reason);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending tenant {TenantId}", tenantId);
            return OperationResult.Failure($"Failed to suspend tenant: {ex.Message}");
        }
    }

    public async Task<OperationResult> ActivateTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                return OperationResult.Failure("Tenant not found");
            }

            tenant.Status = TenantStatus.Active;
            tenant.UpdatedAt = DateTime.UtcNow;
            
            // Remove suspension information
            tenant.Settings.Remove("SuspensionReason");
            tenant.Settings.Remove("SuspendedAt");
            tenant.Settings["ActivatedAt"] = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Activated tenant {TenantId}", tenantId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating tenant {TenantId}", tenantId);
            return OperationResult.Failure($"Failed to activate tenant: {ex.Message}");
        }
    }

    public async Task<OperationResult> UpdateTenantUsageAsync(string tenantId, TenantUsage usage, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants.FindAsync(tenantId);
            if (tenant == null)
            {
                return OperationResult.Failure("Tenant not found");
            }

            tenant.Usage = usage;
            tenant.Usage.LastUpdated = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant usage {TenantId}", tenantId);
            return OperationResult.Failure($"Failed to update tenant usage: {ex.Message}");
        }
    }

    public async Task<bool> HasReachedLimitAsync(string tenantId, string limitType, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants.FindAsync(tenantId);
            if (tenant == null) return true;

            return limitType switch
            {
                "users" => tenant.Usage.CurrentUsers >= tenant.Limits.MaxUsers,
                "messages" => tenant.Usage.MessagesThisMonth >= tenant.Limits.MaxMessagesPerMonth,
                "plugins" => tenant.Usage.ActivePlugins >= tenant.Limits.MaxPlugins,
                "templates" => tenant.Usage.Templates >= tenant.Limits.MaxTemplates,
                "webhooks" => tenant.Usage.Webhooks >= tenant.Limits.MaxWebhooks,
                _ => false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tenant limit {TenantId} {LimitType}", tenantId, limitType);
            return true; // Err on the side of caution
        }
    }

    public async Task<IReadOnlyList<TenantPlugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.TenantPlugins
                .Where(tp => tp.TenantId == tenantId)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant plugins {TenantId}", tenantId);
            return Array.Empty<TenantPlugin>();
        }
    }

    public async Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenantPlugin = await _context.TenantPlugins
                .FirstOrDefaultAsync(tp => tp.TenantId == tenantId && tp.PluginName == pluginName, cancellationToken);

            if (tenantPlugin == null)
            {
                tenantPlugin = new TenantPlugin
                {
                    Id = Guid.NewGuid().ToString(),
                    TenantId = tenantId,
                    PluginName = pluginName,
                    IsEnabled = true,
                    Configuration = configuration,
                    ConfiguredAt = DateTime.UtcNow
                };
                _context.TenantPlugins.Add(tenantPlugin);
            }
            else
            {
                tenantPlugin.Configuration = configuration;
                tenantPlugin.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Configured plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring tenant plugin {TenantId} {PluginName}", tenantId, pluginName);
            return OperationResult.Failure($"Failed to configure plugin: {ex.Message}");
        }
    }

    private TenantLimits GetDefaultLimits(TenantPlan plan)
    {
        return plan switch
        {
            TenantPlan.Basic => new TenantLimits
            {
                MaxUsers = 5,
                MaxMessagesPerMonth = 1000,
                MaxPlugins = 2,
                MaxTemplates = 10,
                MaxWebhooks = 2,
                CanUseCustomDomain = false,
                CanUseAdvancedFeatures = false
            },
            TenantPlan.Professional => new TenantLimits
            {
                MaxUsers = 25,
                MaxMessagesPerMonth = 10000,
                MaxPlugins = 10,
                MaxTemplates = 50,
                MaxWebhooks = 10,
                CanUseCustomDomain = true,
                CanUseAdvancedFeatures = true
            },
            TenantPlan.Enterprise => new TenantLimits
            {
                MaxUsers = 100,
                MaxMessagesPerMonth = 100000,
                MaxPlugins = 50,
                MaxTemplates = 200,
                MaxWebhooks = 50,
                CanUseCustomDomain = true,
                CanUseAdvancedFeatures = true
            },
            _ => new TenantLimits()
        };
    }
}
