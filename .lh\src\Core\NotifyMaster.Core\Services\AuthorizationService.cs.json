{"sourceFile": "src/Core/NotifyMaster.Core/Services/AuthorizationService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751225300245, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751225300245, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Services;\n\n/// <summary>\n/// Service responsible for authorization and permission checking\n/// </summary>\npublic interface IAuthorizationService\n{\n    /// <summary>\n    /// Checks if a user has permission to perform an action on a resource\n    /// </summary>\n    Task<bool> HasPermissionAsync(string userId, string tenantId, string resource, string action, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Checks if a user has a specific role\n    /// </summary>\n    Task<bool> HasRoleAsync(string userId, string tenantId, string roleName, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all permissions for a user\n    /// </summary>\n    Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string tenantId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all roles for a user\n    /// </summary>\n    Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string tenantId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Checks if a user can access a tenant\n    /// </summary>\n    Task<bool> CanAccessTenantAsync(string userId, string tenantId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Checks if a user is a system administrator\n    /// </summary>\n    Task<bool> IsSystemAdminAsync(string userId, CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Implementation of authorization service\n/// </summary>\npublic class AuthorizationService : IAuthorizationService\n{\n    private readonly IUserService _userService;\n    private readonly ITenantService _tenantService;\n    private readonly ILogger<AuthorizationService> _logger;\n\n    public AuthorizationService(\n        IUserService userService,\n        ITenantService tenantService,\n        ILogger<AuthorizationService> logger)\n    {\n        _userService = userService;\n        _tenantService = tenantService;\n        _logger = logger;\n    }\n\n    public async Task<bool> HasPermissionAsync(string userId, string tenantId, string resource, string action, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // System admins have all permissions\n            if (await IsSystemAdminAsync(userId, cancellationToken))\n            {\n                return true;\n            }\n\n            // Check if user can access the tenant\n            if (!await CanAccessTenantAsync(userId, tenantId, cancellationToken))\n            {\n                return false;\n            }\n\n            // Check specific permission\n            return await _userService.HasPermissionAsync(userId, tenantId, resource, action, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking permission {Resource}:{Action} for user {UserId} in tenant {TenantId}\", \n                resource, action, userId, tenantId);\n            return false;\n        }\n    }\n\n    public async Task<bool> HasRoleAsync(string userId, string tenantId, string roleName, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // System admins have all roles\n            if (await IsSystemAdminAsync(userId, cancellationToken))\n            {\n                return true;\n            }\n\n            // Check if user can access the tenant\n            if (!await CanAccessTenantAsync(userId, tenantId, cancellationToken))\n            {\n                return false;\n            }\n\n            // Check specific role\n            return await _userService.HasRoleAsync(userId, tenantId, roleName, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking role {RoleName} for user {UserId} in tenant {TenantId}\", \n                roleName, userId, tenantId);\n            return false;\n        }\n    }\n\n    public async Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if user can access the tenant\n            if (!await CanAccessTenantAsync(userId, tenantId, cancellationToken))\n            {\n                return Array.Empty<Permission>();\n            }\n\n            return await _userService.GetUserPermissionsAsync(userId, tenantId, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting permissions for user {UserId} in tenant {TenantId}\", userId, tenantId);\n            return Array.Empty<Permission>();\n        }\n    }\n\n    public async Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if user can access the tenant\n            if (!await CanAccessTenantAsync(userId, tenantId, cancellationToken))\n            {\n                return Array.Empty<Role>();\n            }\n\n            return await _userService.GetUserRolesAsync(userId, tenantId, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting roles for user {UserId} in tenant {TenantId}\", userId, tenantId);\n            return Array.Empty<Role>();\n        }\n    }\n\n    public async Task<bool> CanAccessTenantAsync(string userId, string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // System admins can access all tenants\n            if (await IsSystemAdminAsync(userId, cancellationToken))\n            {\n                return true;\n            }\n\n            // Get user and check if they belong to the tenant\n            var user = await _userService.GetUserAsync(userId, cancellationToken);\n            if (user == null)\n            {\n                return false;\n            }\n\n            // Check if user belongs to the tenant\n            return user.TenantId == tenantId;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking tenant access for user {UserId} in tenant {TenantId}\", userId, tenantId);\n            return false;\n        }\n    }\n\n    public async Task<bool> IsSystemAdminAsync(string userId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var user = await _userService.GetUserAsync(userId, cancellationToken);\n            if (user == null)\n            {\n                return false;\n            }\n\n            // Check if user has SuperAdmin role with system scope\n            var roles = await _userService.GetUserRolesAsync(userId, user.TenantId, cancellationToken);\n            return roles.Any(r => r.Name == SystemRoles.SuperAdmin && r.Scope == RoleScope.System);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking system admin status for user {UserId}\", userId);\n            return false;\n        }\n    }\n}\n"}]}