using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;

namespace PluginCore.Utilities;

/// <summary>
/// Simple rate limiter implementation for gateway operations.
/// </summary>
public class RateLimiter
{
    private readonly ConcurrentDictionary<string, TokenBucket> _buckets = new();
    private readonly Timer _cleanupTimer;

    public RateLimiter()
    {
        // Cleanup expired buckets every 5 minutes
        _cleanupTimer = new Timer(CleanupExpiredBuckets, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    /// <summary>
    /// Checks if an operation is allowed based on rate limiting rules.
    /// </summary>
    /// <param name="key">The key to rate limit (e.g., API key, user ID, etc.)</param>
    /// <param name="maxRequests">Maximum number of requests allowed</param>
    /// <param name="timeWindow">Time window for the rate limit</param>
    /// <returns>True if the operation is allowed, false otherwise</returns>
    public bool IsAllowed(string key, int maxRequests, TimeSpan timeWindow)
    {
        var bucket = _buckets.GetOrAdd(key, _ => new TokenBucket(maxRequests, timeWindow));
        return bucket.TryConsume();
    }

    /// <summary>
    /// Gets the remaining requests for a given key.
    /// </summary>
    public int GetRemainingRequests(string key, int maxRequests, TimeSpan timeWindow)
    {
        var bucket = _buckets.GetOrAdd(key, _ => new TokenBucket(maxRequests, timeWindow));
        return bucket.RemainingTokens;
    }

    /// <summary>
    /// Gets the time until the next token is available.
    /// </summary>
    public TimeSpan GetTimeUntilReset(string key, int maxRequests, TimeSpan timeWindow)
    {
        var bucket = _buckets.GetOrAdd(key, _ => new TokenBucket(maxRequests, timeWindow));
        return bucket.TimeUntilReset;
    }

    private void CleanupExpiredBuckets(object? state)
    {
        var expiredKeys = new List<string>();
        var cutoff = DateTime.UtcNow.AddHours(-1); // Remove buckets older than 1 hour

        foreach (var kvp in _buckets)
        {
            if (kvp.Value.LastAccess < cutoff)
            {
                expiredKeys.Add(kvp.Key);
            }
        }

        foreach (var key in expiredKeys)
        {
            _buckets.TryRemove(key, out _);
        }
    }

    public void Dispose()
    {
        _cleanupTimer?.Dispose();
    }
}

/// <summary>
/// Token bucket implementation for rate limiting.
/// </summary>
internal class TokenBucket
{
    private readonly int _maxTokens;
    private readonly TimeSpan _refillInterval;
    private readonly object _lock = new();
    
    private int _currentTokens;
    private DateTime _lastRefill;

    public DateTime LastAccess { get; private set; }
    
    public int RemainingTokens
    {
        get
        {
            lock (_lock)
            {
                RefillTokens();
                return _currentTokens;
            }
        }
    }

    public TimeSpan TimeUntilReset
    {
        get
        {
            lock (_lock)
            {
                if (_currentTokens > 0)
                    return TimeSpan.Zero;

                var nextRefill = _lastRefill.Add(_refillInterval);
                var timeUntilNext = nextRefill - DateTime.UtcNow;
                return timeUntilNext > TimeSpan.Zero ? timeUntilNext : TimeSpan.Zero;
            }
        }
    }

    public TokenBucket(int maxTokens, TimeSpan refillInterval)
    {
        _maxTokens = maxTokens;
        _refillInterval = refillInterval;
        _currentTokens = maxTokens;
        _lastRefill = DateTime.UtcNow;
        LastAccess = DateTime.UtcNow;
    }

    public bool TryConsume(int tokens = 1)
    {
        lock (_lock)
        {
            RefillTokens();
            LastAccess = DateTime.UtcNow;

            if (_currentTokens >= tokens)
            {
                _currentTokens -= tokens;
                return true;
            }

            return false;
        }
    }

    private void RefillTokens()
    {
        var now = DateTime.UtcNow;
        var timeSinceLastRefill = now - _lastRefill;

        if (timeSinceLastRefill >= _refillInterval)
        {
            var intervalsElapsed = (int)(timeSinceLastRefill.TotalMilliseconds / _refillInterval.TotalMilliseconds);
            var tokensToAdd = intervalsElapsed * _maxTokens;
            
            _currentTokens = Math.Min(_maxTokens, _currentTokens + tokensToAdd);
            _lastRefill = now;
        }
    }
}

/// <summary>
/// Rate limiting configuration for different operations.
/// </summary>
public class RateLimitConfig
{
    public int MaxRequests { get; set; } = 100;
    public TimeSpan TimeWindow { get; set; } = TimeSpan.FromMinutes(1);
    public bool Enabled { get; set; } = true;

    public static RateLimitConfig Default => new();
    
    public static RateLimitConfig Conservative => new()
    {
        MaxRequests = 10,
        TimeWindow = TimeSpan.FromMinutes(1)
    };
    
    public static RateLimitConfig Aggressive => new()
    {
        MaxRequests = 1000,
        TimeWindow = TimeSpan.FromMinutes(1)
    };
}

/// <summary>
/// Extension methods for rate limiting in gateway operations.
/// </summary>
public static class RateLimitExtensions
{
    private static readonly RateLimiter _globalRateLimiter = new();

    /// <summary>
    /// Executes an operation with rate limiting.
    /// </summary>
    public static async Task<T> ExecuteWithRateLimitAsync<T>(
        this Func<Task<T>> operation,
        string rateLimitKey,
        RateLimitConfig config,
        ILogger? logger = null,
        CancellationToken cancellationToken = default)
    {
        if (!config.Enabled)
        {
            return await operation();
        }

        if (!_globalRateLimiter.IsAllowed(rateLimitKey, config.MaxRequests, config.TimeWindow))
        {
            var timeUntilReset = _globalRateLimiter.GetTimeUntilReset(rateLimitKey, config.MaxRequests, config.TimeWindow);
            logger?.LogWarning("Rate limit exceeded for key {Key}. Time until reset: {TimeUntilReset}", 
                rateLimitKey, timeUntilReset);
            
            throw new InvalidOperationException($"Rate limit exceeded. Try again in {timeUntilReset.TotalSeconds:F0} seconds.");
        }

        return await operation();
    }

    /// <summary>
    /// Gets rate limit information for a key.
    /// </summary>
    public static (int remaining, TimeSpan resetTime) GetRateLimitInfo(string rateLimitKey, RateLimitConfig config)
    {
        var remaining = _globalRateLimiter.GetRemainingRequests(rateLimitKey, config.MaxRequests, config.TimeWindow);
        var resetTime = _globalRateLimiter.GetTimeUntilReset(rateLimitKey, config.MaxRequests, config.TimeWindow);
        return (remaining, resetTime);
    }
}
