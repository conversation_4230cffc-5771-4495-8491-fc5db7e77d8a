{"sourceFile": "src/Core/PluginCore/Base/MessageSendResult.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751199212204, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751199212204, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic class MessageSendResult\r\n{\r\n    public string? ProviderMessageId { get; set; }\r\n    public string? Status { get; set; }\r\n    public string? ErrorMessage { get; set; }\r\n    public Dictionary<string, object>? ResponseData { get; set; }\r\n    public Dictionary<string, object>? Metadata { get; set; }\r\n    public Dictionary<string, object>? Headers { get; set; }\r\n    public Dictionary<string, object>? QueryParameters { get; set; }\r\n    public Dictionary<string, object>? FormParameters { get; set; }\r\n    public Dictionary<string, object>? Cookies { get; set; }\r\n    public Dictionary<string, object>? Files { get; set; }\r\n    public Dictionary<string, object>? Session { get; set; }\r\n    public Dictionary<string, object>? ServerVariables { get; set; }\r\n    public Dictionary<string, object>? ClientVariables { get; set; }\r\n    public Dictionary<string, object>? CustomVariables { get; set; }\r\n    public Dictionary<string, object>? RequestData { get; set; }\r\n    public Dictionary<string, object>? ErrorData { get; set; }\r\n    public Dictionary<string, object>? LogData { get; set; }\r\n    public Dictionary<string, object>? CustomData { get; set; }\r\n    public bool IsSuccess { get; internal set; }\r\n    public string? Recipient { get; internal set; }\r\n    public string? Reason { get; internal set; }\r\n\r\n    public static explicit operator MessageSendResult(NotificationResponse response)\r\n        => new MessageSendResult\r\n        {\r\n            MessageId = response.MessageId,\r\n            Status = response.Status,\r\n            ErrorMessage = response.ErrorMessage,\r\n            ResponseData = response.ResponseData\r\n        };\r\n\r\n\r\n\r\n\r\n\r\n\r\n}\r\n\r\n\r\n}"}]}