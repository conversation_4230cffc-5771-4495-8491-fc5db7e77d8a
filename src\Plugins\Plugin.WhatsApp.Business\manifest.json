{"name": "WhatsApp Business", "version": "1.0.0", "description": "WhatsApp Business API plugin for sending messages via Meta's WhatsApp Business Platform", "author": "NotificationService Team", "type": "Messaging", "provider": "Meta WhatsApp Business", "assemblyName": "Plugin.WhatsApp.Business.dll", "entryPoint": "Plugin.WhatsApp.Business.WhatsAppBusinessPlugin", "isEnabled": true, "priority": 50, "supportedFeatures": ["SendMessage", "SendTemplate", "MessageStatus", "HealthCheck", "AdminConfig", "Metrics"], "configuration": {"accessToken": {"type": "string", "description": "WhatsApp Business API Access Token", "isRequired": true, "isSecret": true}, "phoneNumberId": {"type": "string", "description": "WhatsApp Business Phone Number ID", "isRequired": true, "isSecret": false}, "businessAccountId": {"type": "string", "description": "WhatsApp Business Account ID", "isRequired": true, "isSecret": false}, "webhookVerifyToken": {"type": "string", "description": "Webhook verification token for receiving message status updates", "isRequired": false, "isSecret": true}}, "endpoints": {"send": "https://graph.facebook.com/v18.0/{phone_number_id}/messages", "status": "https://graph.facebook.com/v18.0/{business_account_id}", "webhook": "/webhook/whatsapp"}, "rateLimit": {"requestsPerSecond": 10, "requestsPerMinute": 600, "requestsPerHour": 36000}, "adminFeatures": {"configurationManagement": true, "metricsViewing": true, "healthMonitoring": true, "templateManagement": true}}