@page "/login"
@layout EmptyLayout
@inject IAuthService AuthService
@inject IAppStateService AppState
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<MudContainer MaxWidth="MaxWidth.Small" Class="login-container">
    <div class="login-card glass-effect animate-fade-in">
        <div class="login-header text-center mb-6">
            <div class="login-logo mb-4">
                @if (!string.IsNullOrEmpty(_logoUrl))
                {
                    <div class="logo-image-container">
                        <MudImage Src="@_logoUrl" Alt="@_appTitle" Height="64" Class="logo-image" />
                    </div>
                }
                else
                {
                    <div class="logo-container gradient-primary">
                        <MudIcon Icon="@Icons.Material.Filled.Notifications" Size="Size.Large" Class="logo-icon" />
                    </div>
                }
            </div>
            <MudText Typo="Typo.h4" Class="login-title text-gradient mb-2">@_appTitle</MudText>
            <MudText Typo="Typo.subtitle1" Class="login-subtitle mud-text-secondary">
                Sign in to your account
            </MudText>

            @if (_showTenantSelection && _availableTenants.Count > 1)
            {
                <div class="tenant-indicator mt-3">
                    <MudChip Size="Size.Small"
                             Color="Color.Primary"
                             Variant="Variant.Outlined"
                             Icon="@Icons.Material.Filled.Business">
                        Multi-Tenant Environment
                    </MudChip>
                </div>
            }
        </div>

        <MudForm @ref="_form" @bind-IsValid="@_isFormValid" Class="login-form modern-form">
            <!-- Tenant Selection (if multi-tenant registration is enabled) -->
            @if (_showTenantSelection && _availableTenants.Count > 1)
            {
                <div class="form-group mb-4">
                    <MudSelect @bind-Value="_selectedTenant"
                               Label="Organization"
                               Variant="Variant.Outlined"
                               FullWidth="true"
                               Adornment="Adornment.Start"
                               AdornmentIcon="@Icons.Material.Filled.Business"
                               Class="modern-input">
                        @foreach (var tenant in _availableTenants)
                        {
                            <MudSelectItem Value="@tenant.Id">
                                <div class="d-flex align-center">
                                    @if (!string.IsNullOrEmpty(tenant.Whitelabel?.LogoUrl))
                                    {
                                        <MudAvatar Size="Size.Small" Class="mr-2">
                                            <MudImage Src="@tenant.Whitelabel.LogoUrl" />
                                        </MudAvatar>
                                    }
                                    <div>
                                        <MudText Typo="Typo.body2">@tenant.Name</MudText>
                                        <MudText Typo="Typo.caption" Class="mud-text-secondary">@tenant.Domain</MudText>
                                    </div>
                                </div>
                            </MudSelectItem>
                        }
                    </MudSelect>
                    <MudText Typo="Typo.caption" Class="mud-text-secondary mt-1">
                        Select your organization to continue
                    </MudText>
                </div>
            }

            <div class="form-group mb-4">
                <MudTextField @bind-Value="_email"
                              Label="Email Address"
                              Variant="Variant.Outlined"
                              Required="true"
                              RequiredError="Email is required"
                              Validation="@(new EmailAddressAttribute() { ErrorMessage = "Invalid email format" })"
                              FullWidth="true"
                              InputType="InputType.Email"
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Email"
                              OnKeyPress="@HandleKeyPress"
                              Class="modern-input" />
                <MudText Typo="Typo.caption" Class="mud-text-secondary mt-1">
                    Enter your registered email address
                </MudText>
            </div>

            <div class="form-group mb-4">
                <MudTextField @bind-Value="_password"
                              Label="Password"
                              Variant="Variant.Outlined"
                              Required="true"
                              RequiredError="Password is required"
                              FullWidth="true"
                              InputType="@(_showPassword ? InputType.Text : InputType.Password)"
                              Adornment="Adornment.End"
                              AdornmentIcon="@(_showPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                              OnAdornmentClick="TogglePasswordVisibility"
                              OnKeyPress="@HandleKeyPress"
                              Class="modern-input" />
                <div class="d-flex justify-space-between align-center mt-1">
                    <MudText Typo="Typo.caption" Class="mud-text-secondary">
                        Enter your password
                    </MudText>
                    <MudLink Href="/forgot-password"
                             Color="Color.Primary"
                             Class="forgot-password-link">
                        Forgot password?
                    </MudLink>
                </div>
            </div>

            <div class="d-flex justify-space-between align-center mb-4">
                <MudCheckBox @bind-Value="_rememberMe"
                            Label="Remember me"
                            Color="Color.Primary"
                            Class="remember-me-checkbox" />
                <MudLink Href="/forgot-password"
                         Color="Color.Primary"
                         Class="forgot-password-link">
                    Forgot password?
                </MudLink>
            </div>

            <!-- Login Button -->
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       Size="Size.Large"
                       FullWidth="true"
                       Class="mb-4 login-button"
                       OnClick="HandleLogin"
                       Disabled="@(!_isFormValid || _isLoading)">
                @if (_isLoading)
                {
                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                    Signing In...
                }
                else
                {
                    <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-2" />
                    Sign In
                }
            </MudButton>

            <!-- Two-Factor Authentication (if enabled) -->
            @if (_showTwoFactor)
            {
                <MudTextField @bind-Value="_twoFactorCode"
                              Label="Two-Factor Authentication Code"
                              Variant="Variant.Outlined"
                              Required="true"
                              RequiredError="2FA code is required"
                              Class="mb-4"
                              FullWidth="true"
                              InputType="InputType.Text"
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Security"
                              MaxLength="6"
                              OnKeyPress="@HandleKeyPress" />

                <MudButton Variant="Variant.Filled"
                           Color="Color.Secondary"
                           Size="Size.Large"
                           FullWidth="true"
                           Class="mb-4"
                           OnClick="HandleTwoFactorLogin"
                           Disabled="@(string.IsNullOrEmpty(_twoFactorCode) || _isLoading)">
                    @if (_isLoading)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                        Verifying...
                    }
                    else
                    {
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser" Class="mr-2" />
                        Verify Code
                    }
                </MudButton>
            }
        </MudForm>

        <MudDivider Class="my-4" />

        <!-- Registration Link (if enabled) -->
        @if (_allowRegistration)
        {
            <div class="text-center mb-4">
                <MudText Typo="Typo.body2" Class="mud-text-secondary mb-2">
                    Don't have an account?
                </MudText>
                <MudButton Variant="Variant.Text"
                           Color="Color.Primary"
                           OnClick="@(() => Navigation.NavigateTo("/register"))"
                           StartIcon="@Icons.Material.Filled.PersonAdd">
                    Create Account
                </MudButton>
            </div>
        }

        <!-- Social Login Options (if configured) -->
        @if (_socialLoginEnabled)
        {
            <div class="social-login-section">
                <MudText Typo="Typo.body2" Class="text-center mud-text-secondary mb-3">
                    Or sign in with
                </MudText>
                <div class="d-flex justify-center gap-2">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Default"
                               StartIcon="@Icons.Custom.Brands.Google"
                               OnClick="HandleGoogleLogin"
                               Class="social-login-button">
                        Google
                    </MudButton>
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Default"
                               StartIcon="@Icons.Custom.Brands.Microsoft"
                               OnClick="HandleMicrosoftLogin"
                               Class="social-login-button">
                        Microsoft
                    </MudButton>
                </div>
            </div>
        }

        <!-- Demo Credentials (only in development) -->
        @if (_showDemoCredentials)
        {
            <MudAlert Severity="Severity.Info" Class="mt-4 demo-alert">
                <MudText Typo="Typo.body2">
                    <strong>Demo Credentials:</strong><br />
                    Email: <EMAIL><br />
                    Password: demo123
                </MudText>
                <MudButton Size="Size.Small"
                           Variant="Variant.Text"
                           Color="Color.Primary"
                           OnClick="FillDemoCredentials"
                           Class="mt-2">
                    Use Demo Credentials
                </MudButton>
            </MudAlert>
        }

        <!-- Security Notice -->
        <div class="security-notice mt-4">
            <div class="security-badge">
                <MudIcon Icon="@Icons.Material.Filled.Security" Size="Size.Small" Class="mr-1" />
                <MudText Typo="Typo.caption" Class="mud-text-secondary">
                    Your connection is secure and encrypted
                </MudText>
            </div>
        </div>

        <!-- Footer Links -->
        <div class="login-footer mt-4 text-center">
            <div class="footer-links">
                <MudLink Href="/privacy" Class="footer-link">Privacy Policy</MudLink>
                <span class="mx-2">•</span>
                <MudLink Href="/terms" Class="footer-link">Terms of Service</MudLink>
                <span class="mx-2">•</span>
                <MudLink Href="/help" Class="footer-link">Help</MudLink>
            </div>
            <MudText Typo="Typo.caption" Class="mud-text-secondary mt-2">
                © 2024 NotificationService. All rights reserved.
            </MudText>
        </div>
    </div>
</MudContainer>

@code {
    private MudForm? _form;
    private bool _isFormValid;
    private bool _isLoading;
    private bool _showPassword;
    private bool _rememberMe;
    private bool _showTwoFactor;
    private bool _showTenantSelection;
    private bool _allowRegistration = true;
    private bool _socialLoginEnabled = false;
    private bool _showDemoCredentials = true; // Set based on environment

    private string _email = "";
    private string _password = "";
    private string _twoFactorCode = "";
    private string _selectedTenant = "";
    private string _appTitle = "NotificationService";
    private string? _logoUrl;

    private List<Tenant> _availableTenants = new();

    protected override async Task OnInitializedAsync()
    {
        // Redirect if already authenticated
        if (await AuthService.IsAuthenticatedAsync())
        {
            var returnUrl = Navigation.GetUriWithQueryParameter("returnUrl", (string?)null);
            Navigation.NavigateTo(returnUrl ?? "/dashboard");
            return;
        }

        // Load app configuration
        await LoadAppConfiguration();

        // Load available tenants if multi-tenant registration is enabled
        if (_showTenantSelection)
        {
            await LoadAvailableTenants();
        }

        // Set demo credentials in development
        if (_showDemoCredentials)
        {
            _email = "<EMAIL>";
            _password = "demo123";
        }
    }

    private async Task HandleLogin()
    {
        if (!_isFormValid) return;

        _isLoading = true;
        StateHasChanged();

        try
        {
            var success = await AuthService.LoginAsync(_email, _password);
            
            if (success)
            {
                Snackbar.Add("Login successful!", Severity.Success);
                Navigation.NavigateTo("/dashboard");
            }
            else
            {
                Snackbar.Add("Invalid email or password", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Login failed: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void TogglePasswordVisibility()
    {
        _showPassword = !_showPassword;
    }
}

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-attachment: fixed;
        padding: var(--spacing-lg);
        position: relative;
    }

    .login-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .login-card {
        max-width: 420px;
        width: 100%;
        padding: var(--spacing-3xl);
        border-radius: var(--radius-2xl);
        position: relative;
        z-index: 1;
        box-shadow: var(--shadow-2xl);
    }

    .login-header {
        position: relative;
    }

    .login-logo {
        display: flex;
        justify-content: center;
    }

    .logo-container {
        width: 72px;
        height: 72px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-xl);
        position: relative;
    }

    .logo-container::before {
        content: '';
        position: absolute;
        inset: -2px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--mud-palette-primary), var(--mud-palette-secondary));
        z-index: -1;
    }

    .logo-image-container {
        width: 72px;
        height: 72px;
        border-radius: 50%;
        overflow: hidden;
        box-shadow: var(--shadow-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
    }

    .logo-image {
        border-radius: 50%;
    }

    .logo-icon {
        color: white;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    }

    .login-title {
        font-weight: var(--font-weight-extrabold);
        letter-spacing: -0.02em;
    }

    .login-subtitle {
        font-weight: var(--font-weight-medium);
        margin-bottom: var(--spacing-lg);
    }

    .tenant-indicator {
        display: flex;
        justify-content: center;
    }

    .modern-form {
        margin-top: var(--spacing-xl);
    }

    .form-group {
        position: relative;
    }

    .modern-input {
        transition: all var(--transition-normal);
    }

    .modern-input:focus-within {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .forgot-password-link {
        font-size: 0.875rem;
        text-decoration: none;
        font-weight: var(--font-weight-medium);
        transition: color var(--transition-fast);
    }

    .forgot-password-link:hover {
        text-decoration: none;
        color: var(--mud-palette-primary-hover);
    }

    .remember-me-checkbox {
        font-size: 0.875rem;
    }

    .login-button {
        height: 52px;
        font-weight: var(--font-weight-semibold);
        text-transform: none;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        transition: all var(--transition-normal);
    }

    .login-button:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    .login-button:active {
        transform: translateY(0);
    }

    .social-login-section {
        margin: var(--spacing-xl) 0;
        position: relative;
    }

    .social-login-section::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--mud-palette-divider);
        z-index: 1;
    }

    .social-login-section .mud-text {
        background: var(--mud-palette-surface);
        padding: 0 var(--spacing-lg);
        position: relative;
        z-index: 2;
        display: inline-block;
    }

    .social-login-button {
        flex: 1;
        height: 48px;
        border-radius: var(--radius-lg);
        text-transform: none;
        font-weight: var(--font-weight-medium);
        transition: all var(--transition-normal);
        border: 2px solid var(--mud-palette-divider);
    }

    .social-login-button:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--mud-palette-primary);
    }

    .demo-alert {
        border-radius: var(--radius-lg);
        border: none;
        box-shadow: var(--shadow-sm);
        background: linear-gradient(135deg, var(--mud-palette-info-lighten), var(--mud-palette-info-lighten));
    }

    .security-notice {
        text-align: center;
        margin-top: var(--spacing-lg);
    }

    .security-badge {
        display: inline-flex;
        align-items: center;
        padding: var(--spacing-sm) var(--spacing-md);
        background: rgba(0, 0, 0, 0.02);
        border-radius: var(--radius-md);
        border: 1px solid var(--mud-palette-divider-light);
    }

    .login-footer {
        border-top: 1px solid var(--mud-palette-divider-light);
        padding-top: var(--spacing-lg);
        margin-top: var(--spacing-xl);
    }

    .footer-links {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-sm);
    }

    .footer-link {
        color: var(--mud-palette-text-secondary);
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: var(--font-weight-medium);
        transition: color var(--transition-fast);
    }

    .footer-link:hover {
        color: var(--mud-palette-primary);
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .login-container {
            padding: var(--spacing-md);
            align-items: flex-start;
            padding-top: var(--spacing-xl);
        }

        .login-card {
            padding: var(--spacing-xl);
        }

        .logo-container,
        .logo-image-container {
            width: 64px;
            height: 64px;
        }

        .social-login-button {
            width: 100%;
            margin-bottom: var(--spacing-sm);
        }

        .footer-links {
            flex-direction: column;
            gap: var(--spacing-sm);
        }
    }

    @media (max-width: 480px) {
        .login-card {
            padding: var(--spacing-lg);
        }

        .login-title {
            font-size: 1.75rem;
        }

        .login-subtitle {
            font-size: 1rem;
        }

        .login-button {
            height: 48px;
        }
    }

    /* Dark theme adjustments */
    .mud-theme-dark .login-card {
        background-color: rgba(30, 41, 59, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mud-theme-dark .security-badge {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .mud-theme-dark .social-login-section .mud-text {
        background: rgba(30, 41, 59, 0.9);
    }

    /* Accessibility */
    @media (prefers-reduced-motion: reduce) {
        .animate-fade-in {
            animation: none;
        }

        .modern-input:focus-within,
        .login-button:hover,
        .social-login-button:hover {
            transform: none;
        }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
        .login-card {
            border: 2px solid var(--mud-palette-text-primary);
        }

        .logo-container {
            border: 2px solid var(--mud-palette-text-primary);
        }

        .security-badge {
            border: 2px solid var(--mud-palette-text-primary);
        }
    }

    /* Focus states */
    .login-button:focus-visible,
    .social-login-button:focus-visible {
        outline: 2px solid var(--mud-palette-primary);
        outline-offset: 2px;
    }
</style>
