{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Plugins/ManagePlugins.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1751212787043, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751212805766, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,66 +5,11 @@\n using NotifyMasterApi.Models;\n \n namespace NotifyMasterApi.Features.Plugins;\n \n-/// <summary>\n-/// Load a plugin at runtime\n-/// </summary>\n-public class LoadPluginRequest\n-{\n-    /// <summary>\n-    /// Name of the plugin to load\n-    /// </summary>\n-    /// <example>SendGrid</example>\n-    [Required]\n-    public string PluginName { get; set; } = string.Empty;\n+// Simple endpoint request models (using the models from NotifyMasterApi.Models)\n \n-    /// <summary>\n-    /// Path to the plugin file (optional, will search in plugins directory if not provided)\n-    /// </summary>\n-    /// <example>plugins/Plugin.Email.SendGrid.dll</example>\n-    public string? PluginPath { get; set; }\n-}\n-\n /// <summary>\n-/// Unload a plugin at runtime\n-/// </summary>\n-public class UnloadPluginRequest\n-{\n-    /// <summary>\n-    /// Name of the plugin to unload\n-    /// </summary>\n-    /// <example>SendGrid</example>\n-    [Required]\n-    public string PluginName { get; set; } = string.Empty;\n-}\n-\n-/// <summary>\n-/// Response for plugin management operations\n-/// </summary>\n-public class PluginManagementResponse\n-{\n-    public bool Success { get; set; }\n-    public string Message { get; set; } = string.Empty;\n-    public string? PluginName { get; set; }\n-    public string? PluginVersion { get; set; }\n-    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\n-}\n-\n-/// <summary>\n-/// Load plugins from directory\n-/// </summary>\n-public class LoadPluginsFromDirectoryRequest\n-{\n-    /// <summary>\n-    /// Directory path containing plugins\n-    /// </summary>\n-    /// <example>plugins</example>\n-    [Required]\n-    public string Directory { get; set; } = \"plugins\";\n-}\n-\n-/// <summary>\n /// Endpoint for loading a plugin at runtime\n /// </summary>\n public class LoadPluginEndpoint : Endpoint<LoadPluginRequest, PluginManagementResponse>\n {\n"}, {"date": 1751212831213, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,86 +8,36 @@\n \n // Simple endpoint request models (using the models from NotifyMasterApi.Models)\n \n /// <summary>\n-/// Endpoint for loading a plugin at runtime\n+/// Load plugin endpoint\n /// </summary>\n-public class LoadPluginEndpoint : Endpoint<LoadPluginRequest, PluginManagementResponse>\n+public class LoadPluginEndpoint : Endpoint<LoadPluginRequest, PluginOperationResult>\n {\n-    private readonly IPluginManager _pluginManager;\n-    private readonly ILogger<LoadPluginEndpoint> _logger;\n+    private readonly IMediator _mediator;\n \n-    public LoadPluginEndpoint(IPluginManager pluginManager, ILogger<LoadPluginEndpoint> logger)\n+    public LoadPluginEndpoint(IMediator mediator)\n     {\n-        _pluginManager = pluginManager;\n-        _logger = logger;\n+        _mediator = mediator;\n     }\n \n     public override void Configure()\n     {\n         Post(\"/api/plugins/load\");\n         AllowAnonymous();\n         Summary(s =>\n         {\n-            s.Summary = \"Load Plugin at Runtime\";\n-            s.Description = \"Dynamically load a plugin by name or path without restarting the application\";\n-            s.Response<PluginManagementResponse>(200, \"Plugin loaded successfully\");\n-            s.Response<PluginManagementResponse>(400, \"Invalid request or plugin not found\");\n-            s.Response<PluginManagementResponse>(500, \"Failed to load plugin\");\n+            s.Summary = \"Load Plugin\";\n+            s.Description = \"Load a plugin by name or path\";\n+            s.Response<PluginOperationResult>(200, \"Plugin loaded successfully\");\n         });\n     }\n \n     public override async Task HandleAsync(LoadPluginRequest req, CancellationToken ct)\n     {\n-        try\n-        {\n-            _logger.LogInformation(\"Loading plugin: {PluginName}\", req.PluginName);\n-\n-            PluginCore.Models.OperationResult result;\n-\n-            if (!string.IsNullOrEmpty(req.PluginPath))\n-            {\n-                // Load from specific path\n-                result = await _pluginManager.LoadPluginAsync(req.PluginPath, ct);\n-            }\n-            else\n-            {\n-                // Load by name from plugins directory\n-                result = await _pluginManager.LoadPluginByNameAsync(req.PluginName, \"plugins\", ct);\n-            }\n-\n-            if (result.IsSuccess)\n-            {\n-                var manifest = _pluginManager.GetPluginManifest(req.PluginName);\n-                \n-                await SendOkAsync(new PluginManagementResponse\n-                {\n-                    Success = true,\n-                    Message = result.Message ?? \"Plugin loaded successfully\",\n-                    PluginName = req.PluginName,\n-                    PluginVersion = manifest?.Version\n-                }, ct);\n-            }\n-            else\n-            {\n-                await SendAsync(new PluginManagementResponse\n-                {\n-                    Success = false,\n-                    Message = result.Message ?? \"Failed to load plugin\",\n-                    PluginName = req.PluginName\n-                }, 400, ct);\n-            }\n-        }\n-        catch (Exception ex)\n-        {\n-            _logger.LogError(ex, \"Error loading plugin {PluginName}\", req.PluginName);\n-            await SendAsync(new PluginManagementResponse\n-            {\n-                Success = false,\n-                Message = $\"Error loading plugin: {ex.Message}\",\n-                PluginName = req.PluginName\n-            }, 500, ct);\n-        }\n+        var command = new LoadPluginCommand(req.Name, req.Path);\n+        var result = await _mediator.Send(command, ct);\n+        await SendOkAsync(result, ct);\n     }\n }\n \n /// <summary>\n"}, {"date": 1751212853052, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -40,72 +40,36 @@\n     }\n }\n \n /// <summary>\n-/// Endpoint for unloading a plugin at runtime\n+/// Unload plugin endpoint\n /// </summary>\n-public class UnloadPluginEndpoint : Endpoint<UnloadPluginRequest, PluginManagementResponse>\n+public class UnloadPluginEndpoint : Endpoint<UnloadPluginRequest, PluginOperationResult>\n {\n-    private readonly IPluginManager _pluginManager;\n-    private readonly ILogger<UnloadPluginEndpoint> _logger;\n+    private readonly IMediator _mediator;\n \n-    public UnloadPluginEndpoint(IPluginManager pluginManager, ILogger<UnloadPluginEndpoint> logger)\n+    public UnloadPluginEndpoint(IMediator mediator)\n     {\n-        _pluginManager = pluginManager;\n-        _logger = logger;\n+        _mediator = mediator;\n     }\n \n     public override void Configure()\n     {\n         Post(\"/api/plugins/unload\");\n         AllowAnonymous();\n         Summary(s =>\n         {\n-            s.Summary = \"Unload Plugin at Runtime\";\n-            s.Description = \"Dynamically unload a plugin by name without restarting the application\";\n-            s.Response<PluginManagementResponse>(200, \"Plugin unloaded successfully\");\n-            s.Response<PluginManagementResponse>(400, \"Plugin not found or not loaded\");\n-            s.Response<PluginManagementResponse>(500, \"Failed to unload plugin\");\n+            s.Summary = \"Unload Plugin\";\n+            s.Description = \"Unload a plugin by name\";\n+            s.Response<PluginOperationResult>(200, \"Plugin unloaded successfully\");\n         });\n     }\n \n     public override async Task HandleAsync(UnloadPluginRequest req, CancellationToken ct)\n     {\n-        try\n-        {\n-            _logger.LogInformation(\"Unloading plugin: {PluginName}\", req.PluginName);\n-\n-            var result = await _pluginManager.UnloadPluginAsync(req.PluginName, ct);\n-\n-            if (result.IsSuccess)\n-            {\n-                await SendOkAsync(new PluginManagementResponse\n-                {\n-                    Success = true,\n-                    Message = result.Message ?? \"Plugin unloaded successfully\",\n-                    PluginName = req.PluginName\n-                }, ct);\n-            }\n-            else\n-            {\n-                await SendAsync(new PluginManagementResponse\n-                {\n-                    Success = false,\n-                    Message = result.Message ?? \"Failed to unload plugin\",\n-                    PluginName = req.PluginName\n-                }, 400, ct);\n-            }\n-        }\n-        catch (Exception ex)\n-        {\n-            _logger.LogError(ex, \"Error unloading plugin {PluginName}\", req.PluginName);\n-            await SendAsync(new PluginManagementResponse\n-            {\n-                Success = false,\n-                Message = $\"Error unloading plugin: {ex.Message}\",\n-                PluginName = req.PluginName\n-            }, 500, ct);\n-        }\n+        var command = new UnloadPluginCommand(req.Name);\n+        var result = await _mediator.Send(command, ct);\n+        await SendOkAsync(result, ct);\n     }\n }\n \n /// <summary>\n"}, {"date": 1751212874556, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -72,71 +72,36 @@\n     }\n }\n \n /// <summary>\n-/// Endpoint for loading all plugins from a directory\n+/// Load directory endpoint\n /// </summary>\n-public class LoadPluginsFromDirectoryEndpoint : Endpoint<LoadPluginsFromDirectoryRequest, PluginManagementResponse>\n+public class LoadDirectoryEndpoint : Endpoint<LoadDirectoryRequest, PluginOperationResult>\n {\n-    private readonly IPluginManager _pluginManager;\n-    private readonly ILogger<LoadPluginsFromDirectoryEndpoint> _logger;\n+    private readonly IMediator _mediator;\n \n-    public LoadPluginsFromDirectoryEndpoint(IPluginManager pluginManager, ILogger<LoadPluginsFromDirectoryEndpoint> logger)\n+    public LoadDirectoryEndpoint(IMediator mediator)\n     {\n-        _pluginManager = pluginManager;\n-        _logger = logger;\n+        _mediator = mediator;\n     }\n \n     public override void Configure()\n     {\n         Post(\"/api/plugins/load-directory\");\n         AllowAnonymous();\n         Summary(s =>\n         {\n-            s.Summary = \"Load All Plugins from Directory\";\n-            s.Description = \"Load all plugins found in the specified directory\";\n-            s.Response<PluginManagementResponse>(200, \"Plugins loaded successfully\");\n-            s.Response<PluginManagementResponse>(400, \"Directory not found or invalid\");\n-            s.Response<PluginManagementResponse>(500, \"Failed to load plugins\");\n+            s.Summary = \"Load Directory\";\n+            s.Description = \"Load all plugins from a directory\";\n+            s.Response<PluginOperationResult>(200, \"Plugins loaded successfully\");\n         });\n     }\n \n-    public override async Task HandleAsync(LoadPluginsFromDirectoryRequest req, CancellationToken ct)\n+    public override async Task HandleAsync(LoadDirectoryRequest req, CancellationToken ct)\n     {\n-        try\n-        {\n-            _logger.LogInformation(\"Loading plugins from directory: {Directory}\", req.Directory);\n-\n-            var result = await _pluginManager.LoadPluginsAsync(req.Directory, ct);\n-\n-            if (result.IsSuccess)\n-            {\n-                var manifests = _pluginManager.GetPluginManifests();\n-                \n-                await SendOkAsync(new PluginManagementResponse\n-                {\n-                    Success = true,\n-                    Message = $\"{result.Message}. Loaded {manifests.Count} plugins.\",\n-                }, ct);\n-            }\n-            else\n-            {\n-                await SendAsync(new PluginManagementResponse\n-                {\n-                    Success = false,\n-                    Message = result.Message ?? \"Failed to load plugins from directory\"\n-                }, 400, ct);\n-            }\n-        }\n-        catch (Exception ex)\n-        {\n-            _logger.LogError(ex, \"Error loading plugins from directory {Directory}\", req.Directory);\n-            await SendAsync(new PluginManagementResponse\n-            {\n-                Success = false,\n-                Message = $\"Error loading plugins: {ex.Message}\"\n-            }, 500, ct);\n-        }\n+        var command = new LoadDirectoryCommand(req.Directory);\n+        var result = await _mediator.Send(command, ct);\n+        await SendOkAsync(result, ct);\n     }\n }\n \n /// <summary>\n"}, {"date": 1751212895005, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -104,33 +104,34 @@\n     }\n }\n \n /// <summary>\n-/// Endpoint for getting plugin health status\n+/// Plugin health endpoint\n /// </summary>\n-public class GetPluginHealthEndpoint : EndpointWithoutRequest<List<PluginCore.Interfaces.PluginStatus>>\n+public class GetPluginHealthEndpoint : EndpointWithoutRequest<PluginHealthResponse>\n {\n-    private readonly IPluginManager _pluginManager;\n+    private readonly IMediator _mediator;\n \n-    public GetPluginHealthEndpoint(IPluginManager pluginManager)\n+    public GetPluginHealthEndpoint(IMediator mediator)\n     {\n-        _pluginManager = pluginManager;\n+        _mediator = mediator;\n     }\n \n     public override void Configure()\n     {\n         Get(\"/api/plugins/health\");\n         AllowAnonymous();\n         Summary(s =>\n         {\n-            s.Summary = \"Get Plugin Health Status\";\n-            s.Description = \"Get detailed health status of all loaded plugins\";\n-            s.Response<List<PluginCore.Interfaces.PluginStatus>>(200, \"Plugin health status retrieved successfully\");\n+            s.Summary = \"Get Plugin Health\";\n+            s.Description = \"Get health status of all plugins\";\n+            s.Response<PluginHealthResponse>(200, \"Plugin health retrieved successfully\");\n         });\n     }\n \n     public override async Task HandleAsync(CancellationToken ct)\n     {\n-        var statuses = await _pluginManager.GetPluginStatusesAsync(ct);\n-        await SendOkAsync(statuses.ToList(), ct);\n+        var query = new GetPluginHealthQuery();\n+        var result = await _mediator.Send(query, ct);\n+        await SendOkAsync(result, ct);\n     }\n }\n"}], "date": 1751212787043, "name": "Commit-0", "content": "using FastEndpoints;\nusing MediatR;\nusing NotifyMasterApi.Features.Plugins.Commands;\nusing NotifyMasterApi.Features.Plugins.Queries;\nusing NotifyMasterApi.Models;\n\nnamespace NotifyMasterApi.Features.Plugins;\n\n/// <summary>\n/// Load a plugin at runtime\n/// </summary>\npublic class LoadPluginRequest\n{\n    /// <summary>\n    /// Name of the plugin to load\n    /// </summary>\n    /// <example>SendGrid</example>\n    [Required]\n    public string PluginName { get; set; } = string.Empty;\n\n    /// <summary>\n    /// Path to the plugin file (optional, will search in plugins directory if not provided)\n    /// </summary>\n    /// <example>plugins/Plugin.Email.SendGrid.dll</example>\n    public string? PluginPath { get; set; }\n}\n\n/// <summary>\n/// Unload a plugin at runtime\n/// </summary>\npublic class UnloadPluginRequest\n{\n    /// <summary>\n    /// Name of the plugin to unload\n    /// </summary>\n    /// <example>SendGrid</example>\n    [Required]\n    public string PluginName { get; set; } = string.Empty;\n}\n\n/// <summary>\n/// Response for plugin management operations\n/// </summary>\npublic class PluginManagementResponse\n{\n    public bool Success { get; set; }\n    public string Message { get; set; } = string.Empty;\n    public string? PluginName { get; set; }\n    public string? PluginVersion { get; set; }\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\n}\n\n/// <summary>\n/// Load plugins from directory\n/// </summary>\npublic class LoadPluginsFromDirectoryRequest\n{\n    /// <summary>\n    /// Directory path containing plugins\n    /// </summary>\n    /// <example>plugins</example>\n    [Required]\n    public string Directory { get; set; } = \"plugins\";\n}\n\n/// <summary>\n/// Endpoint for loading a plugin at runtime\n/// </summary>\npublic class LoadPluginEndpoint : Endpoint<LoadPluginRequest, PluginManagementResponse>\n{\n    private readonly IPluginManager _pluginManager;\n    private readonly ILogger<LoadPluginEndpoint> _logger;\n\n    public LoadPluginEndpoint(IPluginManager pluginManager, ILogger<LoadPluginEndpoint> logger)\n    {\n        _pluginManager = pluginManager;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/plugins/load\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Load Plugin at Runtime\";\n            s.Description = \"Dynamically load a plugin by name or path without restarting the application\";\n            s.Response<PluginManagementResponse>(200, \"Plugin loaded successfully\");\n            s.Response<PluginManagementResponse>(400, \"Invalid request or plugin not found\");\n            s.Response<PluginManagementResponse>(500, \"Failed to load plugin\");\n        });\n    }\n\n    public override async Task HandleAsync(LoadPluginRequest req, CancellationToken ct)\n    {\n        try\n        {\n            _logger.LogInformation(\"Loading plugin: {PluginName}\", req.PluginName);\n\n            PluginCore.Models.OperationResult result;\n\n            if (!string.IsNullOrEmpty(req.PluginPath))\n            {\n                // Load from specific path\n                result = await _pluginManager.LoadPluginAsync(req.PluginPath, ct);\n            }\n            else\n            {\n                // Load by name from plugins directory\n                result = await _pluginManager.LoadPluginByNameAsync(req.PluginName, \"plugins\", ct);\n            }\n\n            if (result.IsSuccess)\n            {\n                var manifest = _pluginManager.GetPluginManifest(req.PluginName);\n                \n                await SendOkAsync(new PluginManagementResponse\n                {\n                    Success = true,\n                    Message = result.Message ?? \"Plugin loaded successfully\",\n                    PluginName = req.PluginName,\n                    PluginVersion = manifest?.Version\n                }, ct);\n            }\n            else\n            {\n                await SendAsync(new PluginManagementResponse\n                {\n                    Success = false,\n                    Message = result.Message ?? \"Failed to load plugin\",\n                    PluginName = req.PluginName\n                }, 400, ct);\n            }\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error loading plugin {PluginName}\", req.PluginName);\n            await SendAsync(new PluginManagementResponse\n            {\n                Success = false,\n                Message = $\"Error loading plugin: {ex.Message}\",\n                PluginName = req.PluginName\n            }, 500, ct);\n        }\n    }\n}\n\n/// <summary>\n/// Endpoint for unloading a plugin at runtime\n/// </summary>\npublic class UnloadPluginEndpoint : Endpoint<UnloadPluginRequest, PluginManagementResponse>\n{\n    private readonly IPluginManager _pluginManager;\n    private readonly ILogger<UnloadPluginEndpoint> _logger;\n\n    public UnloadPluginEndpoint(IPluginManager pluginManager, ILogger<UnloadPluginEndpoint> logger)\n    {\n        _pluginManager = pluginManager;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/plugins/unload\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Unload Plugin at Runtime\";\n            s.Description = \"Dynamically unload a plugin by name without restarting the application\";\n            s.Response<PluginManagementResponse>(200, \"Plugin unloaded successfully\");\n            s.Response<PluginManagementResponse>(400, \"Plugin not found or not loaded\");\n            s.Response<PluginManagementResponse>(500, \"Failed to unload plugin\");\n        });\n    }\n\n    public override async Task HandleAsync(UnloadPluginRequest req, CancellationToken ct)\n    {\n        try\n        {\n            _logger.LogInformation(\"Unloading plugin: {PluginName}\", req.PluginName);\n\n            var result = await _pluginManager.UnloadPluginAsync(req.PluginName, ct);\n\n            if (result.IsSuccess)\n            {\n                await SendOkAsync(new PluginManagementResponse\n                {\n                    Success = true,\n                    Message = result.Message ?? \"Plugin unloaded successfully\",\n                    PluginName = req.PluginName\n                }, ct);\n            }\n            else\n            {\n                await SendAsync(new PluginManagementResponse\n                {\n                    Success = false,\n                    Message = result.Message ?? \"Failed to unload plugin\",\n                    PluginName = req.PluginName\n                }, 400, ct);\n            }\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error unloading plugin {PluginName}\", req.PluginName);\n            await SendAsync(new PluginManagementResponse\n            {\n                Success = false,\n                Message = $\"Error unloading plugin: {ex.Message}\",\n                PluginName = req.PluginName\n            }, 500, ct);\n        }\n    }\n}\n\n/// <summary>\n/// Endpoint for loading all plugins from a directory\n/// </summary>\npublic class LoadPluginsFromDirectoryEndpoint : Endpoint<LoadPluginsFromDirectoryRequest, PluginManagementResponse>\n{\n    private readonly IPluginManager _pluginManager;\n    private readonly ILogger<LoadPluginsFromDirectoryEndpoint> _logger;\n\n    public LoadPluginsFromDirectoryEndpoint(IPluginManager pluginManager, ILogger<LoadPluginsFromDirectoryEndpoint> logger)\n    {\n        _pluginManager = pluginManager;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/plugins/load-directory\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Load All Plugins from Directory\";\n            s.Description = \"Load all plugins found in the specified directory\";\n            s.Response<PluginManagementResponse>(200, \"Plugins loaded successfully\");\n            s.Response<PluginManagementResponse>(400, \"Directory not found or invalid\");\n            s.Response<PluginManagementResponse>(500, \"Failed to load plugins\");\n        });\n    }\n\n    public override async Task HandleAsync(LoadPluginsFromDirectoryRequest req, CancellationToken ct)\n    {\n        try\n        {\n            _logger.LogInformation(\"Loading plugins from directory: {Directory}\", req.Directory);\n\n            var result = await _pluginManager.LoadPluginsAsync(req.Directory, ct);\n\n            if (result.IsSuccess)\n            {\n                var manifests = _pluginManager.GetPluginManifests();\n                \n                await SendOkAsync(new PluginManagementResponse\n                {\n                    Success = true,\n                    Message = $\"{result.Message}. Loaded {manifests.Count} plugins.\",\n                }, ct);\n            }\n            else\n            {\n                await SendAsync(new PluginManagementResponse\n                {\n                    Success = false,\n                    Message = result.Message ?? \"Failed to load plugins from directory\"\n                }, 400, ct);\n            }\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error loading plugins from directory {Directory}\", req.Directory);\n            await SendAsync(new PluginManagementResponse\n            {\n                Success = false,\n                Message = $\"Error loading plugins: {ex.Message}\"\n            }, 500, ct);\n        }\n    }\n}\n\n/// <summary>\n/// Endpoint for getting plugin health status\n/// </summary>\npublic class GetPluginHealthEndpoint : EndpointWithoutRequest<List<PluginCore.Interfaces.PluginStatus>>\n{\n    private readonly IPluginManager _pluginManager;\n\n    public GetPluginHealthEndpoint(IPluginManager pluginManager)\n    {\n        _pluginManager = pluginManager;\n    }\n\n    public override void Configure()\n    {\n        Get(\"/api/plugins/health\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Get Plugin Health Status\";\n            s.Description = \"Get detailed health status of all loaded plugins\";\n            s.Response<List<PluginCore.Interfaces.PluginStatus>>(200, \"Plugin health status retrieved successfully\");\n        });\n    }\n\n    public override async Task HandleAsync(CancellationToken ct)\n    {\n        var statuses = await _pluginManager.GetPluginStatusesAsync(ct);\n        await SendOkAsync(statuses.ToList(), ct);\n    }\n}\n"}]}