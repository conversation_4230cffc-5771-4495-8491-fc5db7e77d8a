{"sourceFile": "src/Core/PluginCore/Models/PluginTypes.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751201867749, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751201867749, "name": "Commit-0", "content": "// Location: PluginCore/Models/GatewayModels.cs\r\n\r\nusing System.ComponentModel.DataAnnotations;\r\n\r\nnamespace PluginCore.Models;\r\n\r\n// --- Core Primitives ---\r\n\r\n/// <summary>\r\n/// A universal response for operations that may succeed or fail, providing detailed error information.\r\n/// </summary>\r\npublic record OperationResult(bool IsSuccess, string? Message = null, IEnumerable<ErrorDetail>? Errors = null);\r\n\r\n/// <summary>\r\n/// A structured error detail used in OperationResult.\r\n/// </summary>\r\npublic record ErrorDetail(string Code, string Description, string? Target = null);\r\n\r\n// --- Message-Related Models ---\r\n\r\n/// <summary>\r\n/// Defines the complete payload for sending a single message, used across multiple methods.\r\n/// </summary>\r\npublic record MessagePayload(\r\n    string Recipient,\r\n    string Content,\r\n    string? From = null,\r\n    string? Subject = null, // For email\r\n    Dictionary<string, string>? Headers = null,\r\n    string? CorrelationId = null\r\n);\r\n\r\n/// <summary>\r\n/// The result of a successful message send operation.\r\n/// </summary>\r\npublic record MessageSendResult(string MessageId, DateTimeOffset Timestamp, string Status, string? CorrelationId = null);\r\n\r\n/// <summary>\r\n/// The result of a message scheduling operation.\r\n/// </summary>\r\npublic record MessageScheduleResult(string ScheduledMessageId, DateTimeOffset ScheduledTime, string Status);\r\n\r\n/// <summary>\r\n/// Contains the real-time status of a message.\r\n/// </summary>\r\npublic record MessageStatusInfo(string MessageId, string Status, DateTimeOffset LastUpdated, string? Reason = null);\r\n\r\n/// <summary>\r\n/// Contains a provider-issued delivery receipt.\r\n/// </summary>\r\npublic record DeliveryReceipt(string MessageId, string Status, DateTimeOffset Timestamp, string? ProviderCode = null);\r\n\r\n/// <summary>\r\n/// The response from a raw payload send.\r\n/// </summary>\r\npublic record RawGatewayResponse(bool IsSuccess, int StatusCode, string Body, IEnumerable<ErrorDetail>? Errors = null);\r\n\r\n/// <summary>\r\n/// A message prepared for sending but not yet dispatched, used for previews or dry runs.\r\n/// </summary>\r\npublic record PreparedMessage(object RenderedPayload, Dictionary<string, string> FinalHeaders, ValidationResult Validation);\r\n\r\n\r\n// --- Metrics-Related Models ---\r\n\r\n/// <summary>\r\n/// A comprehensive report on the gateway's current operational status.\r\n/// </summary>\r\npublic record GatewayStatusReport(bool IsHealthy, double CurrentLatencyMs, string Message, Dictionary<string, string> ConnectivityStatus);\r\n\r\n/// <summary>\r\n/// The result of a message delivery report query.\r\n/// </summary>\r\npublic record DeliveryResult(string MessageId, string Status, DateTimeOffset Timestamp, string? FailureReason = null);\r\n\r\n/// <summary>\r\n/// A summary of usage metrics over a specified period.\r\n/// </summary>\r\npublic record UsageMetrics(int MessagesSent, int MessagesFailed, int MessagesQueued, decimal EstimatedCost);\r\n\r\n/// <summary>\r\n/// A single entry in the gateway's error log.\r\n/// </summary>\r\npublic record GatewayErrorEntry(string ErrorId, string ErrorCode, string Description, string Severity, DateTimeOffset Timestamp);\r\n\r\n/// <summary>\r\n/// A snapshot of system throughput.\r\n/// </summary>\r\npublic record PerformanceSnapshot(int MessagesPerMinute, double AverageDeliveryTimeMs, TimeSpan Resolution);\r\n\r\n/// <summary>\r\n/// A report on Service Level Agreement (SLA) compliance.\r\n/// </summary>\r\npublic record SlaReport(double AvailabilityPercentage, double AverageDeliveryTimeMs, int Breaches);\r\n\r\n/// <summary>\r\n/// A breakdown of latencies for different gateway operations.\r\n/// </summary>\r\npublic record LatencyMetrics(double AuthenticationMs, double SendApiCallMs, double DeliveryCallbackMs);\r\n\r\n/// <summary>\r\n/// Data for analyzing traffic trends.\r\n/// </summary>\r\npublic record TrafficTrend(DateTime Period, string Granularity, int TotalMessages, int Delivered, int Failed, double AvgLatencyMs);\r\n\r\n/// <summary>\r\n/// The result of anomaly detection analysis.\r\n/// </summary>\r\npublic record AnomalyDetectionResult(string AnomalyId, DateTime DetectedAt, string Description, string Severity);\r\n\r\n/// <summary>\r\n/// Represents a generated, downloadable report.\r\n/// </summary>\r\npublic record GeneratedReport(string FileName, string MimeType, byte[] Content);\r\n\r\n/// <summary>\r\n/// Information about a single retry attempt.\r\n/// </summary>\r\npublic record RetryAttemptInfo(string MessageId, int RetryCount, string ErrorCode, string Outcome, DateTime AttemptedAt);\r\n\r\n/// <summary>\r\n/// A record of a configuration change and its observed impact.\r\n/// </summary>\r\npublic record ConfigurationImpactRecord(string ChangeId, DateTime ChangedAt, string Description, string ImpactAnalysis);\r\n\r\n\r\n// --- Admin-Related Models ---\r\n// (No specific models needed yet as primitive types and OperationResult suffice)"}]}