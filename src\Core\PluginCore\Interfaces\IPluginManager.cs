using PluginCore.Base;
using PluginCore.Models;

namespace PluginCore.Interfaces;

/// <summary>
/// Interface for managing plugins in the notification service.
/// </summary>
public interface IPluginManager
{
    /// <summary>
    /// Loads all plugins from the specified directory.
    /// </summary>
    Task<OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default);

    /// <summary>
    /// Loads a specific plugin by file path.
    /// </summary>
    Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Loads a plugin by name from the plugin directory.
    /// </summary>
    Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all loaded plugins of a specific type.
    /// </summary>
    IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType;

    /// <summary>
    /// Gets a specific plugin by name and type.
    /// </summary>
    T? GetPlugin<T>(string pluginName) where T : class, IPluginType;

    /// <summary>
    /// Gets all loaded plugin manifests.
    /// </summary>
    IReadOnlyList<PluginManifest> GetPluginManifests();

    /// <summary>
    /// Gets the manifest for a specific plugin.
    /// </summary>
    PluginManifest? GetPluginManifest(string pluginName);

    /// <summary>
    /// Unloads a specific plugin.
    /// </summary>
    Task<OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reloads a specific plugin.
    /// </summary>
    Task<OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the status of all loaded plugins.
    /// </summary>
    Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates a plugin before loading.
    /// </summary>
    Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents the status of a plugin.
/// </summary>
public record PluginStatus(
    string Name,
    string Version,
    string Type,
    bool IsLoaded,
    bool IsHealthy,
    string Status,
    DateTimeOffset LastChecked,
    string? ErrorMessage = null);
