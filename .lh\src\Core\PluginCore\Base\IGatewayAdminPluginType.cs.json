{"sourceFile": "src/Core/PluginCore/Base/IGatewayAdminPluginType.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751203764947, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751204259391, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,9 +15,9 @@\n     /// Sensitive values should be masked by the implementation.\r\n     /// </summary>\r\n     /// <param name=\"cancellationToken\">A token to cancel the operation.</param>\r\n     /// <returns>A list of GatewayConfiguration objects representing the current configuration.</returns>\r\n-    Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(,CancellationToken cancellationToken = default);\r\n+    Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync( ,CancellationToken cancellationToken = default);\r\n \r\n     /// <summary>\r\n     /// Updates one or more configuration values. The plugin should validate\r\n     /// settings before applying them and return a detailed result.\r\n"}, {"date": 1751204269452, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,9 +15,9 @@\n     /// Sensitive values should be masked by the implementation.\r\n     /// </summary>\r\n     /// <param name=\"cancellationToken\">A token to cancel the operation.</param>\r\n     /// <returns>A list of GatewayConfiguration objects representing the current configuration.</returns>\r\n-    Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(JsonO ,CancellationToken cancellationToken = default);\r\n+    Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(JsonOb ,CancellationToken cancellationToken = default);\r\n \r\n     /// <summary>\r\n     /// Updates one or more configuration values. The plugin should validate\r\n     /// settings before applying them and return a detailed result.\r\n"}], "date": 1751203764947, "name": "Commit-0", "content": "using PluginCore.Models;\r\n\r\nnamespace PluginCore.Base;\r\n\r\n/// <summary>\r\n/// Defines administrative and configuration-related functions for a gateway.\r\n/// </summary>\r\n/// <remarks>\r\n/// This interface defines administrative and configuration-related functions for a gateway.\r\n/// </remarks>\r\npublic interface IGatewayAdminPluginType\r\n{\r\n    /// <summary>\r\n    /// Retrieves the current set of configurations for the plugin.\r\n    /// Sensitive values should be masked by the implementation.\r\n    /// </summary>\r\n    /// <param name=\"cancellationToken\">A token to cancel the operation.</param>\r\n    /// <returns>A list of GatewayConfiguration objects representing the current configuration.</returns>\r\n    Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Updates one or more configuration values. The plugin should validate\r\n    /// settings before applying them and return a detailed result.\r\n    /// </summary>\r\n    /// <param name=\"settingsToUpdate\">The list of configuration settings to update.</param>\r\n    /// <param name=\"cancellationToken\">A token to cancel the operation.</param>\r\n    /// <returns>An OperationResult indicating the success of the update.</returns>\r\n    Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Performs a diagnostic test on the gateway's configuration, such as testing\r\n    /// API key validity and connectivity to the provider endpoint.\r\n    /// </summary>\r\n    /// <param name=\"cancellationToken\">A token to cancel the operation.</param>\r\n    /// <returns>An OperationResult indicating the success or failure with detailed error information.</returns>\r\n    Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Returns the manifest describing the plugin’s metadata and capabilities.\r\n    /// </summary>\r\n    /// <Param name=\"cancellationToken\">A token to cancel the operation.</param>\r\n    /// <returns>A PluginManifest object representing the plugin's metadata and capabilities.</returns>\r\n    Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default);\r\n}\r\n"}]}