using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PluginCore.Interfaces;
using PluginCore.Models;

namespace NotifyMaster.Database.Services;

/// <summary>
/// Implementation of role management service using Entity Framework
/// </summary>
public class RoleService : IRoleService
{
    private readonly NotifyMasterDbContext _context;
    private readonly ILogger<RoleService> _logger;

    public RoleService(NotifyMasterDbContext context, ILogger<RoleService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Roles
                .Include(r => r.Permissions).ThenInclude(rp => rp.Permission)
                .Include(r => r.Users).ThenInclude(ur => ur.User)
                .FirstOrDefaultAsync(r => r.Id == roleId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role {RoleId}", roleId);
            return null;
        }
    }

    public async Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Roles
                .Include(r => r.Permissions).ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(r => r.Name == roleName && r.Scope == scope, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role by name {RoleName}", roleName);
            return null;
        }
    }

    public async Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.Roles.AsQueryable();

            if (scope.HasValue)
            {
                query = query.Where(r => r.Scope == scope.Value);
            }

            return await query
                .OrderBy(r => r.Name)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles");
            return Array.Empty<Role>();
        }
    }

    public async Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if role already exists
            var existingRole = await _context.Roles
                .FirstOrDefaultAsync(r => r.Name == request.Name && r.Scope == request.Scope, cancellationToken);

            if (existingRole != null)
            {
                return OperationResult<Role>.Failure("A role with this name already exists in the specified scope");
            }

            var role = new Role
            {
                Id = Guid.NewGuid().ToString(),
                Name = request.Name,
                Description = request.Description,
                Scope = request.Scope,
                IsSystemRole = request.IsSystemRole,
                CreatedAt = DateTime.UtcNow
            };

            _context.Roles.Add(role);

            // Assign permissions if provided
            if (request.PermissionIds.Any())
            {
                var permissions = await _context.Permissions
                    .Where(p => request.PermissionIds.Contains(p.Id))
                    .ToListAsync(cancellationToken);

                foreach (var permission in permissions)
                {
                    _context.RolePermissions.Add(new RolePermission
                    {
                        RoleId = role.Id,
                        PermissionId = permission.Id,
                        AssignedAt = DateTime.UtcNow
                    });
                }
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created role {RoleId} - {RoleName}", role.Id, role.Name);
            return OperationResult<Role>.Success(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role {RoleName}", request.Name);
            return OperationResult<Role>.Failure($"Failed to create role: {ex.Message}");
        }
    }

    public async Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _context.Roles.FindAsync(roleId);
            if (role == null)
            {
                return OperationResult<Role>.Failure("Role not found");
            }

            // Update properties if provided
            if (!string.IsNullOrEmpty(request.Name))
            {
                // Check if new name already exists
                var existingRole = await _context.Roles
                    .FirstOrDefaultAsync(r => r.Name == request.Name && r.Scope == role.Scope && r.Id != roleId, cancellationToken);

                if (existingRole != null)
                {
                    return OperationResult<Role>.Failure("A role with this name already exists");
                }

                role.Name = request.Name;
            }

            if (!string.IsNullOrEmpty(request.Description))
                role.Description = request.Description;

            if (request.Scope.HasValue)
                role.Scope = request.Scope.Value;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated role {RoleId}", roleId);
            return OperationResult<Role>.Success(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role {RoleId}", roleId);
            return OperationResult<Role>.Failure($"Failed to update role: {ex.Message}");
        }
    }

    public async Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _context.Roles.FindAsync(roleId);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            if (role.IsSystemRole)
            {
                return OperationResult.Failure("Cannot delete system roles");
            }

            _context.Roles.Remove(role);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted role {RoleId}", roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role {RoleId}", roleId);
            return OperationResult.Failure($"Failed to delete role: {ex.Message}");
        }
    }

    public async Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _context.Roles.FindAsync(roleId);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return OperationResult.Failure("Permission not found");
            }

            var existingRolePermission = await _context.RolePermissions
                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);

            if (existingRolePermission != null)
            {
                return OperationResult.Failure("Role already has this permission");
            }

            _context.RolePermissions.Add(new RolePermission
            {
                RoleId = roleId,
                PermissionId = permissionId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = assignedBy
            });

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Assigned permission {PermissionId} to role {RoleId}", permissionId, roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission {PermissionId} to role {RoleId}", permissionId, roleId);
            return OperationResult.Failure($"Failed to assign permission: {ex.Message}");
        }
    }

    public async Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var rolePermission = await _context.RolePermissions
                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);

            if (rolePermission == null)
            {
                return OperationResult.Failure("Role does not have this permission");
            }

            _context.RolePermissions.Remove(rolePermission);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Removed permission {PermissionId} from role {RoleId}", permissionId, roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing permission {PermissionId} from role {RoleId}", permissionId, roleId);
            return OperationResult.Failure($"Failed to remove permission: {ex.Message}");
        }
    }

    public async Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.RolePermissions
                .Include(rp => rp.Permission)
                .Where(rp => rp.RoleId == roleId)
                .Select(rp => rp.Permission)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for role {RoleId}", roleId);
            return Array.Empty<Permission>();
        }
    }

    public async Task<IReadOnlyList<User>> GetRoleUsersAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.UserRoles
                .Include(ur => ur.User)
                .Where(ur => ur.RoleId == roleId)
                .Select(ur => ur.User)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for role {RoleId}", roleId);
            return Array.Empty<User>();
        }
    }
}
