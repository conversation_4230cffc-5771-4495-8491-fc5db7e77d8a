using MediatR;
using PluginCore.Interfaces;
using NotifyMasterApi.Features.Plugins.Commands;
using NotifyMasterApi.Models;

namespace NotifyMasterApi.Features.Plugins.Handlers;

/// <summary>
/// Handler for loading plugins
/// </summary>
public class LoadPluginCommandHandler : IRequestHandler<LoadPluginCommand, PluginOperationResult>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<LoadPluginCommandHandler> _logger;

    public LoadPluginCommandHandler(IPluginManager pluginManager, ILogger<LoadPluginCommandHandler> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<PluginOperationResult> Handle(LoadPluginCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Loading plugin: {PluginName}", request.Name);

            PluginCore.Models.OperationResult result;

            if (!string.IsNullOrEmpty(request.Path))
            {
                result = await _pluginManager.LoadPluginAsync(request.Path, cancellationToken);
            }
            else
            {
                result = await _pluginManager.LoadPluginByNameAsync(request.Name, "plugins", cancellationToken);
            }

            var manifest = _pluginManager.GetPluginManifest(request.Name);

            return new PluginOperationResult
            {
                Success = result.IsSuccess,
                Message = result.Message ?? (result.IsSuccess ? "Plugin loaded successfully" : "Failed to load plugin"),
                PluginName = request.Name
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading plugin {PluginName}", request.Name);
            return new PluginOperationResult
            {
                Success = false,
                Message = $"Error loading plugin: {ex.Message}",
                PluginName = request.Name
            };
        }
    }
}

/// <summary>
/// Handler for unloading plugins
/// </summary>
public class UnloadPluginCommandHandler : IRequestHandler<UnloadPluginCommand, PluginOperationResult>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<UnloadPluginCommandHandler> _logger;

    public UnloadPluginCommandHandler(IPluginManager pluginManager, ILogger<UnloadPluginCommandHandler> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<PluginOperationResult> Handle(UnloadPluginCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Unloading plugin: {PluginName}", request.Name);

            var result = await _pluginManager.UnloadPluginAsync(request.Name, cancellationToken);

            return new PluginOperationResult
            {
                Success = result.IsSuccess,
                Message = result.Message ?? (result.IsSuccess ? "Plugin unloaded successfully" : "Failed to unload plugin"),
                PluginName = request.Name
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unloading plugin {PluginName}", request.Name);
            return new PluginOperationResult
            {
                Success = false,
                Message = $"Error unloading plugin: {ex.Message}",
                PluginName = request.Name
            };
        }
    }
}

/// <summary>
/// Handler for loading plugins from directory
/// </summary>
public class LoadDirectoryCommandHandler : IRequestHandler<LoadDirectoryCommand, PluginOperationResult>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<LoadDirectoryCommandHandler> _logger;

    public LoadDirectoryCommandHandler(IPluginManager pluginManager, ILogger<LoadDirectoryCommandHandler> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<PluginOperationResult> Handle(LoadDirectoryCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Loading plugins from directory: {Directory}", request.Directory);

            var result = await _pluginManager.LoadPluginsAsync(request.Directory, cancellationToken);
            var manifests = _pluginManager.GetPluginManifests();

            return new PluginOperationResult
            {
                Success = result.IsSuccess,
                Message = result.IsSuccess 
                    ? $"{result.Message}. Loaded {manifests.Count} plugins."
                    : result.Message ?? "Failed to load plugins from directory"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading plugins from directory {Directory}", request.Directory);
            return new PluginOperationResult
            {
                Success = false,
                Message = $"Error loading plugins: {ex.Message}"
            };
        }
    }
}

/// <summary>
/// Handler for reloading plugins
/// </summary>
public class ReloadPluginCommandHandler : IRequestHandler<ReloadPluginCommand, PluginOperationResult>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<ReloadPluginCommandHandler> _logger;

    public ReloadPluginCommandHandler(IPluginManager pluginManager, ILogger<ReloadPluginCommandHandler> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<PluginOperationResult> Handle(ReloadPluginCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Reloading plugin: {PluginName}", request.Name);

            var result = await _pluginManager.ReloadPluginAsync(request.Name, cancellationToken);

            return new PluginOperationResult
            {
                Success = result.IsSuccess,
                Message = result.Message ?? (result.IsSuccess ? "Plugin reloaded successfully" : "Failed to reload plugin"),
                PluginName = request.Name
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reloading plugin {PluginName}", request.Name);
            return new PluginOperationResult
            {
                Success = false,
                Message = $"Error reloading plugin: {ex.Message}",
                PluginName = request.Name
            };
        }
    }
}
