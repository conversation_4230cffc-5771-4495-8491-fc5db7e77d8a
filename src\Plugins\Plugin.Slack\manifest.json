{"name": "<PERSON><PERSON>ck", "version": "1.0.0", "description": "Slack messaging plugin for sending messages to Slack channels and users", "author": "NotificationService Team", "type": "Messaging", "provider": "<PERSON><PERSON>ck", "assemblyName": "Plugin.Slack.dll", "entryPoint": "Plugin.Slack.SlackPlugin", "isEnabled": true, "priority": 50, "supportedFeatures": ["SendMessage", "ChannelMessaging", "DirectMessaging", "Attachments", "HealthCheck", "AdminConfig", "Metrics"], "configuration": {"webhookUrl": {"type": "string", "description": "Slack Incoming Webhook URL (alternative to bot token)", "isRequired": false, "isSecret": true}, "botToken": {"type": "string", "description": "Slack Bot User OAuth Token (alternative to webhook)", "isRequired": false, "isSecret": true}, "defaultChannel": {"type": "string", "description": "Default channel for messages", "isRequired": false, "isSecret": false, "defaultValue": "#general"}, "username": {"type": "string", "description": "Default bot username", "isRequired": false, "isSecret": false, "defaultValue": "NotificationService"}, "iconEmoji": {"type": "string", "description": "Default bot icon emoji", "isRequired": false, "isSecret": false, "defaultValue": ":robot_face:"}}, "endpoints": {"webhook": "https://hooks.slack.com/services/...", "api": "https://slack.com/api/chat.postMessage", "auth_test": "https://slack.com/api/auth.test"}, "rateLimit": {"requestsPerSecond": 1, "requestsPerMinute": 60, "requestsPerHour": 3600}, "adminFeatures": {"configurationManagement": true, "metricsViewing": true, "healthMonitoring": true, "channelManagement": true}}