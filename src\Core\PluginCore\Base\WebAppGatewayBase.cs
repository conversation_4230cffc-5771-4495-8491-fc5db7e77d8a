using PluginCore.Interfaces;
using PluginCore.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace PluginCore.Base;

/// <summary>
/// Abstract base class for Web Application gateway plugins.
/// Provides common web notification-specific functionality, validation, and helper methods.
/// Implements all three gateway interfaces: Message, Admin, and Metrics.
/// Supports browser notifications, in-app messages, web push, and real-time notifications.
/// </summary>
public abstract class WebAppGatewayBase : IWebAppPlugin, IGatewayAdminPluginType, IGatewayMetricsPluginType
{
    protected readonly ILogger? _logger;
    protected readonly Dictionary<string, object> _configuration = new();

    protected WebAppGatewayBase(ILogger? logger)
    {
        _logger = logger;
    }

    #region IGatewayMessagePluginType Implementation

    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                );
            }

            var normalizedPayload = NormalizeWebAppPayload(payload);
            return await SendWebAppInternalAsync(normalizedPayload, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send web app notification to {Recipient}", payload.Recipient);
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed",
                CorrelationId: payload.CorrelationId
            );
        }
    }

    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<MessageSendResult>();
        var validPayloads = new List<MessagePayload>();

        // Validate all payloads first
        foreach (var payload in payloads)
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (validationResult.IsValid)
            {
                validPayloads.Add(NormalizeWebAppPayload(payload));
            }
            else
            {
                results.Add(new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                ));
            }
        }

        if (validPayloads.Any())
        {
            var bulkResults = await SendBulkWebAppInternalAsync(validPayloads, cancellationToken);
            results.AddRange(bulkResults);
        }

        return results.AsReadOnly();
    }

    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)
    {
        var template = await GetWebAppTemplateAsync(templateId, cancellationToken);
        if (template == null)
        {
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed"
            );
        }

        var title = ProcessWebAppTemplate(template.Title, templateData);
        var content = ProcessWebAppTemplate(template.Content, templateData);
        var actionUrl = template.ActionUrl != null ? ProcessWebAppTemplate(template.ActionUrl, templateData) : null;
        
        var payload = new MessagePayload(
            Recipient: recipient.UserId ?? recipient.Address,
            Content: content,
            Subject: title,
            Headers: new Dictionary<string, string> 
            { 
                ["TemplateId"] = templateId,
                ["NotificationType"] = template.Type ?? "toast",
                ["ActionUrl"] = actionUrl ?? "",
                ["IconUrl"] = template.IconUrl ?? ""
            }
        );

        return await SendMessageAsync(payload, cancellationToken);
    }

    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);

    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);

    public virtual async Task<PluginCore.Models.ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        // Validate recipient (user ID, session ID, or connection ID)
        if (!IsValidWebAppRecipient(payload.Recipient))
        {
            errors.Add("Invalid web app recipient format (should be user ID, session ID, or connection ID)");
        }

        // Validate message content
        if (string.IsNullOrWhiteSpace(payload.Content))
        {
            errors.Add("Web app notification content cannot be empty");
        }
        else if (payload.Content.Length > GetMaxContentLength())
        {
            errors.Add($"Web app notification content exceeds maximum length of {GetMaxContentLength()} characters");
        }

        // Validate title
        if (!string.IsNullOrWhiteSpace(payload.Subject) && payload.Subject.Length > GetMaxTitleLength())
        {
            errors.Add($"Web app notification title exceeds maximum length of {GetMaxTitleLength()} characters");
        }

        // Validate notification type
        var notificationType = payload.Headers?.GetValueOrDefault("NotificationType") ?? "toast";
        if (!IsValidNotificationType(notificationType))
        {
            errors.Add($"Invalid notification type: {notificationType}");
        }

        // Provider-specific validation
        var providerErrors = await ValidateWebAppSpecificAsync(payload, cancellationToken);
        errors.AddRange(providerErrors);

        return new PluginCore.Models.ValidationResult
        {
            IsValid = !errors.Any(),
            ErrorMessage = errors.Any() ? string.Join("; ", errors) : null
        };
    }

    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);

    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);

    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    #endregion

    #region IGatewayAdminPluginType Implementation

    public virtual async Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        var configs = new List<GatewayConfiguration>();
        
        // Add WebApp-specific configuration items
        configs.Add(new GatewayConfiguration("SupportedTypes", "Supported notification types", GetConfiguration<string>("SupportedTypes") ?? "toast,banner,modal,inline", "Comma-separated list of supported notification types", false, false));
        configs.Add(new GatewayConfiguration("MaxTitleLength", "Maximum title length", GetConfiguration<int>("MaxTitleLength").ToString() ?? "100", "Maximum length for notification title", false, false, "int"));
        configs.Add(new GatewayConfiguration("MaxContentLength", "Maximum content length", GetConfiguration<int>("MaxContentLength").ToString() ?? "500", "Maximum length for notification content", false, false, "int"));
        configs.Add(new GatewayConfiguration("EnableRealTime", "Enable real-time notifications", GetConfiguration<bool>("EnableRealTime").ToString() ?? "true", "Enable or disable real-time notification delivery", false, false, "bool"));
        configs.Add(new GatewayConfiguration("EnablePersistence", "Enable notification persistence", GetConfiguration<bool>("EnablePersistence").ToString() ?? "true", "Enable or disable notification persistence", false, false, "bool"));
        configs.Add(new GatewayConfiguration("DefaultIconUrl", "Default notification icon URL", GetConfiguration<string>("DefaultIconUrl") ?? "", "Default icon URL for notifications", false, false));
        
        // Add provider-specific configurations
        var providerConfigs = await GetWebAppProviderConfigurationsAsync(cancellationToken);
        configs.AddRange(providerConfigs);
        
        return configs.AsReadOnly();
    }

    public virtual async Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default)
    {
        try
        {
            foreach (var setting in settingsToUpdate)
            {
                switch (setting.Key)
                {
                    case "SupportedTypes":
                        if (setting.Value != null)
                            SetConfiguration("SupportedTypes", setting.Value);
                        break;
                    case "MaxTitleLength":
                        if (int.TryParse(setting.Value, out var maxTitleLength))
                            SetConfiguration("MaxTitleLength", maxTitleLength);
                        break;
                    case "MaxContentLength":
                        if (int.TryParse(setting.Value, out var maxContentLength))
                            SetConfiguration("MaxContentLength", maxContentLength);
                        break;
                    case "EnableRealTime":
                        if (bool.TryParse(setting.Value, out var enableRealTime))
                            SetConfiguration("EnableRealTime", enableRealTime);
                        break;
                    case "EnablePersistence":
                        if (bool.TryParse(setting.Value, out var enablePersistence))
                            SetConfiguration("EnablePersistence", enablePersistence);
                        break;
                    case "DefaultIconUrl":
                        if (setting.Value != null)
                            SetConfiguration("DefaultIconUrl", setting.Value);
                        break;
                    default:
                        await UpdateWebAppProviderConfigurationAsync(setting, cancellationToken);
                        break;
                }
            }

            return new OperationResult(true, "Configuration updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update WebApp gateway configuration");
            return new OperationResult(false, "Failed to update configuration", new[] { new ErrorDetail("ConfigurationError", ex.Message) });
        }
    }

    public virtual async Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Test basic WebApp functionality
            var isAvailable = await IsAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                return new OperationResult(false, "WebApp gateway is not available");
            }

            // Perform WebApp-specific configuration tests
            var testResult = await TestWebAppConfigurationAsync(cancellationToken);
            return testResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "WebApp configuration test failed");
            return new OperationResult(false, "Configuration test failed", new[] { new ErrorDetail("TestError", ex.Message) });
        }
    }

    public virtual async Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default)
    {
        return await GetWebAppManifestAsync(cancellationToken);
    }

    #endregion

    #region IGatewayMetricsPluginType Implementation

    public virtual async Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default)
    {
        return await GetWebAppStatusReportAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default)
    {
        return await GetWebAppDeliveryReportsAsync(maxItems, cancellationToken);
    }

    public virtual async Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetWebAppUsageMetricsAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetWebAppErrorLogAsync(from, to, cancellationToken);
    }

    public virtual async Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default)
    {
        return await GetWebAppPerformanceSnapshotAsync(resolution, cancellationToken);
    }

    public virtual async Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetWebAppSlaReportAsync(from, to, cancellationToken);
    }

    public virtual async Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetWebAppLatencyMetricsAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = "daily", CancellationToken cancellationToken = default)
    {
        return await GetWebAppTrafficTrendsAsync(granularity, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetWebAppAnomalyReportAsync(from, to, cancellationToken);
    }

    public virtual async Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default)
    {
        return await GenerateWebAppMetricsReportAsync(options, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetWebAppRetryHistoryAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default)
    {
        return await GetWebAppChangeImpactHistoryAsync(cancellationToken);
    }

    #endregion

    #region WebApp-Specific Abstract Methods

    /// <summary>
    /// Sends a single web app notification using the provider's API.
    /// </summary>
    protected abstract Task<MessageSendResult> SendWebAppInternalAsync(MessagePayload payload, CancellationToken cancellationToken);

    /// <summary>
    /// Sends multiple web app notifications in a single operation.
    /// </summary>
    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkWebAppInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);

    /// <summary>
    /// Gets a web app notification template by ID.
    /// </summary>
    protected abstract Task<WebAppTemplate?> GetWebAppTemplateAsync(string templateId, CancellationToken cancellationToken);

    /// <summary>
    /// Performs provider-specific web app notification validation.
    /// </summary>
    protected abstract Task<IEnumerable<string>> ValidateWebAppSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);

    #endregion

    #region WebApp-Specific Abstract Methods for Admin

    /// <summary>
    /// Gets web app provider-specific configuration items.
    /// </summary>
    protected abstract Task<IEnumerable<GatewayConfiguration>> GetWebAppProviderConfigurationsAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Updates web app provider-specific configuration.
    /// </summary>
    protected abstract Task UpdateWebAppProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken);

    /// <summary>
    /// Tests web app provider-specific configuration.
    /// </summary>
    protected abstract Task<OperationResult> TestWebAppConfigurationAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Gets the web app plugin manifest.
    /// </summary>
    protected abstract Task<PluginManifest> GetWebAppManifestAsync(CancellationToken cancellationToken);

    #endregion

    #region WebApp-Specific Abstract Methods for Metrics

    /// <summary>
    /// Gets web app-specific status report.
    /// </summary>
    protected abstract Task<GatewayStatusReport> GetWebAppStatusReportAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app delivery reports.
    /// </summary>
    protected abstract Task<IReadOnlyList<DeliveryResult>> GetWebAppDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app usage metrics.
    /// </summary>
    protected abstract Task<UsageMetrics> GetWebAppUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app error logs.
    /// </summary>
    protected abstract Task<IReadOnlyList<GatewayErrorEntry>> GetWebAppErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app performance snapshot.
    /// </summary>
    protected abstract Task<PerformanceSnapshot> GetWebAppPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app SLA report.
    /// </summary>
    protected abstract Task<SlaReport> GetWebAppSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app latency metrics.
    /// </summary>
    protected abstract Task<LatencyMetrics> GetWebAppLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app traffic trends.
    /// </summary>
    protected abstract Task<IReadOnlyList<TrafficTrend>> GetWebAppTrafficTrendsAsync(string granularity, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app anomaly report.
    /// </summary>
    protected abstract Task<IReadOnlyList<AnomalyDetectionResult>> GetWebAppAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Generates web app metrics report.
    /// </summary>
    protected abstract Task<GeneratedReport> GenerateWebAppMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app retry history.
    /// </summary>
    protected abstract Task<IReadOnlyList<RetryAttemptInfo>> GetWebAppRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets web app configuration change impact history.
    /// </summary>
    protected abstract Task<IReadOnlyList<ConfigurationImpactRecord>> GetWebAppChangeImpactHistoryAsync(CancellationToken cancellationToken);

    #endregion

    #region WebApp-Specific Helper Methods

    /// <summary>
    /// Validates web app recipient format (user ID, session ID, or connection ID).
    /// </summary>
    protected virtual bool IsValidWebAppRecipient(string recipient)
    {
        if (string.IsNullOrWhiteSpace(recipient))
            return false;

        // Basic validation: should be a reasonable length and contain valid characters
        return recipient.Length >= 3 && recipient.Length <= 256 &&
               recipient.All(c => char.IsLetterOrDigit(c) || c == '-' || c == '_' || c == '@' || c == '.');
    }

    /// <summary>
    /// Validates notification type for web app notifications.
    /// </summary>
    protected virtual bool IsValidNotificationType(string notificationType)
    {
        var supportedTypes = GetSupportedNotificationTypes();
        return supportedTypes.Contains(notificationType.ToLowerInvariant());
    }

    /// <summary>
    /// Gets the maximum title length for web app notifications (default 100 characters).
    /// </summary>
    protected virtual int GetMaxTitleLength() => GetConfiguration<int>("MaxTitleLength") > 0 ? GetConfiguration<int>("MaxTitleLength") : 100;

    /// <summary>
    /// Gets the maximum content length for web app notifications (default 500 characters).
    /// </summary>
    protected virtual int GetMaxContentLength() => GetConfiguration<int>("MaxContentLength") > 0 ? GetConfiguration<int>("MaxContentLength") : 500;

    /// <summary>
    /// Normalizes web app payload for consistent processing.
    /// </summary>
    protected virtual MessagePayload NormalizeWebAppPayload(MessagePayload payload)
    {
        var normalizedRecipient = payload.Recipient.Trim();
        var normalizedTitle = payload.Subject?.Trim() ?? "";
        var normalizedContent = payload.Content.Trim();

        return payload with
        {
            Recipient = normalizedRecipient,
            Subject = normalizedTitle,
            Content = normalizedContent
        };
    }

    /// <summary>
    /// Processes web app template with data substitution.
    /// </summary>
    protected virtual string ProcessWebAppTemplate(string template, IDictionary<string, string> templateData)
    {
        var result = template;
        foreach (var kvp in templateData)
        {
            result = result.Replace($"{{{kvp.Key}}}", kvp.Value);
        }
        return result;
    }

    /// <summary>
    /// Gets supported notification types for web app notifications.
    /// </summary>
    protected virtual string[] GetSupportedNotificationTypes()
    {
        var types = GetConfiguration<string>("SupportedTypes") ?? "toast,banner,modal,inline";
        return types.Split(',', StringSplitOptions.RemoveEmptyEntries)
                   .Select(t => t.Trim().ToLowerInvariant())
                   .ToArray();
    }

    /// <summary>
    /// Checks if real-time notifications are enabled.
    /// </summary>
    protected virtual bool IsRealTimeEnabled()
    {
        return GetConfiguration<bool>("EnableRealTime");
    }

    /// <summary>
    /// Checks if notification persistence is enabled.
    /// </summary>
    protected virtual bool IsPersistenceEnabled()
    {
        return GetConfiguration<bool>("EnablePersistence");
    }

    /// <summary>
    /// Gets the default icon URL for notifications.
    /// </summary>
    protected virtual string? GetDefaultIconUrl()
    {
        return GetConfiguration<string>("DefaultIconUrl");
    }

    /// <summary>
    /// Sets configuration value.
    /// </summary>
    protected virtual void SetConfiguration(string key, object value)
    {
        _configuration[key] = value;
    }

    /// <summary>
    /// Gets configuration value.
    /// </summary>
    protected virtual T? GetConfiguration<T>(string key)
    {
        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    #endregion
}

/// <summary>
/// Represents a web app notification template with title, content, type, action URL, and icon.
/// </summary>
public record WebAppTemplate(string Title, string Content, string? Type = null, string? ActionUrl = null, string? IconUrl = null);
