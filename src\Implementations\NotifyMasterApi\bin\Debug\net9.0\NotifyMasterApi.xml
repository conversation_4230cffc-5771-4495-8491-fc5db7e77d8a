<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NotifyMasterApi</name>
    </assembly>
    <members>
        <member name="T:NotifyMasterApi.Services.InMemoryDatabaseService">
            <summary>
            In-memory database service for fallback when main database is not available
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.DatabaseFallbackExtensions">
            <summary>
            Extension methods for database fallback configuration
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.DatabaseFallbackActivationService">
            <summary>
            Service to activate database fallback on startup
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.PluginAutoLoaderService">
            <summary>
            Background service that automatically loads plugins at startup
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.PluginLoadContext">
            <summary>
            Custom AssemblyLoadContext for loading plugins in isolation
            Allows for proper unloading of plugin assemblies
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RuntimePluginManager">
            <summary>
            Runtime plugin manager that loads/unloads plugins dynamically without concrete references
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.SendNotificationAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            Send notification through a specific plugin using runtime invocation
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.GetPluginsHealthStatusAsync">
            <summary>
            Get health status of all loaded plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.GetPluginsByTypeAsync(System.String)">
            <summary>
            Get plugins by type (SMS, Email, Push)
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.InvokePluginMethodAsync(System.String,System.String,System.Object[])">
            <summary>
            Invoke any method on a plugin using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginManager.LoadAllPluginsAsync">
            <summary>
            Load all plugins from the plugins directory
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.PluginManifest">
            <summary>
            Plugin manifest structure for runtime loading
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RedisConnectionService">
            <summary>
            Redis connection service with fallback to in-memory storage
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RedisConnectionServiceExtensions">
            <summary>
            Extension methods for Redis connection service registration
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.IQueueService">
            <summary>
            Redis-based queue service for handling notification messages
            Replaces RabbitMQ functionality with Redis pub/sub and lists
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.PublishAsync``1(System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Publish a message to a Redis list (queue)
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.ConsumeAsync``1(System.String,System.Threading.CancellationToken)">
            <summary>
            Consume a single message from a Redis list (queue)
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.ConsumeBatchAsync``1(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Consume multiple messages from a Redis list (queue) in batch
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.GetQueueLengthAsync(System.String)">
            <summary>
            Get the current length of a queue
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.DeleteQueueAsync(System.String)">
            <summary>
            Delete a queue and all its messages
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.SubscribeAsync``1(System.String,System.Func{``0,System.Threading.Tasks.Task},System.Threading.CancellationToken)">
            <summary>
            Subscribe to a Redis pub/sub channel for real-time events
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RedisQueueService.PublishEventAsync``1(System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Publish an event to a Redis pub/sub channel
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.QueueNames">
            <summary>
            Queue names for different notification types
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.ChannelNames">
            <summary>
            Channel names for real-time events
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.QueuedNotification">
            <summary>
            Message models for queue operations
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.QueuedNotification.#ctor(System.String,System.String,System.Object,System.DateTime,System.Int32,System.Nullable{System.DateTime})">
            <summary>
            Message models for queue operations
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RuntimeNotificationService">
            <summary>
            Service that routes notifications to runtime-loaded plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.SendSmsAsync(SmsContract.Models.SendSmsRequest,System.String)">
            <summary>
            Send SMS using runtime-loaded SMS plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.SendEmailAsync(EmailContract.Models.SendEmailRequest,System.String)">
            <summary>
            Send Email using runtime-loaded Email plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.SendPushAsync(PushNotificationContract.Models.SendPushMessageRequest,System.String)">
            <summary>
            Send Push notification using runtime-loaded Push plugins
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.GetAvailableProvidersAsync(System.String)">
            <summary>
            Get available providers for a specific notification type
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.GetPluginHealthStatusAsync">
            <summary>
            Get plugin health status
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimeNotificationService.InvokePluginMethodAsync(System.String,System.String,System.Object[])">
            <summary>
            Invoke a specific method on a plugin
            </summary>
        </member>
        <member name="T:NotifyMasterApi.Services.RuntimePluginInstance">
            <summary>
            Represents a runtime-loaded plugin instance with no concrete type references
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.InitializeAsync(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Initialize the plugin using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.ValidateConfigurationAsync(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Validate plugin configuration using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.SendNotificationAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            Send notification using reflection (works for any notification type)
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.HealthCheckAsync">
            <summary>
            Perform health check using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.GetPluginInfo">
            <summary>
            Get plugin information using reflection
            </summary>
        </member>
        <member name="M:NotifyMasterApi.Services.RuntimePluginInstance.InvokeMethodAsync(System.String,System.Object[])">
            <summary>
            Invoke any method on the plugin using reflection
            </summary>
        </member>
    </members>
</doc>
