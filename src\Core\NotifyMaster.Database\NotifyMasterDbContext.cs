using Microsoft.EntityFrameworkCore;
using PluginCore.Models;

namespace NotifyMaster.Database;

/// <summary>
/// Main database context for NotifyMaster with multi-tenancy support
/// </summary>
public class NotifyMasterDbContext : DbContext
{
    public NotifyMasterDbContext(DbContextOptions<NotifyMasterDbContext> options) : base(options)
    {
    }

    // Tenant and User Management
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<UserPermission> UserPermissions { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }
    public DbSet<TenantPlugin> TenantPlugins { get; set; }

    // Notification Management
    public DbSet<NotificationLog> NotificationLogs { get; set; }
    public DbSet<MessageTemplate> MessageTemplates { get; set; }
    public DbSet<ScheduledMessage> ScheduledMessages { get; set; }
    public DbSet<WebhookEndpoint> WebhookEndpoints { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure tenant entity
        ConfigureTenant(modelBuilder);
        
        // Configure user management entities
        ConfigureUserManagement(modelBuilder);
        
        // Configure notification entities
        ConfigureNotificationEntities(modelBuilder);
        
        // Seed default data
        SeedDefaultData(modelBuilder);
    }

    private void ConfigureTenant(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.HasKey(t => t.Id);
            entity.Property(t => t.Name).IsRequired().HasMaxLength(100);
            entity.Property(t => t.Domain).IsRequired().HasMaxLength(100);
            entity.HasIndex(t => t.Domain).IsUnique();
            entity.Property(t => t.Description).HasMaxLength(500);
            
            // Configure complex properties as JSON
            entity.OwnsOne(t => t.Settings, settings =>
            {
                settings.ToJson();
            });
            
            entity.OwnsOne(t => t.Limits, limits =>
            {
                limits.ToJson();
            });
            
            entity.OwnsOne(t => t.Usage, usage =>
            {
                usage.ToJson();
            });
        });

        modelBuilder.Entity<TenantPlugin>(entity =>
        {
            entity.HasKey(tp => tp.Id);
            entity.Property(tp => tp.TenantId).IsRequired();
            entity.Property(tp => tp.PluginName).IsRequired().HasMaxLength(100);
            
            entity.HasOne(tp => tp.Tenant)
                  .WithMany(t => t.Plugins)
                  .HasForeignKey(tp => tp.TenantId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.OwnsOne(tp => tp.Configuration, config =>
            {
                config.ToJson();
            });
            
            entity.HasIndex(tp => new { tp.TenantId, tp.PluginName }).IsUnique();
        });
    }

    private void ConfigureUserManagement(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(u => u.Id);
            entity.Property(u => u.TenantId).IsRequired();
            entity.Property(u => u.Username).IsRequired().HasMaxLength(100);
            entity.Property(u => u.Email).IsRequired().HasMaxLength(255);
            entity.Property(u => u.PasswordHash).IsRequired();
            entity.Property(u => u.FirstName).HasMaxLength(100);
            entity.Property(u => u.LastName).HasMaxLength(100);
            
            entity.HasOne(u => u.Tenant)
                  .WithMany(t => t.Users)
                  .HasForeignKey(u => u.TenantId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasIndex(u => new { u.TenantId, u.Email }).IsUnique();
            entity.HasIndex(u => new { u.TenantId, u.Username }).IsUnique();
            
            entity.OwnsOne(u => u.Profile, profile =>
            {
                profile.ToJson();
            });
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(r => r.Id);
            entity.Property(r => r.Name).IsRequired().HasMaxLength(100);
            entity.Property(r => r.Description).HasMaxLength(500);
            entity.HasIndex(r => new { r.Name, r.Scope }).IsUnique();
        });

        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(p => p.Id);
            entity.Property(p => p.Name).IsRequired().HasMaxLength(100);
            entity.Property(p => p.Description).HasMaxLength(500);
            entity.Property(p => p.Resource).IsRequired().HasMaxLength(50);
            entity.Property(p => p.Action).IsRequired().HasMaxLength(50);
            entity.HasIndex(p => new { p.Resource, p.Action }).IsUnique();
        });

        // Configure many-to-many relationships
        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(ur => new { ur.UserId, ur.RoleId });
            
            entity.HasOne(ur => ur.User)
                  .WithMany(u => u.Roles)
                  .HasForeignKey(ur => ur.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasOne(ur => ur.Role)
                  .WithMany(r => r.Users)
                  .HasForeignKey(ur => ur.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<UserPermission>(entity =>
        {
            entity.HasKey(up => new { up.UserId, up.PermissionId });
            
            entity.HasOne(up => up.User)
                  .WithMany(u => u.Permissions)
                  .HasForeignKey(up => up.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasOne(up => up.Permission)
                  .WithMany(p => p.Users)
                  .HasForeignKey(up => up.PermissionId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<RolePermission>(entity =>
        {
            entity.HasKey(rp => new { rp.RoleId, rp.PermissionId });
            
            entity.HasOne(rp => rp.Role)
                  .WithMany(r => r.Permissions)
                  .HasForeignKey(rp => rp.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasOne(rp => rp.Permission)
                  .WithMany(p => p.Roles)
                  .HasForeignKey(rp => rp.PermissionId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureNotificationEntities(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<NotificationLog>(entity =>
        {
            entity.HasKey(n => n.Id);
            entity.Property(n => n.TenantId).IsRequired();
            entity.Property(n => n.MessageId).IsRequired().HasMaxLength(100);
            entity.Property(n => n.Channel).IsRequired().HasMaxLength(50);
            entity.Property(n => n.Recipient).IsRequired().HasMaxLength(255);
            entity.Property(n => n.Subject).HasMaxLength(500);
            entity.Property(n => n.Provider).HasMaxLength(100);
            entity.Property(n => n.CorrelationId).HasMaxLength(50);
            entity.Property(n => n.UserId).HasMaxLength(100);
            
            entity.HasIndex(n => n.TenantId);
            entity.HasIndex(n => n.MessageId);
            entity.HasIndex(n => new { n.TenantId, n.Status });
            
            entity.OwnsOne(n => n.Metadata, metadata =>
            {
                metadata.ToJson();
            });
        });

        // Add other notification entities as needed
        modelBuilder.Entity<MessageTemplate>(entity =>
        {
            entity.HasKey(mt => mt.Id);
            entity.Property(mt => mt.TenantId).IsRequired();
            entity.Property(mt => mt.Name).IsRequired().HasMaxLength(100);
            entity.Property(mt => mt.Subject).HasMaxLength(500);
            entity.Property(mt => mt.Content).IsRequired();
            
            entity.HasIndex(mt => new { mt.TenantId, mt.Name }).IsUnique();
        });

        modelBuilder.Entity<ScheduledMessage>(entity =>
        {
            entity.HasKey(sm => sm.Id);
            entity.Property(sm => sm.TenantId).IsRequired();
            entity.Property(sm => sm.MessageId).IsRequired().HasMaxLength(100);
            entity.Property(sm => sm.Channel).IsRequired().HasMaxLength(50);
            entity.Property(sm => sm.Recipient).IsRequired().HasMaxLength(255);
            
            entity.HasIndex(sm => sm.TenantId);
            entity.HasIndex(sm => new { sm.TenantId, sm.ScheduledTime });
        });

        modelBuilder.Entity<WebhookEndpoint>(entity =>
        {
            entity.HasKey(we => we.Id);
            entity.Property(we => we.TenantId).IsRequired();
            entity.Property(we => we.Name).IsRequired().HasMaxLength(100);
            entity.Property(we => we.Url).IsRequired().HasMaxLength(500);
            entity.Property(we => we.Secret).HasMaxLength(100);
            
            entity.HasIndex(we => new { we.TenantId, we.Name }).IsUnique();
        });
    }

    private void SeedDefaultData(ModelBuilder modelBuilder)
    {
        // Seed default permissions
        var permissions = SystemPermissions.GetDefaultPermissions();
        var permissionEntities = permissions.Select((p, index) => new Permission
        {
            Id = Guid.NewGuid().ToString(),
            Name = p.Name,
            Description = p.Description,
            Resource = p.Resource,
            Action = p.Action,
            IsSystemPermission = true
        }).ToArray();

        modelBuilder.Entity<Permission>().HasData(permissionEntities);

        // Seed default roles
        var roles = SystemRoles.GetDefaultRoles();
        var roleEntities = roles.Select(r => new Role
        {
            Id = Guid.NewGuid().ToString(),
            Name = r.Name,
            Description = r.Description,
            Scope = r.Scope,
            IsSystemRole = true,
            CreatedAt = DateTime.UtcNow
        }).ToArray();

        modelBuilder.Entity<Role>().HasData(roleEntities);

        // Seed role permissions
        var rolePermissions = new List<RolePermission>();
        foreach (var role in roles)
        {
            var roleEntity = roleEntities.First(r => r.Name == role.Name);
            foreach (var permissionName in role.Permissions)
            {
                var permissionEntity = permissionEntities.FirstOrDefault(p => p.Name == permissionName);
                if (permissionEntity != null)
                {
                    rolePermissions.Add(new RolePermission
                    {
                        RoleId = roleEntity.Id,
                        PermissionId = permissionEntity.Id,
                        AssignedAt = DateTime.UtcNow
                    });
                }
            }
        }

        modelBuilder.Entity<RolePermission>().HasData(rolePermissions);
    }
}

/// <summary>
/// Additional entities for notification management
/// </summary>
public class NotificationLog
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string MessageId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string Recipient { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string Content { get; set; } = string.Empty;
    public NotificationStatus Status { get; set; }
    public string? Provider { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? FailedAt { get; set; }
    public int RetryCount { get; set; } = 0;
    public DateTime? LastRetryAt { get; set; }
    public string? CorrelationId { get; set; }
    public string? UserId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class MessageTemplate
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string Content { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
}

public class ScheduledMessage
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string MessageId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string Recipient { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string Content { get; set; } = string.Empty;
    public DateTime ScheduledTime { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public bool IsProcessed { get; set; } = false;
    public DateTime? ProcessedAt { get; set; }
}

public class WebhookEndpoint
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string? Secret { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}

public enum NotificationStatus
{
    Pending = 0,
    Processing = 1,
    Sent = 2,
    Delivered = 3,
    Failed = 4,
    Cancelled = 5,
    Retrying = 6
}
