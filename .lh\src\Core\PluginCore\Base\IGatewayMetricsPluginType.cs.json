{"sourceFile": "src/Core/PluginCore/Base/IGatewayMetricsPluginType.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 14, "patches": [{"date": 1751197258735, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751198910086, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n using PluginCore.Models;\r\n \r\n namespace PluginCore.Base;\r\n \r\n-public interface IGatewayMetricsPluginType\r\n+public interface IGatewayMetricsPluginType : IPluginType\r\n {\r\n     /// <summary>\r\n     /// Gets the current overall status of the gateway, including health, connectivity, and latency.\r\n     /// </summary>\r\n"}, {"date": 1751198917043, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,6 @@\n using PluginCore.Models;\r\n+using NotificationService.Core.PluginCore.Base;\r\n \r\n namespace PluginCore.Base;\r\n \r\n public interface IGatewayMetricsPluginType : IPluginType\r\n"}, {"date": 1751199273031, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -63,5 +63,9 @@\n     /// <summary>\r\n     /// Returns historical configuration change events and their impact on delivery or performance.\r\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n+}\r\n+\r\n+public class GatewayStatusReport\r\n+{\r\n }\n\\ No newline at end of file\n"}, {"date": 1751199291927, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,7 +65,11 @@\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n }\r\n \r\n+public class DeliveryResult\r\n+{\r\n+}\r\n+\r\n public class GatewayStatusReport\r\n {\r\n }\n\\ No newline at end of file\n"}, {"date": 1751199299582, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,8 +65,12 @@\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n }\r\n \r\n+public class UsageMetrics\r\n+{\r\n+}\r\n+\r\n public class DeliveryResult\r\n {\r\n }\r\n \r\n"}, {"date": 1751199306052, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,8 +65,12 @@\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n }\r\n \r\n+public class GatewayErrorEntry\r\n+{\r\n+}\r\n+\r\n public class UsageMetrics\r\n {\r\n }\r\n \r\n"}, {"date": 1751199311732, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,8 +65,12 @@\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n }\r\n \r\n+public class SlaReport\r\n+{\r\n+}\r\n+\r\n public class GatewayErrorEntry\r\n {\r\n }\r\n \r\n"}, {"date": 1751199319501, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,8 +65,16 @@\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n }\r\n \r\n+public class LatencyMetrics\r\n+{\r\n+}\r\n+\r\n+public class PerformanceSnapshot\r\n+{\r\n+}\r\n+\r\n public class SlaReport\r\n {\r\n }\r\n \r\n"}, {"date": 1751199332334, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,8 +65,16 @@\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n }\r\n \r\n+public class AnomalyDetectionResult\r\n+{\r\n+}\r\n+\r\n+public class TrafficTrend\r\n+{\r\n+}\r\n+\r\n public class LatencyMetrics\r\n {\r\n }\r\n \r\n"}, {"date": 1751199340624, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,8 +65,16 @@\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n }\r\n \r\n+public class MetricsReportOptions\r\n+{\r\n+}\r\n+\r\n+public class GeneratedReport\r\n+{\r\n+}\r\n+\r\n public class AnomalyDetectionResult\r\n {\r\n }\r\n \r\n"}, {"date": 1751199349426, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,8 +65,16 @@\n     /// </summary>\r\n     Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n }\r\n \r\n+public class ConfigurationImpactRecord\r\n+{\r\n+}\r\n+\r\n+public class RetryAttemptInfo\r\n+{\r\n+}\r\n+\r\n public class MetricsReportOptions\r\n {\r\n }\r\n \r\n"}, {"date": 1751199359161, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -67,8 +67,15 @@\n }\r\n \r\n public class ConfigurationImpactRecord\r\n {\r\n+    public string ChangeId { get; set; } = string.Empty;\r\n+    public string ChangeType { get; set; } = string.Empty;\r\n+    public string ChangeDescription { get; set; } = string.Empty;\r\n+    public DateTime ChangeTimestamp { get; set; }\r\n+    public string ImpactType { get; set; } = string.Empty;\r\n+    public string ImpactDescription { get; set; } = string.Empty;\r\n+    \r\n }\r\n \r\n public class RetryAttemptInfo\r\n {\r\n"}, {"date": 1751199366250, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,8 +74,9 @@\n     public DateTime ChangeTimestamp { get; set; }\r\n     public string ImpactType { get; set; } = string.Empty;\r\n     public string ImpactDescription { get; set; } = string.Empty;\r\n     public DateTime ImpactTimestamp { get; set; }\r\n+    public string? AdditionalData { get; set; }\r\n     \r\n }\r\n \r\n public class RetryAttemptInfo\r\n"}, {"date": 1751199401539, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -90,8 +90,9 @@\n     public string? FollowUpResolutionNotes { get; set; }\r\n     public string? FollowUpResolutionRemediation { get; set; }\r\n     public string? FollowUpResolutionAdditionalData { get; set; }\r\n     public string? FollowUpResolutionImpact { get; set; }\r\n+    public DateTime? FollowUpResolutionImpactTimestamp { get; set; }\r\n     \r\n }\r\n \r\n public class RetryAttemptInfo\r\n"}], "date": 1751197258735, "name": "Commit-0", "content": "using PluginCore.Models;\r\n\r\nnamespace PluginCore.Base;\r\n\r\npublic interface IGatewayMetricsPluginType\r\n{\r\n    /// <summary>\r\n    /// Gets the current overall status of the gateway, including health, connectivity, and latency.\r\n    /// </summary>\r\n    Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves a list of recent message delivery results, including timestamps, delivery status, and failure reasons.\r\n    /// </summary>\r\n    Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Gets usage metrics such as messages sent, failed, queued, throttled, and cost estimates between two dates.\r\n    /// </summary>\r\n    Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves detailed error logs including error codes, descriptions, severity, and timestamps.\r\n    /// </summary>\r\n    Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves throughput data (messages per interval) to monitor system load and capacity.\r\n    /// </summary>\r\n    Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Provides SLA (Service Level Agreement) statistics, such as average delivery time and availability percentage.\r\n    /// </summary>\r\n    Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves latency breakdowns for various gateway operations (auth, send, delivery callback, etc.).\r\n    /// </summary>\r\n    Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Gets daily or hourly trend reports for traffic analysis and forecasting.\r\n    /// </summary>\r\n    Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = \"daily\", CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Lists recent anomalies or spikes in usage, errors, or latency with detected patterns.\r\n    /// </summary>\r\n    Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Generates and returns a downloadable metrics report in PDF, CSV, or JSON.\r\n    /// </summary>\r\n    Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Retrieves a list of gateway-side retry attempts, categorized by error type and retry outcome.\r\n    /// </summary>\r\n    Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Returns historical configuration change events and their impact on delivery or performance.\r\n    /// </summary>\r\n    Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n}"}]}