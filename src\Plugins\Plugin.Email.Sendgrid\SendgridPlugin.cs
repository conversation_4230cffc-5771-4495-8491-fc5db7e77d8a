using Microsoft.Extensions.Logging;
using PluginCore.Interfaces;
using PluginCore.Models;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace Plugin.Email.Sendgrid;

public class SendgridPlugin : IEmailPlugin
{
    private readonly ILogger<SendgridPlugin> _logger;
    private Dictionary<string, PluginConfigurationItem> _configuration = new();
    private ISendGridClient? _sendGridClient;
    private bool _isInitialized = false;

    public SendgridPlugin(ILogger<SendgridPlugin> logger)
    {
        _logger = logger;
    }

    public string Name => "SendGrid Email Plugin";
    public string Version => "1.0.0";
    public string Provider => "Sendgrid";

    public async Task InitializeAsync(Dictionary<string, PluginConfigurationItem> configuration)
    {
        _configuration = configuration;
        
        var apiKey = GetConfigValue<string>("apiKey");
        if (string.IsNullOrEmpty(apiKey))
        {
            throw new InvalidOperationException("SendGrid API Key is required");
        }

        var fromEmail = GetConfigValue<string>("fromEmail");
        if (string.IsNullOrEmpty(fromEmail))
        {
            throw new InvalidOperationException("From email address is required");
        }

        _sendGridClient = new SendGridClient(apiKey);
        
        _isInitialized = true;
        _logger.LogInformation("SendGrid plugin initialized successfully");
    }

    public async Task<EmailResponse> SendAsync(EmailMessageRequest request)
    {
        if (!_isInitialized || _sendGridClient == null)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var from = new EmailAddress(
                GetConfigValue<string>("fromEmail"), 
                GetConfigValue<string>("fromName") ?? GetConfigValue<string>("fromEmail")
            );
            
            var to = new EmailAddress(request.ToEmail, request.ToName);
            var subject = request.Subject;
            var plainTextContent = request.Body;
            var htmlContent = request.HtmlBody ?? request.Body;

            var msg = MailHelper.CreateSingleEmail(from, to, subject, plainTextContent, htmlContent);

            // Add CC recipients
            if (request.CcEmails?.Any() == true)
            {
                foreach (var cc in request.CcEmails)
                {
                    msg.AddCc(new EmailAddress(cc));
                }
            }

            // Add BCC recipients
            if (request.BccEmails?.Any() == true)
            {
                foreach (var bcc in request.BccEmails)
                {
                    msg.AddBcc(new EmailAddress(bcc));
                }
            }

            // Add attachments
            if (request.Attachments?.Any() == true)
            {
                foreach (var attachment in request.Attachments)
                {
                    msg.AddAttachment(attachment.FileName, Convert.ToBase64String(attachment.Content), attachment.ContentType);
                }
            }

            // Configure tracking
            var enableClickTracking = GetConfigValue<bool>("enableClickTracking", true);
            var enableOpenTracking = GetConfigValue<bool>("enableOpenTracking", true);
            
            msg.SetClickTracking(enableClickTracking, enableClickTracking);
            msg.SetOpenTracking(enableOpenTracking);

            var response = await _sendGridClient.SendEmailAsync(msg);

            if (response.IsSuccessStatusCode)
            {
                var messageId = response.Headers.GetValues("X-Message-Id").FirstOrDefault() ?? Guid.NewGuid().ToString();
                _logger.LogInformation("Email sent via SendGrid. MessageId: {MessageId}", messageId);
                return new EmailResponse(true, MessageId: messageId);
            }
            else
            {
                var errorBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("SendGrid API error: {StatusCode} - {Error}", response.StatusCode, errorBody);
                return new EmailResponse(false, ErrorMessage: $"SendGrid API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email via SendGrid");
            return new EmailResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkEmailResponse> SendBulkAsync(BulkEmailRequest request)
    {
        var results = new List<EmailResponse>();
        
        foreach (var email in request.Emails)
        {
            var result = await SendAsync(email);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        return new BulkEmailResponse(
            successCount == results.Count,
            SuccessCount: successCount,
            FailureCount: results.Count - successCount,
            Results: results
        );
    }

    public async Task<EmailResponse> SendTemplateEmailAsync(TemplateEmailRequest request)
    {
        if (!_isInitialized || _sendGridClient == null)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var from = new EmailAddress(
                GetConfigValue<string>("fromEmail"), 
                GetConfigValue<string>("fromName") ?? GetConfigValue<string>("fromEmail")
            );
            
            var to = new EmailAddress(request.ToEmail, request.ToName);

            var msg = new SendGridMessage()
            {
                From = from,
                TemplateId = request.TemplateId ?? GetConfigValue<string>("templateId")
            };

            msg.AddTo(to);

            // Add template data
            if (request.TemplateData?.Any() == true)
            {
                msg.SetTemplateData(request.TemplateData);
            }

            // Add CC recipients
            if (request.CcEmails?.Any() == true)
            {
                foreach (var cc in request.CcEmails)
                {
                    msg.AddCc(new EmailAddress(cc));
                }
            }

            // Add BCC recipients
            if (request.BccEmails?.Any() == true)
            {
                foreach (var bcc in request.BccEmails)
                {
                    msg.AddBcc(new EmailAddress(bcc));
                }
            }

            var response = await _sendGridClient.SendEmailAsync(msg);

            if (response.IsSuccessStatusCode)
            {
                var messageId = response.Headers.GetValues("X-Message-Id").FirstOrDefault() ?? Guid.NewGuid().ToString();
                return new EmailResponse(true, MessageId: messageId);
            }
            else
            {
                var errorBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("SendGrid template email error: {StatusCode} - {Error}", response.StatusCode, errorBody);
                return new EmailResponse(false, ErrorMessage: $"SendGrid API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending template email via SendGrid");
            return new EmailResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<EmailTrackingResponse> GetEmailTrackingAsync(string messageId)
    {
        // SendGrid tracking would require webhook setup or Stats API
        return new EmailTrackingResponse(false, ErrorMessage: "Email tracking requires webhook configuration");
    }

    public async Task<EmailStatisticsResponse> GetEmailStatisticsAsync(DateTime startDate, DateTime endDate)
    {
        if (!_isInitialized || _sendGridClient == null)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            // This would require SendGrid Stats API implementation
            // For now, return a placeholder response
            return new EmailStatisticsResponse(false, ErrorMessage: "Email statistics not implemented yet");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email statistics from SendGrid");
            return new EmailStatisticsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        // SendGrid doesn't provide direct message status API without webhooks
        return new MessageStatusResponse(false, ErrorMessage: "Message status requires webhook configuration");
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string emailAddress)
    {
        // SendGrid doesn't provide message history API
        return new MessageHistoryResponse(false, ErrorMessage: "Message history not available via SendGrid API");
    }

    public async Task<EmailResponse> ResendMessageAsync(string messageId)
    {
        // SendGrid doesn't support direct message resending
        return new EmailResponse(false, ErrorMessage: "Message resending not supported by SendGrid");
    }

    public async Task<bool> ValidateConfigurationAsync()
    {
        try
        {
            var apiKey = GetConfigValue<string>("apiKey");
            var fromEmail = GetConfigValue<string>("fromEmail");

            if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(fromEmail))
                return false;

            // Test API connection
            var client = new SendGridClient(apiKey);
            var response = await client.RequestAsync(method: SendGridClient.Method.GET, urlPath: "user/profile");

            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private T? GetConfigValue<T>(string key, T? defaultValue = default)
    {
        if (_configuration.TryGetValue(key, out var configItem))
        {
            if (configItem.DefaultValue != null)
            {
                return (T)Convert.ChangeType(configItem.DefaultValue, typeof(T));
            }
        }
        return defaultValue;
    }

    public void Dispose()
    {
        // SendGrid client doesn't require explicit disposal
    }
}
