using PluginCore.Models;
using System.Net;
using System.Text.Json;

namespace PluginCore.Utilities;

/// <summary>
/// Utility class providing common functionality for gateway implementations.
/// </summary>
public static class GatewayUtilities
{
    /// <summary>
    /// Executes an operation with retry logic.
    /// </summary>
    public static async Task<T> ExecuteWithRetryAsync<T>(
        Func<Task<T>> operation,
        int maxRetries = 3,
        TimeSpan? delay = null,
        ILogger? logger = null,
        CancellationToken cancellationToken = default)
    {
        var actualDelay = delay ?? TimeSpan.FromSeconds(1);
        var lastException = default(Exception);

        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex) when (attempt < maxRetries && IsRetriableException(ex))
            {
                lastException = ex;
                logger?.LogWarning(ex, "Operation failed on attempt {Attempt}/{MaxRetries}. Retrying in {Delay}ms", 
                    attempt + 1, maxRetries + 1, actualDelay.TotalMilliseconds);
                
                await Task.Delay(actualDelay, cancellationToken);
                actualDelay = TimeSpan.FromMilliseconds(actualDelay.TotalMilliseconds * 1.5); // Exponential backoff
            }
        }

        throw lastException ?? new InvalidOperationException("Operation failed after all retry attempts");
    }

    /// <summary>
    /// Determines if an exception is retriable.
    /// </summary>
    public static bool IsRetriableException(Exception exception)
    {
        return exception switch
        {
            HttpRequestException httpEx => IsRetriableHttpStatusCode(httpEx),
            TaskCanceledException => true,
            TimeoutException => true,
            SocketException => true,
            _ => false
        };
    }

    /// <summary>
    /// Determines if an HTTP status code is retriable.
    /// </summary>
    public static bool IsRetriableHttpStatusCode(HttpRequestException httpException)
    {
        // Check if the exception message contains status codes that are retriable
        var message = httpException.Message.ToLowerInvariant();
        return message.Contains("500") || // Internal Server Error
               message.Contains("502") || // Bad Gateway
               message.Contains("503") || // Service Unavailable
               message.Contains("504") || // Gateway Timeout
               message.Contains("429");   // Too Many Requests
    }

    /// <summary>
    /// Creates a standardized error result for failed operations.
    /// </summary>
    public static MessageSendResult CreateFailureResult(string? correlationId = null, string? errorMessage = null)
    {
        return new MessageSendResult(
            MessageId: Guid.NewGuid().ToString(),
            Timestamp: DateTimeOffset.UtcNow,
            Status: "Failed",
            CorrelationId: correlationId
        );
    }

    /// <summary>
    /// Creates a standardized success result for successful operations.
    /// </summary>
    public static MessageSendResult CreateSuccessResult(string messageId, string? correlationId = null)
    {
        return new MessageSendResult(
            MessageId: messageId,
            Timestamp: DateTimeOffset.UtcNow,
            Status: "Sent",
            CorrelationId: correlationId
        );
    }

    /// <summary>
    /// Validates configuration values and returns validation errors.
    /// </summary>
    public static List<string> ValidateConfiguration(Dictionary<string, object> configuration, Dictionary<string, bool> requiredKeys)
    {
        var errors = new List<string>();

        foreach (var (key, isRequired) in requiredKeys)
        {
            if (isRequired && (!configuration.ContainsKey(key) || 
                configuration[key] == null || 
                string.IsNullOrWhiteSpace(configuration[key].ToString())))
            {
                errors.Add($"Required configuration '{key}' is missing or empty");
            }
        }

        return errors;
    }

    /// <summary>
    /// Safely gets a configuration value with type conversion.
    /// </summary>
    public static T? GetConfigurationValue<T>(Dictionary<string, object> configuration, string key, T? defaultValue = default)
    {
        if (!configuration.TryGetValue(key, out var value) || value == null)
            return defaultValue;

        try
        {
            if (value is T directValue)
                return directValue;

            if (typeof(T) == typeof(string))
                return (T)(object)value.ToString()!;

            if (typeof(T) == typeof(int) && int.TryParse(value.ToString(), out var intValue))
                return (T)(object)intValue;

            if (typeof(T) == typeof(bool) && bool.TryParse(value.ToString(), out var boolValue))
                return (T)(object)boolValue;

            if (typeof(T) == typeof(double) && double.TryParse(value.ToString(), out var doubleValue))
                return (T)(object)doubleValue;

            // Try JSON deserialization for complex types
            if (value is string jsonString)
            {
                return JsonSerializer.Deserialize<T>(jsonString);
            }

            return defaultValue;
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// Creates a basic HTTP client with common settings.
    /// </summary>
    public static HttpClient CreateHttpClient(string? baseAddress = null, TimeSpan? timeout = null)
    {
        var client = new HttpClient();
        
        if (!string.IsNullOrEmpty(baseAddress))
            client.BaseAddress = new Uri(baseAddress);
            
        client.Timeout = timeout ?? TimeSpan.FromSeconds(30);
        client.DefaultRequestHeaders.Add("User-Agent", "NotificationService/2.0");
        
        return client;
    }

    /// <summary>
    /// Sanitizes sensitive data from configuration for logging.
    /// </summary>
    public static Dictionary<string, object> SanitizeConfiguration(Dictionary<string, object> configuration, string[] sensitiveKeys)
    {
        var sanitized = new Dictionary<string, object>(configuration);
        
        foreach (var key in sensitiveKeys)
        {
            if (sanitized.ContainsKey(key))
            {
                var value = sanitized[key]?.ToString();
                if (!string.IsNullOrEmpty(value))
                {
                    sanitized[key] = value.Length > 4 ? 
                        $"{value[..2]}***{value[^2..]}" : 
                        "***";
                }
            }
        }
        
        return sanitized;
    }

    /// <summary>
    /// Generates a correlation ID if one is not provided.
    /// </summary>
    public static string GenerateCorrelationId(string? existingId = null)
    {
        return existingId ?? $"gw_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid():N}";
    }

    /// <summary>
    /// Validates a URL format.
    /// </summary>
    public static bool IsValidUrl(string url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }

    /// <summary>
    /// Truncates text to a maximum length with ellipsis.
    /// </summary>
    public static string TruncateText(string text, int maxLength, string ellipsis = "...")
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;

        return text[..(maxLength - ellipsis.Length)] + ellipsis;
    }

    /// <summary>
    /// Creates a standardized operation result.
    /// </summary>
    public static OperationResult CreateOperationResult(bool success, string? message = null, Exception? exception = null)
    {
        var errors = exception != null ? 
            new[] { new ErrorDetail("OperationError", exception.Message) } : 
            null;

        return new OperationResult(success, message, errors);
    }

    /// <summary>
    /// Parses a comma-separated string into a list.
    /// </summary>
    public static List<string> ParseCommaSeparatedString(string? input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return new List<string>();

        return input.Split(',', StringSplitOptions.RemoveEmptyEntries)
                   .Select(s => s.Trim())
                   .Where(s => !string.IsNullOrEmpty(s))
                   .ToList();
    }

    /// <summary>
    /// Creates a mock delivery result for testing purposes.
    /// </summary>
    public static DeliveryResult CreateMockDeliveryResult(string messageId, string status = "Delivered")
    {
        return new DeliveryResult(messageId, status, DateTimeOffset.UtcNow);
    }

    /// <summary>
    /// Creates a mock usage metrics for testing purposes.
    /// </summary>
    public static UsageMetrics CreateMockUsageMetrics(DateTimeOffset from, DateTimeOffset to)
    {
        var random = new Random();
        return new UsageMetrics(
            TotalMessages: random.Next(100, 1000),
            SuccessfulMessages: random.Next(80, 95),
            FailedMessages: random.Next(5, 20),
            EstimatedCost: random.NextDouble() * 100
        );
    }
}
