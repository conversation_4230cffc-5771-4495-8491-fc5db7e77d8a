{"sourceFile": "src/Services/NotifyMasterApi/Features/Setup/SetupEndpoints.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751192207594, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751192242169, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -27,15 +27,15 @@\n     {\r\n         return Task.FromResult(_setupStatus);\r\n     }\r\n \r\n-    public async Task<bool> InitializeSystemAsync(string rootTenantName, string adminEmail, string adminPassword)\r\n+    public Task<bool> InitializeSystemAsync(string rootTenantName, string adminEmail, string adminPassword)\r\n     {\r\n         lock (_lockObject)\r\n         {\r\n             if (_setupStatus.IsInitialized)\r\n             {\r\n-                return false; // Already initialized\r\n+                return Task.FromResult(false); // Already initialized\r\n             }\r\n \r\n             try\r\n             {\r\n@@ -49,30 +49,31 @@\n                 _setupStatus.RootTenantName = rootTenantName;\r\n                 _setupStatus.InitializedAt = DateTime.UtcNow;\r\n                 _setupStatus.InitializedBy = adminEmail;\r\n \r\n-                _logger.LogInformation(\"System initialized successfully with root tenant '{RootTenantName}' by {AdminEmail}\", \r\n+                _logger.LogInformation(\"System initialized successfully with root tenant '{RootTenantName}' by {AdminEmail}\",\r\n                     rootTenantName, adminEmail);\r\n \r\n-                return true;\r\n+                return Task.FromResult(true);\r\n             }\r\n             catch (Exception ex)\r\n             {\r\n                 _logger.LogError(ex, \"Failed to initialize system\");\r\n-                return false;\r\n+                return Task.FromResult(false);\r\n             }\r\n         }\r\n     }\r\n \r\n-    public async Task<bool> IsSystemInitializedAsync()\r\n+    public Task<bool> IsSystemInitializedAsync()\r\n     {\r\n-        return _setupStatus.IsInitialized;\r\n+        return Task.FromResult(_setupStatus.IsInitialized);\r\n     }\r\n \r\n-    public async Task LockSystemAsync()\r\n+    public Task LockSystemAsync()\r\n     {\r\n         // In a real implementation, this would set a flag in persistent storage\r\n         _setupStatus.IsInitialized = true;\r\n+        return Task.CompletedTask;\r\n     }\r\n }\r\n \r\n public class GetSetupStatusEndpoint : Endpoint<EmptyRequest, object>\r\n"}], "date": 1751192207594, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing NotifyMasterApi.Infrastructure;\r\nusing NotifyMasterApi.Services;\r\n\r\nnamespace NotifyMasterApi.Features.Setup;\r\n\r\npublic interface ISetupService\r\n{\r\n    Task<SetupStatus> GetSetupStatusAsync();\r\n    Task<bool> InitializeSystemAsync(string rootTenantName, string adminEmail, string adminPassword);\r\n    Task<bool> IsSystemInitializedAsync();\r\n    Task LockSystemAsync();\r\n}\r\n\r\npublic class SetupService : ISetupService\r\n{\r\n    private readonly ILogger<SetupService> _logger;\r\n    private static SetupStatus _setupStatus = new();\r\n    private static readonly object _lockObject = new();\r\n\r\n    public SetupService(ILogger<SetupService> logger)\r\n    {\r\n        _logger = logger;\r\n    }\r\n\r\n    public Task<SetupStatus> GetSetupStatusAsync()\r\n    {\r\n        return Task.FromResult(_setupStatus);\r\n    }\r\n\r\n    public async Task<bool> InitializeSystemAsync(string rootTenantName, string adminEmail, string adminPassword)\r\n    {\r\n        lock (_lockObject)\r\n        {\r\n            if (_setupStatus.IsInitialized)\r\n            {\r\n                return false; // Already initialized\r\n            }\r\n\r\n            try\r\n            {\r\n                // Initialize system components\r\n                _setupStatus.ComponentStatus[\"Database\"] = true;\r\n                _setupStatus.ComponentStatus[\"Plugins\"] = true;\r\n                _setupStatus.ComponentStatus[\"Queue\"] = true;\r\n                _setupStatus.ComponentStatus[\"EventStream\"] = true;\r\n\r\n                _setupStatus.IsInitialized = true;\r\n                _setupStatus.RootTenantName = rootTenantName;\r\n                _setupStatus.InitializedAt = DateTime.UtcNow;\r\n                _setupStatus.InitializedBy = adminEmail;\r\n\r\n                _logger.LogInformation(\"System initialized successfully with root tenant '{RootTenantName}' by {AdminEmail}\", \r\n                    rootTenantName, adminEmail);\r\n\r\n                return true;\r\n            }\r\n            catch (Exception ex)\r\n            {\r\n                _logger.LogError(ex, \"Failed to initialize system\");\r\n                return false;\r\n            }\r\n        }\r\n    }\r\n\r\n    public async Task<bool> IsSystemInitializedAsync()\r\n    {\r\n        return _setupStatus.IsInitialized;\r\n    }\r\n\r\n    public async Task LockSystemAsync()\r\n    {\r\n        // In a real implementation, this would set a flag in persistent storage\r\n        _setupStatus.IsInitialized = true;\r\n    }\r\n}\r\n\r\npublic class GetSetupStatusEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly ISetupService _setupService;\r\n\r\n    public GetSetupStatusEndpoint(ISetupService setupService)\r\n    {\r\n        _setupService = setupService;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/health/setup-status\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get setup status\";\r\n            s.Description = \"Check if the system has been initialized\";\r\n            s.Responses[200] = \"Setup status retrieved successfully\";\r\n        });\r\n        Tags(\"Setup\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        var status = await _setupService.GetSetupStatusAsync();\r\n        await SendOkAsync(status, ct);\r\n    }\r\n}\r\n\r\npublic class InitializeSystemRequest\r\n{\r\n    public string RootTenantName { get; set; } = string.Empty;\r\n    public string AdminEmail { get; set; } = string.Empty;\r\n    public string AdminPassword { get; set; } = string.Empty;\r\n    public Dictionary<string, object> Configuration { get; set; } = new();\r\n}\r\n\r\npublic class InitializeSystemEndpoint : Endpoint<InitializeSystemRequest, object>\r\n{\r\n    private readonly ISetupService _setupService;\r\n    private readonly ILogger<InitializeSystemEndpoint> _logger;\r\n\r\n    public InitializeSystemEndpoint(ISetupService setupService, ILogger<InitializeSystemEndpoint> logger)\r\n    {\r\n        _setupService = setupService;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/setup/initialize\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Initialize system\";\r\n            s.Description = \"Initialize the notification service system\";\r\n            s.Responses[200] = \"System initialized successfully\";\r\n            s.Responses[400] = \"Invalid request or system already initialized\";\r\n            s.Responses[500] = \"Initialization failed\";\r\n        });\r\n        Tags(\"Setup\");\r\n    }\r\n\r\n    public override async Task HandleAsync(InitializeSystemRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            if (await _setupService.IsSystemInitializedAsync())\r\n            {\r\n                await SendAsync(new { Error = \"System is already initialized\" }, 400, ct);\r\n                return;\r\n            }\r\n\r\n            var success = await _setupService.InitializeSystemAsync(req.RootTenantName, req.AdminEmail, req.AdminPassword);\r\n            \r\n            if (success)\r\n            {\r\n                await SendOkAsync(new\r\n                {\r\n                    Success = true,\r\n                    Message = \"System initialized successfully\",\r\n                    RootTenantName = req.RootTenantName,\r\n                    Timestamp = DateTime.UtcNow\r\n                }, ct);\r\n            }\r\n            else\r\n            {\r\n                await SendAsync(new { Error = \"Failed to initialize system\" }, 500, ct);\r\n            }\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error during system initialization\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetSystemUsageStatsEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly ILogger<GetSystemUsageStatsEndpoint> _logger;\r\n\r\n    public GetSystemUsageStatsEndpoint(ILogger<GetSystemUsageStatsEndpoint> logger)\r\n    {\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/system/usage-stats\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get system usage statistics\";\r\n            s.Description = \"Get live system metrics including messages per second, plugin usage, and queue depth\";\r\n            s.Responses[200] = \"Usage statistics retrieved successfully\";\r\n        });\r\n        Tags(\"System\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            // Simulate real-time metrics\r\n            var stats = new\r\n            {\r\n                MessagesPerSecond = new Random().NextDouble() * 100,\r\n                QueueDepth = new Random().Next(0, 1000),\r\n                PluginUsage = new\r\n                {\r\n                    Email = new\r\n                    {\r\n                        CpuUsage = new Random().NextDouble() * 100,\r\n                        MemoryUsageMB = new Random().Next(50, 500),\r\n                        RequestsPerSecond = new Random().NextDouble() * 50\r\n                    },\r\n                    SMS = new\r\n                    {\r\n                        CpuUsage = new Random().NextDouble() * 100,\r\n                        MemoryUsageMB = new Random().Next(30, 300),\r\n                        RequestsPerSecond = new Random().NextDouble() * 30\r\n                    },\r\n                    Push = new\r\n                    {\r\n                        CpuUsage = new Random().NextDouble() * 100,\r\n                        MemoryUsageMB = new Random().Next(40, 400),\r\n                        RequestsPerSecond = new Random().NextDouble() * 40\r\n                    }\r\n                },\r\n                SystemResources = new\r\n                {\r\n                    CpuUsage = new Random().NextDouble() * 100,\r\n                    MemoryUsageMB = new Random().Next(500, 2000),\r\n                    DiskUsageGB = new Random().Next(10, 100),\r\n                    NetworkBytesPerSecond = new Random().Next(1000, 100000)\r\n                },\r\n                Timestamp = DateTime.UtcNow\r\n            };\r\n\r\n            await SendOkAsync(stats, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting system usage statistics\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetChannelUsageRequest\r\n{\r\n    public string? TenantId { get; set; }\r\n    public string? Period { get; set; } = \"day\"; // hour, day, week, month\r\n    public DateTime? StartDate { get; set; }\r\n    public DateTime? EndDate { get; set; }\r\n}\r\n\r\npublic class GetChannelUsageEndpoint : Endpoint<GetChannelUsageRequest, object>\r\n{\r\n    private readonly ILogger<GetChannelUsageEndpoint> _logger;\r\n\r\n    public GetChannelUsageEndpoint(ILogger<GetChannelUsageEndpoint> logger)\r\n    {\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/system/channel-usage\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get channel usage breakdown\";\r\n            s.Description = \"Get per-channel usage statistics with breakdown by period, user, or campaign\";\r\n            s.Responses[200] = \"Channel usage retrieved successfully\";\r\n        });\r\n        Tags(\"System\");\r\n    }\r\n\r\n    public override async Task HandleAsync(GetChannelUsageRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            // Simulate channel usage data\r\n            var usage = new\r\n            {\r\n                TenantId = req.TenantId ?? \"all\",\r\n                Period = req.Period,\r\n                StartDate = req.StartDate ?? DateTime.UtcNow.AddDays(-7),\r\n                EndDate = req.EndDate ?? DateTime.UtcNow,\r\n                Channels = new\r\n                {\r\n                    Email = new\r\n                    {\r\n                        TotalMessages = new Random().Next(1000, 10000),\r\n                        SuccessfulMessages = new Random().Next(900, 9500),\r\n                        FailedMessages = new Random().Next(10, 500),\r\n                        AverageLatencyMs = new Random().Next(100, 2000),\r\n                        Cost = Math.Round(new Random().NextDouble() * 100, 2),\r\n                        TopProviders = new[]\r\n                        {\r\n                            new { Provider = \"SendGrid\", Count = new Random().Next(500, 5000) },\r\n                            new { Provider = \"Mailgun\", Count = new Random().Next(200, 2000) }\r\n                        }\r\n                    },\r\n                    SMS = new\r\n                    {\r\n                        TotalMessages = new Random().Next(500, 5000),\r\n                        SuccessfulMessages = new Random().Next(450, 4800),\r\n                        FailedMessages = new Random().Next(5, 200),\r\n                        AverageLatencyMs = new Random().Next(500, 3000),\r\n                        Cost = Math.Round(new Random().NextDouble() * 200, 2),\r\n                        TopProviders = new[]\r\n                        {\r\n                            new { Provider = \"Twilio\", Count = new Random().Next(300, 3000) },\r\n                            new { Provider = \"BulkSMS\", Count = new Random().Next(100, 1000) }\r\n                        }\r\n                    },\r\n                    Push = new\r\n                    {\r\n                        TotalMessages = new Random().Next(2000, 20000),\r\n                        SuccessfulMessages = new Random().Next(1800, 19000),\r\n                        FailedMessages = new Random().Next(50, 1000),\r\n                        AverageLatencyMs = new Random().Next(50, 1000),\r\n                        Cost = Math.Round(new Random().NextDouble() * 50, 2),\r\n                        TopProviders = new[]\r\n                        {\r\n                            new { Provider = \"Firebase FCM\", Count = new Random().Next(1500, 15000) }\r\n                        }\r\n                    }\r\n                },\r\n                Timestamp = DateTime.UtcNow\r\n            };\r\n\r\n            await SendOkAsync(usage, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting channel usage\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n"}]}