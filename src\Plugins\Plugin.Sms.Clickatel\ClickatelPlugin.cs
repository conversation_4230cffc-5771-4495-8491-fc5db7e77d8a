using Microsoft.Extensions.Logging;
using PluginCore.Interfaces;
using PluginCore.Models;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Plugin.Sms.Clickatel;

public class ClickatelPlugin : ISmsPlugin
{
    private readonly ILogger<ClickatelPlugin> _logger;
    private readonly HttpClient _httpClient;
    private Dictionary<string, PluginConfigurationItem> _configuration = new();
    private bool _isInitialized = false;

    public ClickatelPlugin(ILogger<ClickatelPlugin> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    public string Name => "Clickatel SMS Plugin";
    public string Version => "1.0.0";
    public string Provider => "Clickatel";

    public async Task InitializeAsync(Dictionary<string, PluginConfigurationItem> configuration)
    {
        _configuration = configuration;
        
        var apiUrl = GetConfigValue<string>("apiUrl") ?? "https://platform.clickatell.com";
        var apiKey = GetConfigValue<string>("apiKey");
        var timeout = GetConfigValue<int>("timeout", 30);

        if (string.IsNullOrEmpty(apiKey))
        {
            throw new InvalidOperationException("Clickatel API Key is required");
        }

        _httpClient.BaseAddress = new Uri(apiUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(timeout);
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

        _isInitialized = true;
        _logger.LogInformation("Clickatel plugin initialized successfully");
    }

    public async Task<SmsResponse> SendAsync(SmsMessageRequest request)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var payload = new
            {
                messages = new[]
                {
                    new
                    {
                        to = new[] { request.PhoneNumber },
                        text = request.Message,
                        from = GetConfigValue<string>("from")
                    }
                }
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/v1/message", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ClickatelResponse>(responseContent);
                var message = result?.Messages?.FirstOrDefault();
                
                if (message != null && !string.IsNullOrEmpty(message.ApiMessageId))
                {
                    return new SmsResponse(true, MessageId: message.ApiMessageId);
                }
                else
                {
                    var errorMessage = message?.Error?.Description ?? "Unknown error";
                    return new SmsResponse(false, ErrorMessage: errorMessage);
                }
            }
            else
            {
                _logger.LogError("Clickatel API error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                return new SmsResponse(false, ErrorMessage: $"API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS via Clickatel");
            return new SmsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var messages = request.Messages.Select(sms => new
            {
                to = new[] { sms.PhoneNumber },
                text = sms.Message,
                from = GetConfigValue<string>("from")
            }).ToArray();

            var payload = new { messages };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/v1/message", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ClickatelResponse>(responseContent);
                var results = new List<SmsResponse>();

                if (result?.Messages != null)
                {
                    foreach (var message in result.Messages)
                    {
                        if (!string.IsNullOrEmpty(message.ApiMessageId))
                        {
                            results.Add(new SmsResponse(true, MessageId: message.ApiMessageId));
                        }
                        else
                        {
                            var errorMessage = message.Error?.Description ?? "Unknown error";
                            results.Add(new SmsResponse(false, ErrorMessage: errorMessage));
                        }
                    }
                }

                var successCount = results.Count(r => r.IsSuccess);
                return new BulkSmsResponse(
                    successCount == results.Count,
                    SuccessCount: successCount,
                    FailureCount: results.Count - successCount,
                    Results: results
                );
            }
            else
            {
                _logger.LogError("Clickatel bulk API error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                return new BulkSmsResponse(false, ErrorMessage: $"API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk SMS via Clickatel");
            return new BulkSmsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var response = await _httpClient.GetAsync($"/v1/message/{messageId}");
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ClickatelMessageStatus>(responseContent);
                return new MessageStatusResponse(true, Status: result?.Status ?? "Unknown");
            }
            else
            {
                return new MessageStatusResponse(false, ErrorMessage: $"API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message status from Clickatel");
            return new MessageStatusResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber)
    {
        // Clickatel doesn't provide message history by phone number in their standard API
        return new MessageHistoryResponse(false, ErrorMessage: "Message history not supported by Clickatel API");
    }

    public async Task<SmsResponse> ResendMessageAsync(string messageId)
    {
        // Clickatel doesn't support message resending
        return new SmsResponse(false, ErrorMessage: "Message resending not supported by Clickatel API");
    }

    public async Task<bool> ValidateConfigurationAsync()
    {
        try
        {
            var apiKey = GetConfigValue<string>("apiKey");
            
            if (string.IsNullOrEmpty(apiKey))
                return false;

            // Test API connection
            var response = await _httpClient.GetAsync("/v1/account/balance");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private T? GetConfigValue<T>(string key, T? defaultValue = default)
    {
        if (_configuration.TryGetValue(key, out var configItem))
        {
            if (configItem.DefaultValue != null)
            {
                return (T)Convert.ChangeType(configItem.DefaultValue, typeof(T));
            }
        }
        return defaultValue;
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}

// Response models for Clickatel API
public class ClickatelResponse
{
    public ClickatelMessage[]? Messages { get; set; }
}

public class ClickatelMessage
{
    public string? ApiMessageId { get; set; }
    public string? To { get; set; }
    public ClickatelError? Error { get; set; }
}

public class ClickatelError
{
    public string? Code { get; set; }
    public string? Description { get; set; }
}

public class ClickatelMessageStatus
{
    public string? ApiMessageId { get; set; }
    public string? Status { get; set; }
    public string? To { get; set; }
}
