using Microsoft.Extensions.DependencyInjection;
using PluginContract.Interfaces;
using PluginCore.Services;

namespace PluginCore.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddPluginSystem(this IServiceCollection services)
    {
        services.AddSingleton<IPluginManager, PluginManager>();
        services.AddScoped<NotificationService>();

        return services;
    }
    public static IServiceCollection AddEmailService(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<EmailServiceSettings>(configuration.GetSection("EmailService"));

        services.AddScoped<IEmailService, EmailSenderService>();
        services.AddScoped<ISmtpClient, SmtpClientService>();

        return services;
    }
    public static IServiceCollection AddPushNotificationService(this IServiceCollection services)
    {
        services.AddScoped<IPushNotificationService, PushNotificationServiceImplementation>();

        return services;
    }
    public static IServiceCollection AddSmsService(this IServiceCollection services)
    {
        services.AddScoped<ISmsService, SmsServiceImplementation>();
        
        return services;
    }
}
