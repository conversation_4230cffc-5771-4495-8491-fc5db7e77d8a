using PluginCore.Models;
using System.ComponentModel.DataAnnotations;

namespace PluginCore.Base;

/// <summary>
/// Handles the core functionality of sending messages through a gateway.
/// </summary>
/// <remarks>
/// This interface defines the core functionality for sending messages through a gateway.
/// </remarks>
public interface IGatewayMessagePluginType
{
    /// <summary>
    /// Sends a single outbound message through the gateway.
    /// </summary>
    /// <param name="payload">The message payload to send.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A MessageSendResult indicating the success of the send operation.</returns>
    Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends multiple messages in a single, optimized operation.
    /// </summary>
    /// <param name="payloads">The list of message payloads to send.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A list of MessageSendResult objects, one for each sent message.</returns>
    Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a message using a registered template and dynamic data.
    /// </summary>
    /// <param name="templateId">The ID of the template to use.</param>
    /// <param name="templateData">The dynamic data to populate the template.</param>
    /// <param name="recipient">The intended recipient of the message.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A MessageSendResult indicating the success of the send operation.</returns>
    Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedules a message for future delivery.
    /// </summary>
    /// <param name="payload">The message payload to send.</param>
    /// <param name="scheduledTime">The time at which the message should be sent.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A MessageScheduleResult indicating the success of the scheduling operation.</returns>
    Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancels a previously scheduled message.
    /// </summary>
    /// <param name="scheduledMessageId">The ID of the scheduled message to cancel.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>    
    /// <returns>An OperationResult indicating the success of the cancellation.</returns>
    Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the real-time status of a specific message.
    /// </summary>
    /// <param name="messageId">The ID of the message to check.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A MessageStatusInfo object representing the current status of the message.</returns>
    Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves a provider-issued delivery receipt if available.
    /// </summary>
    /// <param name="messageId">The ID of the message to check.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A DeliveryReceipt object if available, otherwise null.</returns>
    Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates a message payload without sending it.
    /// </summary>
    /// <param name="payload">The message payload to validate.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A ValidationResult indicating whether the message is valid and any errors found.</returns>
    Task<ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);
}
