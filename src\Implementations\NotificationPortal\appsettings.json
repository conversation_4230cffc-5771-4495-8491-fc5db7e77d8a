{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.SignalR": "Information"}}, "AllowedHosts": "*", "ApiSettings": {"BaseUrl": "http://localhost:5120", "Timeout": 30}, "Authentication": {"DefaultScheme": "Cookies", "CookieExpiration": "7.00:00:00"}, "Features": {"EnableSignalR": true, "EnableNotifications": true, "EnableAuditLogging": true, "EnableMultiTenancy": true}, "UI": {"DefaultTheme": "light", "DefaultLanguage": "en", "PageSize": 25, "EnableAnimations": true}}