{"sourceFile": "src/Core/PluginCore/Utilities/RateLimiter.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751213690133, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751213690133, "name": "Commit-0", "content": "using System.Collections.Concurrent;\nusing Microsoft.Extensions.Logging;\n\nnamespace PluginCore.Utilities;\n\n/// <summary>\n/// Simple rate limiter implementation for gateway operations.\n/// </summary>\npublic class RateLimiter\n{\n    private readonly ConcurrentDictionary<string, TokenBucket> _buckets = new();\n    private readonly Timer _cleanupTimer;\n\n    public RateLimiter()\n    {\n        // Cleanup expired buckets every 5 minutes\n        _cleanupTimer = new Timer(CleanupExpiredBuckets, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));\n    }\n\n    /// <summary>\n    /// Checks if an operation is allowed based on rate limiting rules.\n    /// </summary>\n    /// <param name=\"key\">The key to rate limit (e.g., API key, user ID, etc.)</param>\n    /// <param name=\"maxRequests\">Maximum number of requests allowed</param>\n    /// <param name=\"timeWindow\">Time window for the rate limit</param>\n    /// <returns>True if the operation is allowed, false otherwise</returns>\n    public bool IsAllowed(string key, int maxRequests, TimeSpan timeWindow)\n    {\n        var bucket = _buckets.GetOrAdd(key, _ => new TokenBucket(maxRequests, timeWindow));\n        return bucket.TryConsume();\n    }\n\n    /// <summary>\n    /// Gets the remaining requests for a given key.\n    /// </summary>\n    public int GetRemainingRequests(string key, int maxRequests, TimeSpan timeWindow)\n    {\n        var bucket = _buckets.GetOrAdd(key, _ => new TokenBucket(maxRequests, timeWindow));\n        return bucket.RemainingTokens;\n    }\n\n    /// <summary>\n    /// Gets the time until the next token is available.\n    /// </summary>\n    public TimeSpan GetTimeUntilReset(string key, int maxRequests, TimeSpan timeWindow)\n    {\n        var bucket = _buckets.GetOrAdd(key, _ => new TokenBucket(maxRequests, timeWindow));\n        return bucket.TimeUntilReset;\n    }\n\n    private void CleanupExpiredBuckets(object? state)\n    {\n        var expiredKeys = new List<string>();\n        var cutoff = DateTime.UtcNow.AddHours(-1); // Remove buckets older than 1 hour\n\n        foreach (var kvp in _buckets)\n        {\n            if (kvp.Value.LastAccess < cutoff)\n            {\n                expiredKeys.Add(kvp.Key);\n            }\n        }\n\n        foreach (var key in expiredKeys)\n        {\n            _buckets.TryRemove(key, out _);\n        }\n    }\n\n    public void Dispose()\n    {\n        _cleanupTimer?.Dispose();\n    }\n}\n\n/// <summary>\n/// Token bucket implementation for rate limiting.\n/// </summary>\ninternal class TokenBucket\n{\n    private readonly int _maxTokens;\n    private readonly TimeSpan _refillInterval;\n    private readonly object _lock = new();\n    \n    private int _currentTokens;\n    private DateTime _lastRefill;\n\n    public DateTime LastAccess { get; private set; }\n    \n    public int RemainingTokens\n    {\n        get\n        {\n            lock (_lock)\n            {\n                RefillTokens();\n                return _currentTokens;\n            }\n        }\n    }\n\n    public TimeSpan TimeUntilReset\n    {\n        get\n        {\n            lock (_lock)\n            {\n                if (_currentTokens > 0)\n                    return TimeSpan.Zero;\n\n                var nextRefill = _lastRefill.Add(_refillInterval);\n                var timeUntilNext = nextRefill - DateTime.UtcNow;\n                return timeUntilNext > TimeSpan.Zero ? timeUntilNext : TimeSpan.Zero;\n            }\n        }\n    }\n\n    public TokenBucket(int maxTokens, TimeSpan refillInterval)\n    {\n        _maxTokens = maxTokens;\n        _refillInterval = refillInterval;\n        _currentTokens = maxTokens;\n        _lastRefill = DateTime.UtcNow;\n        LastAccess = DateTime.UtcNow;\n    }\n\n    public bool TryConsume(int tokens = 1)\n    {\n        lock (_lock)\n        {\n            RefillTokens();\n            LastAccess = DateTime.UtcNow;\n\n            if (_currentTokens >= tokens)\n            {\n                _currentTokens -= tokens;\n                return true;\n            }\n\n            return false;\n        }\n    }\n\n    private void RefillTokens()\n    {\n        var now = DateTime.UtcNow;\n        var timeSinceLastRefill = now - _lastRefill;\n\n        if (timeSinceLastRefill >= _refillInterval)\n        {\n            var intervalsElapsed = (int)(timeSinceLastRefill.TotalMilliseconds / _refillInterval.TotalMilliseconds);\n            var tokensToAdd = intervalsElapsed * _maxTokens;\n            \n            _currentTokens = Math.Min(_maxTokens, _currentTokens + tokensToAdd);\n            _lastRefill = now;\n        }\n    }\n}\n\n/// <summary>\n/// Rate limiting configuration for different operations.\n/// </summary>\npublic class RateLimitConfig\n{\n    public int MaxRequests { get; set; } = 100;\n    public TimeSpan TimeWindow { get; set; } = TimeSpan.FromMinutes(1);\n    public bool Enabled { get; set; } = true;\n\n    public static RateLimitConfig Default => new();\n    \n    public static RateLimitConfig Conservative => new()\n    {\n        MaxRequests = 10,\n        TimeWindow = TimeSpan.FromMinutes(1)\n    };\n    \n    public static RateLimitConfig Aggressive => new()\n    {\n        MaxRequests = 1000,\n        TimeWindow = TimeSpan.FromMinutes(1)\n    };\n}\n\n/// <summary>\n/// Extension methods for rate limiting in gateway operations.\n/// </summary>\npublic static class RateLimitExtensions\n{\n    private static readonly RateLimiter _globalRateLimiter = new();\n\n    /// <summary>\n    /// Executes an operation with rate limiting.\n    /// </summary>\n    public static async Task<T> ExecuteWithRateLimitAsync<T>(\n        this Func<Task<T>> operation,\n        string rateLimitKey,\n        RateLimitConfig config,\n        ILogger? logger = null,\n        CancellationToken cancellationToken = default)\n    {\n        if (!config.Enabled)\n        {\n            return await operation();\n        }\n\n        if (!_globalRateLimiter.IsAllowed(rateLimitKey, config.MaxRequests, config.TimeWindow))\n        {\n            var timeUntilReset = _globalRateLimiter.GetTimeUntilReset(rateLimitKey, config.MaxRequests, config.TimeWindow);\n            logger?.LogWarning(\"Rate limit exceeded for key {Key}. Time until reset: {TimeUntilReset}\", \n                rateLimitKey, timeUntilReset);\n            \n            throw new InvalidOperationException($\"Rate limit exceeded. Try again in {timeUntilReset.TotalSeconds:F0} seconds.\");\n        }\n\n        return await operation();\n    }\n\n    /// <summary>\n    /// Gets rate limit information for a key.\n    /// </summary>\n    public static (int remaining, TimeSpan resetTime) GetRateLimitInfo(string rateLimitKey, RateLimitConfig config)\n    {\n        var remaining = _globalRateLimiter.GetRemainingRequests(rateLimitKey, config.MaxRequests, config.TimeWindow);\n        var resetTime = _globalRateLimiter.GetTimeUntilReset(rateLimitKey, config.MaxRequests, config.TimeWindow);\n        return (remaining, resetTime);\n    }\n}\n"}]}