{"sourceFile": "src/Services/NotifyMasterApi/Documentation/EndpointDocumentationExtensions.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 9, "patches": [{"date": 1751192017828, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751192032384, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -76,9 +76,9 @@\n                 s.Responses[403] = \"🔒 Admin privileges required\";\n             }\n         });\n \n-        endpoint.Tags(tags);\n+        // Note: Tags should be configured in the endpoint's Configure() method\n     }\n \n     /// <summary>\n     /// Configure documentation for admin endpoints\n"}, {"date": 1751192106178, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -88,9 +88,9 @@\n         string method,\n         string route,\n         string summary,\n         string description,\n-        string[] additionalTags = null)\n+        string[]? additionalTags = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n         var tags = new List<string> { \"🔧 Administration\", \"System Management\" };\n"}, {"date": 1751192126151, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -113,9 +113,9 @@\n         string method,\n         string route,\n         string summary,\n         string description,\n-        string[] additionalTags = null)\n+        string[]? additionalTags = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n         var tags = new List<string> { \"🔌 Plugin Management\", \"Extensions\" };\n"}, {"date": 1751192145347, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -137,9 +137,9 @@\n         string method,\n         string route,\n         string summary,\n         string description,\n-        string[] additionalTags = null)\n+        string[]? additionalTags = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n         var tags = new List<string> { \"💚 Health & Monitoring\", \"System Status\" };\n"}, {"date": 1751192170450, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -162,9 +162,9 @@\n         string route,\n         string summary,\n         string description,\n         string notificationType,\n-        string[] additionalTags = null)\n+        string[]? additionalTags = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n         var emoji = notificationType.ToLower() switch\n"}, {"date": 1751192343845, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -27,9 +27,9 @@\n     {\n         // Note: Route configuration should be done in the endpoint's Configure() method\n         // This extension method only handles documentation configuration\n \n-        endpoint.Description(s =>\n+        endpoint.Summary(s =>\n         {\n             s.Summary = summary;\n             s.Description = $@\"{description}\n \n"}, {"date": 1751192496422, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,10 @@\n public static class EndpointDocumentationExtensions\n {\n     /// <summary>\n     /// Configure comprehensive documentation for messaging endpoints\n+    /// Note: This is a placeholder for documentation configuration.\n+    /// Actual endpoint configuration should be done in the endpoint's Configure() method.\n     /// </summary>\n     public static void ConfigureMessagingEndpoint<TRequest, TResponse>(\n         this Endpoint<TRequest, TResponse> endpoint,\n         string method,\n@@ -24,61 +26,11 @@\n         string? exampleResponse = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n-        // Note: Route configuration should be done in the endpoint's Configure() method\n-        // This extension method only handles documentation configuration\n-\n-        endpoint.Summary(s =>\n-        {\n-            s.Summary = summary;\n-            s.Description = $@\"{description}\n-\n-## 📋 Category\n-{category}\n-\n-## 🔧 Usage\n-This endpoint is part of the **{category}** functionality of the NotificationService API.\n-\n-{(exampleRequest != null ? $\"## 📝 Example Request\\n```json\\n{exampleRequest}\\n```\\n\" : \"\")}\n-{(exampleResponse != null ? $\"## ✅ Example Response\\n```json\\n{exampleResponse}\\n```\\n\" : \"\")}\n-\n-## 📚 Related Endpoints\n-- Health Check: `GET /api/health`\n-- System Status: `GET /api/admin/system/status`\n-- Plugin Management: `GET /api/plugins`\";\n-\n-            // Standard response codes\n-            s.Responses[200] = \"✅ Operation completed successfully\";\n-            s.Responses[400] = \"❌ Bad Request - Invalid input parameters\";\n-            s.Responses[401] = \"🔒 Unauthorized - Authentication required\";\n-            s.Responses[403] = \"🚫 Forbidden - Insufficient permissions\";\n-            s.Responses[404] = \"🔍 Not Found - Resource not found\";\n-            s.Responses[429] = \"⏱️ Too Many Requests - Rate limit exceeded\";\n-            s.Responses[500] = \"💥 Internal Server Error - Something went wrong\";\n-            s.Responses[503] = \"🚧 Service Unavailable - Service temporarily unavailable\";\n-\n-            // Add custom response descriptions based on endpoint type\n-            if (category.Contains(\"Messaging\") || category.Contains(\"Email\") || category.Contains(\"SMS\") || category.Contains(\"Push\"))\n-            {\n-                s.Responses[200] = \"✅ Message sent successfully\";\n-                s.Responses[400] = \"❌ Invalid message format or missing required fields\";\n-                s.Responses[422] = \"📝 Validation Error - Message content validation failed\";\n-            }\n-            else if (category.Contains(\"Plugin\"))\n-            {\n-                s.Responses[200] = \"✅ Plugin operation completed successfully\";\n-                s.Responses[404] = \"🔌 Plugin not found or not loaded\";\n-                s.Responses[409] = \"⚠️ Plugin conflict or dependency issue\";\n-            }\n-            else if (category.Contains(\"Admin\") || category.Contains(\"System\"))\n-            {\n-                s.Responses[200] = \"✅ System information retrieved successfully\";\n-                s.Responses[403] = \"🔒 Admin privileges required\";\n-            }\n-        });\n-\n-        // Note: Tags should be configured in the endpoint's Configure() method\n+        // Note: This extension method is for documentation purposes only.\n+        // Actual endpoint configuration (routes, auth, summaries, tags) should be done\n+        // in the endpoint's Configure() method using FastEndpoints' built-in methods.\n     }\n \n     /// <summary>\n     /// Configure documentation for admin endpoints\n"}, {"date": 1751192512310, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,18 +44,9 @@\n         string[]? additionalTags = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n-        var tags = new List<string> { \"🔧 Administration\", \"System Management\" };\n-        if (additionalTags != null)\n-            tags.AddRange(additionalTags);\n-\n-        endpoint.ConfigureMessagingEndpoint(\n-            method, route, summary, description,\n-            \"System Administration\",\n-            tags.ToArray(),\n-            requiresAuth: true\n-        );\n+        // Note: This is a placeholder for admin endpoint documentation\n     }\n \n     /// <summary>\n     /// Configure documentation for plugin endpoints\n"}, {"date": 1751192539881, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -60,17 +60,9 @@\n         string[]? additionalTags = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n-        var tags = new List<string> { \"🔌 Plugin Management\", \"Extensions\" };\n-        if (additionalTags != null)\n-            tags.AddRange(additionalTags);\n-\n-        endpoint.ConfigureMessagingEndpoint(\n-            method, route, summary, description,\n-            \"Plugin Management\",\n-            tags.ToArray()\n-        );\n+        // Note: This is a placeholder for plugin endpoint documentation\n     }\n \n     /// <summary>\n     /// Configure documentation for health and monitoring endpoints\n@@ -84,17 +76,9 @@\n         string[]? additionalTags = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n-        var tags = new List<string> { \"💚 Health & Monitoring\", \"System Status\" };\n-        if (additionalTags != null)\n-            tags.AddRange(additionalTags);\n-\n-        endpoint.ConfigureMessagingEndpoint(\n-            method, route, summary, description,\n-            \"Health & Monitoring\",\n-            tags.ToArray()\n-        );\n+        // Note: This is a placeholder for health endpoint documentation\n     }\n \n     /// <summary>\n     /// Configure documentation for messaging endpoints (Email, SMS, Push)\n@@ -109,26 +93,9 @@\n         string[]? additionalTags = null)\n         where TRequest : notnull, new()\n         where TResponse : notnull, new()\n     {\n-        var emoji = notificationType.ToLower() switch\n-        {\n-            \"email\" => \"📧\",\n-            \"sms\" => \"📱\",\n-            \"push\" => \"🔔\",\n-            \"messaging\" => \"💬\",\n-            _ => \"📨\"\n-        };\n-\n-        var tags = new List<string> { $\"{emoji} {notificationType}\", \"Messaging\", \"Notifications\" };\n-        if (additionalTags != null)\n-            tags.AddRange(additionalTags);\n-\n-        endpoint.ConfigureMessagingEndpoint(\n-            method, route, summary, description,\n-            $\"{notificationType} Messaging\",\n-            tags.ToArray()\n-        );\n+        // Note: This is a placeholder for notification endpoint documentation\n     }\n }\n \n /// <summary>\n"}], "date": 1751192017828, "name": "Commit-0", "content": "using FastEndpoints;\nusing System.ComponentModel;\n\nnamespace NotifyMasterApi.Documentation;\n\n/// <summary>\n/// Extension methods for enhanced FastEndpoints documentation\n/// </summary>\npublic static class EndpointDocumentationExtensions\n{\n    /// <summary>\n    /// Configure comprehensive documentation for messaging endpoints\n    /// </summary>\n    public static void ConfigureMessagingEndpoint<TRequest, TResponse>(\n        this Endpoint<TRequest, TResponse> endpoint,\n        string method,\n        string route,\n        string summary,\n        string description,\n        string category,\n        string[] tags,\n        bool requiresAuth = false,\n        string? exampleRequest = null,\n        string? exampleResponse = null)\n        where TRequest : notnull, new()\n        where TResponse : notnull, new()\n    {\n        // Note: Route configuration should be done in the endpoint's Configure() method\n        // This extension method only handles documentation configuration\n\n        endpoint.Description(s =>\n        {\n            s.Summary = summary;\n            s.Description = $@\"{description}\n\n## 📋 Category\n{category}\n\n## 🔧 Usage\nThis endpoint is part of the **{category}** functionality of the NotificationService API.\n\n{(exampleRequest != null ? $\"## 📝 Example Request\\n```json\\n{exampleRequest}\\n```\\n\" : \"\")}\n{(exampleResponse != null ? $\"## ✅ Example Response\\n```json\\n{exampleResponse}\\n```\\n\" : \"\")}\n\n## 📚 Related Endpoints\n- Health Check: `GET /api/health`\n- System Status: `GET /api/admin/system/status`\n- Plugin Management: `GET /api/plugins`\";\n\n            // Standard response codes\n            s.Responses[200] = \"✅ Operation completed successfully\";\n            s.Responses[400] = \"❌ Bad Request - Invalid input parameters\";\n            s.Responses[401] = \"🔒 Unauthorized - Authentication required\";\n            s.Responses[403] = \"🚫 Forbidden - Insufficient permissions\";\n            s.Responses[404] = \"🔍 Not Found - Resource not found\";\n            s.Responses[429] = \"⏱️ Too Many Requests - Rate limit exceeded\";\n            s.Responses[500] = \"💥 Internal Server Error - Something went wrong\";\n            s.Responses[503] = \"🚧 Service Unavailable - Service temporarily unavailable\";\n\n            // Add custom response descriptions based on endpoint type\n            if (category.Contains(\"Messaging\") || category.Contains(\"Email\") || category.Contains(\"SMS\") || category.Contains(\"Push\"))\n            {\n                s.Responses[200] = \"✅ Message sent successfully\";\n                s.Responses[400] = \"❌ Invalid message format or missing required fields\";\n                s.Responses[422] = \"📝 Validation Error - Message content validation failed\";\n            }\n            else if (category.Contains(\"Plugin\"))\n            {\n                s.Responses[200] = \"✅ Plugin operation completed successfully\";\n                s.Responses[404] = \"🔌 Plugin not found or not loaded\";\n                s.Responses[409] = \"⚠️ Plugin conflict or dependency issue\";\n            }\n            else if (category.Contains(\"Admin\") || category.Contains(\"System\"))\n            {\n                s.Responses[200] = \"✅ System information retrieved successfully\";\n                s.Responses[403] = \"🔒 Admin privileges required\";\n            }\n        });\n\n        endpoint.Tags(tags);\n    }\n\n    /// <summary>\n    /// Configure documentation for admin endpoints\n    /// </summary>\n    public static void ConfigureAdminEndpoint<TRequest, TResponse>(\n        this Endpoint<TRequest, TResponse> endpoint,\n        string method,\n        string route,\n        string summary,\n        string description,\n        string[] additionalTags = null)\n        where TRequest : notnull, new()\n        where TResponse : notnull, new()\n    {\n        var tags = new List<string> { \"🔧 Administration\", \"System Management\" };\n        if (additionalTags != null)\n            tags.AddRange(additionalTags);\n\n        endpoint.ConfigureMessagingEndpoint(\n            method, route, summary, description,\n            \"System Administration\",\n            tags.ToArray(),\n            requiresAuth: true\n        );\n    }\n\n    /// <summary>\n    /// Configure documentation for plugin endpoints\n    /// </summary>\n    public static void ConfigurePluginEndpoint<TRequest, TResponse>(\n        this Endpoint<TRequest, TResponse> endpoint,\n        string method,\n        string route,\n        string summary,\n        string description,\n        string[] additionalTags = null)\n        where TRequest : notnull, new()\n        where TResponse : notnull, new()\n    {\n        var tags = new List<string> { \"🔌 Plugin Management\", \"Extensions\" };\n        if (additionalTags != null)\n            tags.AddRange(additionalTags);\n\n        endpoint.ConfigureMessagingEndpoint(\n            method, route, summary, description,\n            \"Plugin Management\",\n            tags.ToArray()\n        );\n    }\n\n    /// <summary>\n    /// Configure documentation for health and monitoring endpoints\n    /// </summary>\n    public static void ConfigureHealthEndpoint<TRequest, TResponse>(\n        this Endpoint<TRequest, TResponse> endpoint,\n        string method,\n        string route,\n        string summary,\n        string description,\n        string[] additionalTags = null)\n        where TRequest : notnull, new()\n        where TResponse : notnull, new()\n    {\n        var tags = new List<string> { \"💚 Health & Monitoring\", \"System Status\" };\n        if (additionalTags != null)\n            tags.AddRange(additionalTags);\n\n        endpoint.ConfigureMessagingEndpoint(\n            method, route, summary, description,\n            \"Health & Monitoring\",\n            tags.ToArray()\n        );\n    }\n\n    /// <summary>\n    /// Configure documentation for messaging endpoints (Email, SMS, Push)\n    /// </summary>\n    public static void ConfigureNotificationEndpoint<TRequest, TResponse>(\n        this Endpoint<TRequest, TResponse> endpoint,\n        string method,\n        string route,\n        string summary,\n        string description,\n        string notificationType,\n        string[] additionalTags = null)\n        where TRequest : notnull, new()\n        where TResponse : notnull, new()\n    {\n        var emoji = notificationType.ToLower() switch\n        {\n            \"email\" => \"📧\",\n            \"sms\" => \"📱\",\n            \"push\" => \"🔔\",\n            \"messaging\" => \"💬\",\n            _ => \"📨\"\n        };\n\n        var tags = new List<string> { $\"{emoji} {notificationType}\", \"Messaging\", \"Notifications\" };\n        if (additionalTags != null)\n            tags.AddRange(additionalTags);\n\n        endpoint.ConfigureMessagingEndpoint(\n            method, route, summary, description,\n            $\"{notificationType} Messaging\",\n            tags.ToArray()\n        );\n    }\n}\n\n/// <summary>\n/// Documentation categories for endpoint organization\n/// </summary>\npublic static class DocumentationCategories\n{\n    public const string Email = \"📧 Email Services\";\n    public const string SMS = \"📱 SMS Services\";\n    public const string Push = \"🔔 Push Notifications\";\n    public const string Messaging = \"💬 Messaging Services\";\n    public const string PluginManagement = \"🔌 Plugin Management\";\n    public const string SystemAdmin = \"🔧 System Administration\";\n    public const string HealthMonitoring = \"💚 Health & Monitoring\";\n    public const string TenantManagement = \"🏢 Tenant Management\";\n    public const string Webhooks = \"🔗 Webhooks & Events\";\n    public const string Templates = \"📄 Templates\";\n    public const string Advanced = \"⚡ Advanced Features\";\n    public const string Setup = \"🚀 Setup & Configuration\";\n}\n\n/// <summary>\n/// Common example requests and responses for documentation\n/// </summary>\npublic static class DocumentationExamples\n{\n    public static class Email\n    {\n        public const string SendRequest = \"\"\"\n            {\n              \"to\": \"<EMAIL>\",\n              \"from\": \"<EMAIL>\",\n              \"subject\": \"Welcome to NotificationService\",\n              \"htmlBody\": \"<h1>Welcome!</h1><p>Thank you for joining us.</p>\",\n              \"plainTextBody\": \"Welcome! Thank you for joining us.\",\n              \"category\": \"welcome\"\n            }\n            \"\"\";\n\n        public const string SendResponse = \"\"\"\n            {\n              \"success\": true,\n              \"messageId\": \"msg_abc123def456\",\n              \"timestamp\": \"2024-01-15T10:30:00Z\"\n            }\n            \"\"\";\n    }\n\n    public static class SMS\n    {\n        public const string SendRequest = \"\"\"\n            {\n              \"phoneNumber\": \"+1234567890\",\n              \"message\": \"Your verification code is: 123456\",\n              \"from\": \"YourService\"\n            }\n            \"\"\";\n\n        public const string SendResponse = \"\"\"\n            {\n              \"success\": true,\n              \"messageId\": \"sms_xyz789abc123\",\n              \"timestamp\": \"2024-01-15T10:30:00Z\"\n            }\n            \"\"\";\n    }\n\n    public static class Push\n    {\n        public const string SendRequest = \"\"\"\n            {\n              \"deviceToken\": \"device_token_here\",\n              \"title\": \"New Message\",\n              \"message\": \"You have a new notification\",\n              \"imageUrl\": \"https://example.com/image.png\",\n              \"data\": {\n                \"action\": \"open_chat\",\n                \"chatId\": \"chat_123\"\n              }\n            }\n            \"\"\";\n\n        public const string SendResponse = \"\"\"\n            {\n              \"success\": true,\n              \"messageId\": \"push_def456ghi789\",\n              \"timestamp\": \"2024-01-15T10:30:00Z\"\n            }\n            \"\"\";\n    }\n}\n"}]}