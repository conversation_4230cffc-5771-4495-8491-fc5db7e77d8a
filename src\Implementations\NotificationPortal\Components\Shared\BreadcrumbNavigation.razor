@inject IBreadcrumbService BreadcrumbService
@inject NavigationManager Navigation
@implements IDisposable

@if (BreadcrumbService.CurrentBreadcrumbs.Any())
{
    <MudBreadcrumbs Items="_breadcrumbItems" Class="breadcrumb-navigation">
        <ItemTemplate Context="item">
            @if (item.Href != null && !item.IsActive)
            {
                <MudLink Href="@item.Href" Class="breadcrumb-link">
                    @if (!string.IsNullOrEmpty(item.Icon))
                    {
                        <MudIcon Icon="@item.Icon" Size="Size.Small" Class="mr-1" />
                    }
                    @item.Text
                </MudLink>
            }
            else
            {
                <span class="breadcrumb-current">
                    @if (!string.IsNullOrEmpty(item.Icon))
                    {
                        <MudIcon Icon="@item.Icon" Size="Size.Small" Class="mr-1" />
                    }
                    @item.Text
                </span>
            }
        </ItemTemplate>
        <SeparatorTemplate>
            <MudIcon Icon="@Icons.Material.Filled.ChevronRight" Size="Size.Small" Class="breadcrumb-separator" />
        </SeparatorTemplate>
    </MudBreadcrumbs>
}

@code {
    private List<BreadcrumbItem> _breadcrumbItems = new();

    protected override void OnInitialized()
    {
        UpdateBreadcrumbs();
        BreadcrumbService.OnBreadcrumbChanged += UpdateBreadcrumbs;
    }

    private void UpdateBreadcrumbs()
    {
        _breadcrumbItems = BreadcrumbService.CurrentBreadcrumbs.Select(b => new BreadcrumbItem
        {
            Text = b.Text,
            Href = b.Href,
            Icon = b.Icon,
            IsActive = b.IsActive
        }).ToList();
        
        StateHasChanged();
    }

    public void Dispose()
    {
        BreadcrumbService.OnBreadcrumbChanged -= UpdateBreadcrumbs;
    }
}

<style>
    .breadcrumb-navigation {
        padding: 8px 0;
        margin-bottom: 16px;
    }

    .breadcrumb-link {
        color: var(--mud-palette-text-secondary);
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: color 0.2s ease;
    }

    .breadcrumb-link:hover {
        color: var(--mud-palette-primary);
        text-decoration: none;
    }

    .breadcrumb-current {
        color: var(--mud-palette-text-primary);
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    .breadcrumb-separator {
        color: var(--mud-palette-text-secondary);
        margin: 0 8px;
    }
</style>
