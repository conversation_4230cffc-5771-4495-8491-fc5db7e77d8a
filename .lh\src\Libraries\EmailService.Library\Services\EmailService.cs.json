{"sourceFile": "src/Libraries/EmailService.Library/Services/EmailService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1751191331255, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751191343311, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -75,17 +75,17 @@\n             Status = \"Delivered\"\r\n         });\r\n     }\r\n \r\n-    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string emailAddress)\r\n+    public Task<MessageHistoryResponse> GetMessageHistoryAsync(string emailAddress)\r\n     {\r\n         // Implementation would depend on email provider capabilities\r\n-        return new MessageHistoryResponse\r\n+        return Task.FromResult(new MessageHistoryResponse\r\n         {\r\n             IsSuccess = true,\r\n             Messages = new List<MessageHistoryItem>(),\r\n             TotalCount = 0\r\n-        };\r\n+        });\r\n     }\r\n \r\n     public async Task<EmailResponse> ResendMessageAsync(string messageId)\r\n     {\r\n"}, {"date": 1751191355524, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -86,12 +86,12 @@\n             TotalCount = 0\r\n         });\r\n     }\r\n \r\n-    public async Task<EmailResponse> ResendMessageAsync(string messageId)\r\n+    public Task<EmailResponse> ResendMessageAsync(string messageId)\r\n     {\r\n         // Implementation would depend on email provider capabilities\r\n-        return new EmailResponse(false, errorMessage: \"Resend not implemented\");\r\n+        return Task.FromResult(new EmailResponse(false, errorMessage: \"Resend not implemented\"));\r\n     }\r\n \r\n     // Admin functionality implementations\r\n     public async Task<object> GetServiceStatusAsync()\r\n"}, {"date": 1751191374002, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -93,17 +93,17 @@\n         return Task.FromResult(new EmailResponse(false, errorMessage: \"Resend not implemented\"));\r\n     }\r\n \r\n     // Admin functionality implementations\r\n-    public async Task<object> GetServiceStatusAsync()\r\n+    public Task<object> GetServiceStatusAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Status = \"Running\",\r\n             Provider = \"SMTP\",\r\n             LastCheck = DateTime.UtcNow,\r\n             IsHealthy = true\r\n-        };\r\n+        });\r\n     }\r\n \r\n     public async Task<object> GetProvidersAsync()\r\n     {\r\n"}, {"date": 1751191388065, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -104,15 +104,15 @@\n             IsHealthy = true\r\n         });\r\n     }\r\n \r\n-    public async Task<object> GetProvidersAsync()\r\n+    public Task<object> GetProvidersAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Providers = new[] { \"SMTP\" },\r\n             ActiveProvider = \"SMTP\"\r\n-        };\r\n+        });\r\n     }\r\n \r\n     public async Task<ServiceResult> ConfigureProviderAsync(string provider, object configuration)\r\n     {\r\n"}, {"date": 1751191413432, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -113,31 +113,31 @@\n             ActiveProvider = \"SMTP\"\r\n         });\r\n     }\r\n \r\n-    public async Task<ServiceResult> ConfigureProviderAsync(string provider, object configuration)\r\n+    public Task<ServiceResult> ConfigureProviderAsync(string provider, object configuration)\r\n     {\r\n-        return new ServiceResult { Success = true };\r\n+        return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n-    public async Task<object> TestProviderAsync(string provider, string? testEmail = null)\r\n+    public Task<object> TestProviderAsync(string provider, string? testEmail = null)\r\n     {\r\n-        return new { Success = true, Message = \"Test email sent successfully\" };\r\n+        return Task.FromResult<object>(new { Success = true, Message = \"Test email sent successfully\" });\r\n     }\r\n \r\n-    public async Task<ServiceResult> UpdateProviderStatusAsync(string provider, bool enabled)\r\n+    public Task<ServiceResult> UpdateProviderStatusAsync(string provider, bool enabled)\r\n     {\r\n-        return new ServiceResult { Success = true };\r\n+        return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n-    public async Task<object> GetConfigurationAsync()\r\n+    public Task<object> GetConfigurationAsync()\r\n     {\r\n-        return new { SmtpHost = _mailSetting.Host, SmtpPort = _mailSetting.Port };\r\n+        return Task.FromResult<object>(new { SmtpHost = _mailSetting.Host, SmtpPort = _mailSetting.Port });\r\n     }\r\n \r\n-    public async Task<ServiceResult> UpdateConfigurationAsync(object configuration)\r\n+    public Task<ServiceResult> UpdateConfigurationAsync(object configuration)\r\n     {\r\n-        return new ServiceResult { Success = true };\r\n+        return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n     public async Task ClearCacheAsync()\r\n     {\r\n"}, {"date": 1751191436624, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -138,21 +138,22 @@\n     {\r\n         return Task.FromResult(new ServiceResult { Success = true });\r\n     }\r\n \r\n-    public async Task ClearCacheAsync()\r\n+    public Task ClearCacheAsync()\r\n     {\r\n         // Clear any cached data\r\n+        return Task.CompletedTask;\r\n     }\r\n \r\n-    public async Task<object> GetQueueStatusAsync()\r\n+    public Task<object> GetQueueStatusAsync()\r\n     {\r\n-        return new { QueueLength = 0, ProcessingCount = 0 };\r\n+        return Task.FromResult<object>(new { QueueLength = 0, ProcessingCount = 0 });\r\n     }\r\n \r\n-    public async Task<ServiceResult> PurgeQueueAsync()\r\n+    public Task<ServiceResult> PurgeQueueAsync()\r\n     {\r\n-        return new ServiceResult { Success = true, PurgedCount = 0 };\r\n+        return Task.FromResult(new ServiceResult { Success = true, PurgedCount = 0 });\r\n     }\r\n \r\n     // Metrics functionality implementations\r\n     public async Task<object> GetSummaryMetricsAsync()\r\n"}, {"date": 1751191450686, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -155,17 +155,17 @@\n         return Task.FromResult(new ServiceResult { Success = true, PurgedCount = 0 });\r\n     }\r\n \r\n     // Metrics functionality implementations\r\n-    public async Task<object> GetSummaryMetricsAsync()\r\n+    public Task<object> GetSummaryMetricsAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             TotalSent = 0,\r\n             SuccessRate = 100.0,\r\n             LastHour = 0,\r\n             LastDay = 0\r\n-        };\r\n+        });\r\n     }\r\n \r\n     public async Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? provider = null)\r\n     {\r\n"}, {"date": 1751191475454, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -166,34 +166,34 @@\n             LastDay = 0\r\n         });\r\n     }\r\n \r\n-    public async Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? provider = null)\r\n+    public Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? provider = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Period = new { Start = startDate, End = endDate },\r\n             Provider = provider,\r\n             Metrics = new { Sent = 0, Delivered = 0, Failed = 0 }\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)\r\n+    public Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Period = new { Start = startDate, End = endDate },\r\n             Errors = new object[0]\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetMonthlyMetricsAsync(int months = 12)\r\n+    public Task<object> GetMonthlyMetricsAsync(int months = 12)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Months = months,\r\n             Data = new object[0]\r\n-        };\r\n+        });\r\n     }\r\n \r\n     public async Task<object> GetPerformanceMetricsAsync()\r\n     {\r\n"}, {"date": 1751191500153, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -194,26 +194,26 @@\n             Data = new object[0]\r\n         });\r\n     }\r\n \r\n-    public async Task<object> GetPerformanceMetricsAsync()\r\n+    public Task<object> GetPerformanceMetricsAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             AverageDeliveryTime = TimeSpan.FromMinutes(2),\r\n             ThroughputPerHour = 100\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetDeliveryRateMetricsAsync(string? provider = null)\r\n+    public Task<object> GetDeliveryRateMetricsAsync(string? provider = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Provider = provider,\r\n             DeliveryRate = 95.5,\r\n             BounceRate = 2.1,\r\n             ComplaintRate = 0.1\r\n-        };\r\n+        });\r\n     }\r\n \r\n     public async Task<object> GetDeliveryMetricsAsync(string? provider = null)\r\n     {\r\n"}, {"date": 1751191523392, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -214,37 +214,37 @@\n             ComplaintRate = 0.1\r\n         });\r\n     }\r\n \r\n-    public async Task<object> GetDeliveryMetricsAsync(string? provider = null)\r\n+    public Task<object> GetDeliveryMetricsAsync(string? provider = null)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Provider = provider,\r\n             TotalSent = 0,\r\n             TotalDelivered = 0,\r\n             DeliveryRate = 100.0\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetProviderMetricsAsync(string provider)\r\n+    public Task<object> GetProviderMetricsAsync(string provider)\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             Provider = provider,\r\n             MessagesSent = 0,\r\n             DeliveryRate = 100.0,\r\n             AverageResponseTime = TimeSpan.FromSeconds(1)\r\n-        };\r\n+        });\r\n     }\r\n \r\n-    public async Task<object> GetMonthlyStatisticsAsync()\r\n+    public Task<object> GetMonthlyStatisticsAsync()\r\n     {\r\n-        return new\r\n+        return Task.FromResult<object>(new\r\n         {\r\n             TotalSent = 0,\r\n             TotalDelivered = 0,\r\n             TotalFailed = 0,\r\n             MonthlyData = new object[0]\r\n-        };\r\n+        });\r\n     }\r\n }\r\n"}], "date": 1751191331255, "name": "Commit-0", "content": "using EmailContract.Models;\r\nusing EmailService.Library.Interfaces;\r\nusing EmailService.Library.Mappers;\r\nusing EmailService.Library.Configuration;\r\nusing Microsoft.Extensions.Options;\r\nusing Microsoft.Extensions.Logging;\r\nusing NotificationContract.Models;\r\n\r\nnamespace EmailService.Library.Services;\r\n\r\npublic sealed class EmailSenderService : IEmailService\r\n{\r\n    private readonly ISmtpClient _smtpClient;\r\n    private readonly MailSetting _mailSetting;\r\n    private readonly ILogger<EmailSenderService> _logger;\r\n\r\n    public EmailSenderService(ISmtpClient smtpClient, IOptions<EmailServiceSettings> config, ILogger<EmailSenderService> logger)\r\n    {\r\n        _smtpClient = smtpClient;\r\n        _mailSetting = config.Value.MailSettings;\r\n        _logger = logger;\r\n    }\r\n\r\n    public async Task<EmailResponse> SendAsync(EmailMessageRequest request)\r\n    {\r\n        try\r\n        {\r\n            if (request is null)\r\n                throw new ArgumentNullException(nameof(request));\r\n\r\n            if (string.IsNullOrWhiteSpace(request.To))\r\n                throw new ArgumentException(\"To email address can't be null\");\r\n\r\n            if (string.IsNullOrWhiteSpace(request.Body))\r\n                throw new ArgumentException(\"Body can't be null\");\r\n\r\n            _logger.LogInformation(\"Sending email to {To} with subject {Subject}\", request.To, request.Subject);\r\n\r\n            await _smtpClient.SendAsync(request.MapToMailMessage(_mailSetting));\r\n\r\n            _logger.LogInformation(\"Email sent successfully to {To}\", request.To);\r\n\r\n            return new EmailResponse(true, messageId: Guid.NewGuid().ToString());\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Failed to send email to {To}\", request.To);\r\n            return new EmailResponse(false, errorMessage: ex.Message);\r\n        }\r\n    }\r\n\r\n    public async Task<BulkEmailResponse> SendBulkAsync(BulkEmailRequest request)\r\n    {\r\n        var results = new List<EmailResponse>();\r\n        \r\n        foreach (var email in request.Messages)\r\n        {\r\n            var result = await SendAsync(email);\r\n            results.Add(result);\r\n        }\r\n\r\n        var successCount = results.Count(r => r.IsSuccess);\r\n        var response = new BulkEmailResponse(successCount == results.Count);\r\n        response.Results = results;\r\n        return response;\r\n    }\r\n\r\n    public Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)\r\n    {\r\n        // Implementation would depend on email provider capabilities\r\n        return Task.FromResult(new MessageStatusResponse\r\n        {\r\n            IsSuccess = true,\r\n            MessageId = messageId,\r\n            Status = \"Delivered\"\r\n        });\r\n    }\r\n\r\n    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string emailAddress)\r\n    {\r\n        // Implementation would depend on email provider capabilities\r\n        return new MessageHistoryResponse\r\n        {\r\n            IsSuccess = true,\r\n            Messages = new List<MessageHistoryItem>(),\r\n            TotalCount = 0\r\n        };\r\n    }\r\n\r\n    public async Task<EmailResponse> ResendMessageAsync(string messageId)\r\n    {\r\n        // Implementation would depend on email provider capabilities\r\n        return new EmailResponse(false, errorMessage: \"Resend not implemented\");\r\n    }\r\n\r\n    // Admin functionality implementations\r\n    public async Task<object> GetServiceStatusAsync()\r\n    {\r\n        return new\r\n        {\r\n            Status = \"Running\",\r\n            Provider = \"SMTP\",\r\n            LastCheck = DateTime.UtcNow,\r\n            IsHealthy = true\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetProvidersAsync()\r\n    {\r\n        return new\r\n        {\r\n            Providers = new[] { \"SMTP\" },\r\n            ActiveProvider = \"SMTP\"\r\n        };\r\n    }\r\n\r\n    public async Task<ServiceResult> ConfigureProviderAsync(string provider, object configuration)\r\n    {\r\n        return new ServiceResult { Success = true };\r\n    }\r\n\r\n    public async Task<object> TestProviderAsync(string provider, string? testEmail = null)\r\n    {\r\n        return new { Success = true, Message = \"Test email sent successfully\" };\r\n    }\r\n\r\n    public async Task<ServiceResult> UpdateProviderStatusAsync(string provider, bool enabled)\r\n    {\r\n        return new ServiceResult { Success = true };\r\n    }\r\n\r\n    public async Task<object> GetConfigurationAsync()\r\n    {\r\n        return new { SmtpHost = _mailSetting.Host, SmtpPort = _mailSetting.Port };\r\n    }\r\n\r\n    public async Task<ServiceResult> UpdateConfigurationAsync(object configuration)\r\n    {\r\n        return new ServiceResult { Success = true };\r\n    }\r\n\r\n    public async Task ClearCacheAsync()\r\n    {\r\n        // Clear any cached data\r\n    }\r\n\r\n    public async Task<object> GetQueueStatusAsync()\r\n    {\r\n        return new { QueueLength = 0, ProcessingCount = 0 };\r\n    }\r\n\r\n    public async Task<ServiceResult> PurgeQueueAsync()\r\n    {\r\n        return new ServiceResult { Success = true, PurgedCount = 0 };\r\n    }\r\n\r\n    // Metrics functionality implementations\r\n    public async Task<object> GetSummaryMetricsAsync()\r\n    {\r\n        return new\r\n        {\r\n            TotalSent = 0,\r\n            SuccessRate = 100.0,\r\n            LastHour = 0,\r\n            LastDay = 0\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? provider = null)\r\n    {\r\n        return new\r\n        {\r\n            Period = new { Start = startDate, End = endDate },\r\n            Provider = provider,\r\n            Metrics = new { Sent = 0, Delivered = 0, Failed = 0 }\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)\r\n    {\r\n        return new\r\n        {\r\n            Period = new { Start = startDate, End = endDate },\r\n            Errors = new object[0]\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetMonthlyMetricsAsync(int months = 12)\r\n    {\r\n        return new\r\n        {\r\n            Months = months,\r\n            Data = new object[0]\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetPerformanceMetricsAsync()\r\n    {\r\n        return new\r\n        {\r\n            AverageDeliveryTime = TimeSpan.FromMinutes(2),\r\n            ThroughputPerHour = 100\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetDeliveryRateMetricsAsync(string? provider = null)\r\n    {\r\n        return new\r\n        {\r\n            Provider = provider,\r\n            DeliveryRate = 95.5,\r\n            BounceRate = 2.1,\r\n            ComplaintRate = 0.1\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetDeliveryMetricsAsync(string? provider = null)\r\n    {\r\n        return new\r\n        {\r\n            Provider = provider,\r\n            TotalSent = 0,\r\n            TotalDelivered = 0,\r\n            DeliveryRate = 100.0\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetProviderMetricsAsync(string provider)\r\n    {\r\n        return new\r\n        {\r\n            Provider = provider,\r\n            MessagesSent = 0,\r\n            DeliveryRate = 100.0,\r\n            AverageResponseTime = TimeSpan.FromSeconds(1)\r\n        };\r\n    }\r\n\r\n    public async Task<object> GetMonthlyStatisticsAsync()\r\n    {\r\n        return new\r\n        {\r\n            TotalSent = 0,\r\n            TotalDelivered = 0,\r\n            TotalFailed = 0,\r\n            MonthlyData = new object[0]\r\n        };\r\n    }\r\n}\r\n"}]}