using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Base;
using System.Text.Json;
using System.Text;
using System.Diagnostics;

namespace Plugin.Slack;

public class SlackPlugin : BaseNotificationPlugin
{
    private readonly ILogger<SlackPlugin> _logger;
    private readonly HttpClient _httpClient;
    private bool _isInitialized = false;
    private string? _webhookUrl;
    private string? _botToken;
    private string? _defaultChannel;

    public SlackPlugin(ILogger<SlackPlugin> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    public override PluginInfo PluginInfo => new(
        Name: "Slack",
        Version: "1.0.0",
        Description: "Slack messaging plugin for sending messages to Slack channels and users",
        Type: PluginContract.Enums.PluginType.Messaging,
        Author: "NotificationService Team",
        IsEnabled: true
    );

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);

        _webhookUrl = configuration["Slack:WebhookUrl"];
        _botToken = configuration["Slack:BotToken"];
        _defaultChannel = configuration["Slack:DefaultChannel"];

        if (string.IsNullOrEmpty(_webhookUrl) && string.IsNullOrEmpty(_botToken))
        {
            throw new InvalidOperationException("Either Slack Webhook URL or Bot Token is required");
        }

        if (!string.IsNullOrEmpty(_botToken))
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_botToken}");
        }

        _isInitialized = true;
        _logger.LogInformation("Slack plugin initialized successfully");
    }

    public override async Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (!_isInitialized)
                throw new InvalidOperationException("Plugin not initialized");

            if (request is not SlackRequest slackRequest)
                throw new ArgumentException("Invalid request type for Slack plugin");

            var success = false;
            var messageId = Guid.NewGuid().ToString();

            if (!string.IsNullOrEmpty(_webhookUrl))
            {
                success = await SendViaWebhook(slackRequest, cancellationToken);
            }
            else if (!string.IsNullOrEmpty(_botToken))
            {
                success = await SendViaApi(slackRequest, cancellationToken);
            }

            stopwatch.Stop();
            var responseTimeMs = stopwatch.Elapsed.TotalMilliseconds;

            if (success)
            {
                TrackRequest(true, responseTimeMs, slackRequest.Message.Length);
                _logger.LogInformation("Slack message sent successfully. MessageId: {MessageId}", messageId);
                return new SlackResponse(true, MessageId: messageId);
            }
            else
            {
                TrackRequest(false, responseTimeMs, 0, "SendFailed");
                return new SlackResponse(false, ErrorMessage: "Failed to send Slack message");
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            TrackRequest(false, stopwatch.Elapsed.TotalMilliseconds, 0, ex.GetType().Name);
            _logger.LogError(ex, "Error sending Slack message");
            return new SlackResponse(false, ErrorMessage: ex.Message);
        }
    }

    private async Task<bool> SendViaWebhook(SlackRequest request, CancellationToken cancellationToken)
    {
        var payload = new
        {
            text = request.Message,
            channel = request.Channel ?? _defaultChannel,
            username = request.Username ?? "NotificationService",
            icon_emoji = request.IconEmoji ?? ":robot_face:",
            attachments = request.Attachments
        };

        var json = JsonSerializer.Serialize(payload);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync(_webhookUrl, content, cancellationToken);
        return response.IsSuccessStatusCode;
    }

    private async Task<bool> SendViaApi(SlackRequest request, CancellationToken cancellationToken)
    {
        var payload = new
        {
            channel = request.Channel ?? _defaultChannel,
            text = request.Message,
            username = request.Username,
            icon_emoji = request.IconEmoji,
            attachments = request.Attachments
        };

        var json = JsonSerializer.Serialize(payload);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync("https://slack.com/api/chat.postMessage", content, cancellationToken);
        
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
            return responseData.GetProperty("ok").GetBoolean();
        }

        return false;
    }

    public override async Task<bool> HealthCheckAsync()
    {
        try
        {
            if (!_isInitialized) return false;

            if (!string.IsNullOrEmpty(_botToken))
            {
                var response = await _httpClient.GetAsync("https://slack.com/api/auth.test");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonSerializer.Deserialize<JsonElement>(content);
                    return data.GetProperty("ok").GetBoolean();
                }
            }

            return true; // For webhook, we can't easily test without sending a message
        }
        catch
        {
            return false;
        }
    }

    protected override string GetProviderName() => "Slack";

    protected override List<ConfigurationField> GetConfigurationFields()
    {
        return new List<ConfigurationField>
        {
            new("WebhookUrl", "string", "Slack Incoming Webhook URL", false, true),
            new("BotToken", "string", "Slack Bot User OAuth Token", false, true),
            new("DefaultChannel", "string", "Default channel for messages", false, false, "#general"),
            new("Username", "string", "Default bot username", false, false, "NotificationService"),
            new("IconEmoji", "string", "Default bot icon emoji", false, false, ":robot_face:")
        };
    }

    protected override Dictionary<string, object> GetCustomMetrics()
    {
        return new Dictionary<string, object>
        {
            ["provider"] = "Slack",
            ["method"] = !string.IsNullOrEmpty(_webhookUrl) ? "webhook" : "api",
            ["default_channel"] = _defaultChannel ?? "not_configured",
            ["last_health_check"] = DateTime.UtcNow
        };
    }
}

// Slack-specific request and response models
public record SlackRequest(
    string Message,
    string? Channel = null,
    string? Username = null,
    string? IconEmoji = null,
    object[]? Attachments = null,
    Dictionary<string, object>? Metadata = null
) : NotificationRequest(Message, Metadata);

public record SlackResponse(
    bool IsSuccess,
    string? MessageId = null,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
) : NotificationResponse(IsSuccess, ErrorMessage, ResponseData);
