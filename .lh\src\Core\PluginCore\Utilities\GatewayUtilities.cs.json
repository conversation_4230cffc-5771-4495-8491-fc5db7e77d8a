{"sourceFile": "src/Core/PluginCore/Utilities/GatewayUtilities.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751213674974, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751214047115, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,8 @@\n using PluginCore.Models;\n using Microsoft.Extensions.Logging;\n using System.Net;\n+using System.Net.Sockets;\n using System.Text.Json;\n \n namespace PluginCore.Utilities;\n \n"}, {"date": 1751214203810, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -266,11 +266,11 @@\n     public static UsageMetrics CreateMockUsageMetrics(DateTimeOffset from, DateTimeOffset to)\n     {\n         var random = new Random();\n         return new UsageMetrics(\n-            TotalMessages: random.Next(100, 1000),\n-            SuccessfulMessages: random.Next(80, 95),\n-            FailedMessages: random.Next(5, 20),\n-            EstimatedCost: random.NextDouble() * 100\n+            MessagesSent: random.Next(80, 95),\n+            MessagesFailed: random.Next(5, 20),\n+            MessagesQueued: random.Next(0, 10),\n+            EstimatedCost: (decimal)(random.NextDouble() * 100)\n         );\n     }\n }\n"}], "date": 1751213674974, "name": "Commit-0", "content": "using PluginCore.Models;\nusing Microsoft.Extensions.Logging;\nusing System.Net;\nusing System.Text.Json;\n\nnamespace PluginCore.Utilities;\n\n/// <summary>\n/// Utility class providing common functionality for gateway implementations.\n/// </summary>\npublic static class GatewayUtilities\n{\n    /// <summary>\n    /// Executes an operation with retry logic.\n    /// </summary>\n    public static async Task<T> ExecuteWithRetryAsync<T>(\n        Func<Task<T>> operation,\n        int maxRetries = 3,\n        TimeSpan? delay = null,\n        ILogger? logger = null,\n        CancellationToken cancellationToken = default)\n    {\n        var actualDelay = delay ?? TimeSpan.FromSeconds(1);\n        var lastException = default(Exception);\n\n        for (int attempt = 0; attempt <= maxRetries; attempt++)\n        {\n            try\n            {\n                return await operation();\n            }\n            catch (Exception ex) when (attempt < maxRetries && IsRetriableException(ex))\n            {\n                lastException = ex;\n                logger?.LogWarning(ex, \"Operation failed on attempt {Attempt}/{MaxRetries}. Retrying in {Delay}ms\", \n                    attempt + 1, maxRetries + 1, actualDelay.TotalMilliseconds);\n                \n                await Task.Delay(actualDelay, cancellationToken);\n                actualDelay = TimeSpan.FromMilliseconds(actualDelay.TotalMilliseconds * 1.5); // Exponential backoff\n            }\n        }\n\n        throw lastException ?? new InvalidOperationException(\"Operation failed after all retry attempts\");\n    }\n\n    /// <summary>\n    /// Determines if an exception is retriable.\n    /// </summary>\n    public static bool IsRetriableException(Exception exception)\n    {\n        return exception switch\n        {\n            HttpRequestException httpEx => IsRetriableHttpStatusCode(httpEx),\n            TaskCanceledException => true,\n            TimeoutException => true,\n            SocketException => true,\n            _ => false\n        };\n    }\n\n    /// <summary>\n    /// Determines if an HTTP status code is retriable.\n    /// </summary>\n    public static bool IsRetriableHttpStatusCode(HttpRequestException httpException)\n    {\n        // Check if the exception message contains status codes that are retriable\n        var message = httpException.Message.ToLowerInvariant();\n        return message.Contains(\"500\") || // Internal Server Error\n               message.Contains(\"502\") || // Bad Gateway\n               message.Contains(\"503\") || // Service Unavailable\n               message.Contains(\"504\") || // Gateway Timeout\n               message.Contains(\"429\");   // Too Many Requests\n    }\n\n    /// <summary>\n    /// Creates a standardized error result for failed operations.\n    /// </summary>\n    public static MessageSendResult CreateFailureResult(string? correlationId = null, string? errorMessage = null)\n    {\n        return new MessageSendResult(\n            MessageId: Guid.NewGuid().ToString(),\n            Timestamp: DateTimeOffset.UtcNow,\n            Status: \"Failed\",\n            CorrelationId: correlationId\n        );\n    }\n\n    /// <summary>\n    /// Creates a standardized success result for successful operations.\n    /// </summary>\n    public static MessageSendResult CreateSuccessResult(string messageId, string? correlationId = null)\n    {\n        return new MessageSendResult(\n            MessageId: messageId,\n            Timestamp: DateTimeOffset.UtcNow,\n            Status: \"Sent\",\n            CorrelationId: correlationId\n        );\n    }\n\n    /// <summary>\n    /// Validates configuration values and returns validation errors.\n    /// </summary>\n    public static List<string> ValidateConfiguration(Dictionary<string, object> configuration, Dictionary<string, bool> requiredKeys)\n    {\n        var errors = new List<string>();\n\n        foreach (var (key, isRequired) in requiredKeys)\n        {\n            if (isRequired && (!configuration.ContainsKey(key) || \n                configuration[key] == null || \n                string.IsNullOrWhiteSpace(configuration[key].ToString())))\n            {\n                errors.Add($\"Required configuration '{key}' is missing or empty\");\n            }\n        }\n\n        return errors;\n    }\n\n    /// <summary>\n    /// Safely gets a configuration value with type conversion.\n    /// </summary>\n    public static T? GetConfigurationValue<T>(Dictionary<string, object> configuration, string key, T? defaultValue = default)\n    {\n        if (!configuration.TryGetValue(key, out var value) || value == null)\n            return defaultValue;\n\n        try\n        {\n            if (value is T directValue)\n                return directValue;\n\n            if (typeof(T) == typeof(string))\n                return (T)(object)value.ToString()!;\n\n            if (typeof(T) == typeof(int) && int.TryParse(value.ToString(), out var intValue))\n                return (T)(object)intValue;\n\n            if (typeof(T) == typeof(bool) && bool.TryParse(value.ToString(), out var boolValue))\n                return (T)(object)boolValue;\n\n            if (typeof(T) == typeof(double) && double.TryParse(value.ToString(), out var doubleValue))\n                return (T)(object)doubleValue;\n\n            // Try JSON deserialization for complex types\n            if (value is string jsonString)\n            {\n                return JsonSerializer.Deserialize<T>(jsonString);\n            }\n\n            return defaultValue;\n        }\n        catch\n        {\n            return defaultValue;\n        }\n    }\n\n    /// <summary>\n    /// Creates a basic HTTP client with common settings.\n    /// </summary>\n    public static HttpClient CreateHttpClient(string? baseAddress = null, TimeSpan? timeout = null)\n    {\n        var client = new HttpClient();\n        \n        if (!string.IsNullOrEmpty(baseAddress))\n            client.BaseAddress = new Uri(baseAddress);\n            \n        client.Timeout = timeout ?? TimeSpan.FromSeconds(30);\n        client.DefaultRequestHeaders.Add(\"User-Agent\", \"NotificationService/2.0\");\n        \n        return client;\n    }\n\n    /// <summary>\n    /// Sanitizes sensitive data from configuration for logging.\n    /// </summary>\n    public static Dictionary<string, object> SanitizeConfiguration(Dictionary<string, object> configuration, string[] sensitiveKeys)\n    {\n        var sanitized = new Dictionary<string, object>(configuration);\n        \n        foreach (var key in sensitiveKeys)\n        {\n            if (sanitized.ContainsKey(key))\n            {\n                var value = sanitized[key]?.ToString();\n                if (!string.IsNullOrEmpty(value))\n                {\n                    sanitized[key] = value.Length > 4 ? \n                        $\"{value[..2]}***{value[^2..]}\" : \n                        \"***\";\n                }\n            }\n        }\n        \n        return sanitized;\n    }\n\n    /// <summary>\n    /// Generates a correlation ID if one is not provided.\n    /// </summary>\n    public static string GenerateCorrelationId(string? existingId = null)\n    {\n        return existingId ?? $\"gw_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid():N}\";\n    }\n\n    /// <summary>\n    /// Validates a URL format.\n    /// </summary>\n    public static bool IsValidUrl(string url)\n    {\n        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&\n               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);\n    }\n\n    /// <summary>\n    /// Truncates text to a maximum length with ellipsis.\n    /// </summary>\n    public static string TruncateText(string text, int maxLength, string ellipsis = \"...\")\n    {\n        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)\n            return text;\n\n        return text[..(maxLength - ellipsis.Length)] + ellipsis;\n    }\n\n    /// <summary>\n    /// Creates a standardized operation result.\n    /// </summary>\n    public static OperationResult CreateOperationResult(bool success, string? message = null, Exception? exception = null)\n    {\n        var errors = exception != null ? \n            new[] { new ErrorDetail(\"OperationError\", exception.Message) } : \n            null;\n\n        return new OperationResult(success, message, errors);\n    }\n\n    /// <summary>\n    /// Parses a comma-separated string into a list.\n    /// </summary>\n    public static List<string> ParseCommaSeparatedString(string? input)\n    {\n        if (string.IsNullOrWhiteSpace(input))\n            return new List<string>();\n\n        return input.Split(',', StringSplitOptions.RemoveEmptyEntries)\n                   .Select(s => s.Trim())\n                   .Where(s => !string.IsNullOrEmpty(s))\n                   .ToList();\n    }\n\n    /// <summary>\n    /// Creates a mock delivery result for testing purposes.\n    /// </summary>\n    public static DeliveryResult CreateMockDeliveryResult(string messageId, string status = \"Delivered\")\n    {\n        return new DeliveryResult(messageId, status, DateTimeOffset.UtcNow);\n    }\n\n    /// <summary>\n    /// Creates a mock usage metrics for testing purposes.\n    /// </summary>\n    public static UsageMetrics CreateMockUsageMetrics(DateTimeOffset from, DateTimeOffset to)\n    {\n        var random = new Random();\n        return new UsageMetrics(\n            TotalMessages: random.Next(100, 1000),\n            SuccessfulMessages: random.Next(80, 95),\n            FailedMessages: random.Next(5, 20),\n            EstimatedCost: random.NextDouble() * 100\n        );\n    }\n}\n"}]}