using Microsoft.Extensions.Logging;
using NotifyMasterApi.Infrastructure;
using Azure.Storage.Blobs;
using Amazon.S3;
using Amazon.S3.Model;

namespace NotifyMasterApi.Services;

public interface IStorageAdapterService
{
    Task<bool> StoreAsync(string key, string content, string? contentType = null);
    Task<string?> RetrieveAsync(string key);
    Task<bool> DeleteAsync(string key);
    Task<List<string>> ListKeysAsync(string? prefix = null);
    Task<bool> ExistsAsync(string key);
}

public class InMemoryStorageAdapter : IStorageAdapterService
{
    private readonly Dictionary<string, string> _storage = new();
    private readonly ILogger<InMemoryStorageAdapter> _logger;

    public InMemoryStorageAdapter(ILogger<InMemoryStorageAdapter> logger)
    {
        _logger = logger;
    }

    public async Task<bool> StoreAsync(string key, string content, string? contentType = null)
    {
        try
        {
            _storage[key] = content;
            _logger.LogDebug("Stored content with key: {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store content with key: {Key}", key);
            return false;
        }
    }

    public async Task<string?> RetrieveAsync(string key)
    {
        try
        {
            _storage.TryGetValue(key, out var content);
            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve content with key: {Key}", key);
            return null;
        }
    }

    public async Task<bool> DeleteAsync(string key)
    {
        try
        {
            var removed = _storage.Remove(key);
            if (removed)
            {
                _logger.LogDebug("Deleted content with key: {Key}", key);
            }
            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete content with key: {Key}", key);
            return false;
        }
    }

    public async Task<List<string>> ListKeysAsync(string? prefix = null)
    {
        try
        {
            var keys = _storage.Keys.AsEnumerable();
            
            if (!string.IsNullOrEmpty(prefix))
            {
                keys = keys.Where(k => k.StartsWith(prefix));
            }
            
            return keys.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list keys with prefix: {Prefix}", prefix);
            return new List<string>();
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        return _storage.ContainsKey(key);
    }
}

public class AzureBlobStorageAdapter : IStorageAdapterService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly string _containerName;
    private readonly ILogger<AzureBlobStorageAdapter> _logger;

    public AzureBlobStorageAdapter(string connectionString, string containerName, ILogger<AzureBlobStorageAdapter> logger)
    {
        _blobServiceClient = new BlobServiceClient(connectionString);
        _containerName = containerName;
        _logger = logger;
    }

    public async Task<bool> StoreAsync(string key, string content, string? contentType = null)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            await containerClient.CreateIfNotExistsAsync();
            
            var blobClient = containerClient.GetBlobClient(key);
            
            using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(content));
            await blobClient.UploadAsync(stream, overwrite: true);
            
            _logger.LogDebug("Stored blob with key: {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store blob with key: {Key}", key);
            return false;
        }
    }

    public async Task<string?> RetrieveAsync(string key)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var blobClient = containerClient.GetBlobClient(key);
            
            if (!await blobClient.ExistsAsync())
            {
                return null;
            }
            
            var response = await blobClient.DownloadContentAsync();
            return response.Value.Content.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve blob with key: {Key}", key);
            return null;
        }
    }

    public async Task<bool> DeleteAsync(string key)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var blobClient = containerClient.GetBlobClient(key);
            
            var response = await blobClient.DeleteIfExistsAsync();
            return response.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete blob with key: {Key}", key);
            return false;
        }
    }

    public async Task<List<string>> ListKeysAsync(string? prefix = null)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var keys = new List<string>();
            
            await foreach (var blobItem in containerClient.GetBlobsAsync(prefix: prefix))
            {
                keys.Add(blobItem.Name);
            }
            
            return keys;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list blobs with prefix: {Prefix}", prefix);
            return new List<string>();
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var blobClient = containerClient.GetBlobClient(key);
            
            var response = await blobClient.ExistsAsync();
            return response.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if blob exists with key: {Key}", key);
            return false;
        }
    }
}

public class S3StorageAdapter : IStorageAdapterService
{
    private readonly IAmazonS3 _s3Client;
    private readonly string _bucketName;
    private readonly ILogger<S3StorageAdapter> _logger;

    public S3StorageAdapter(IAmazonS3 s3Client, string bucketName, ILogger<S3StorageAdapter> logger)
    {
        _s3Client = s3Client;
        _bucketName = bucketName;
        _logger = logger;
    }

    public async Task<bool> StoreAsync(string key, string content, string? contentType = null)
    {
        try
        {
            var request = new PutObjectRequest
            {
                BucketName = _bucketName,
                Key = key,
                ContentBody = content,
                ContentType = contentType ?? "text/plain"
            };
            
            await _s3Client.PutObjectAsync(request);
            _logger.LogDebug("Stored S3 object with key: {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store S3 object with key: {Key}", key);
            return false;
        }
    }

    public async Task<string?> RetrieveAsync(string key)
    {
        try
        {
            var request = new GetObjectRequest
            {
                BucketName = _bucketName,
                Key = key
            };
            
            using var response = await _s3Client.GetObjectAsync(request);
            using var reader = new StreamReader(response.ResponseStream);
            return await reader.ReadToEndAsync();
        }
        catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve S3 object with key: {Key}", key);
            return null;
        }
    }

    public async Task<bool> DeleteAsync(string key)
    {
        try
        {
            var request = new DeleteObjectRequest
            {
                BucketName = _bucketName,
                Key = key
            };
            
            await _s3Client.DeleteObjectAsync(request);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete S3 object with key: {Key}", key);
            return false;
        }
    }

    public async Task<List<string>> ListKeysAsync(string? prefix = null)
    {
        try
        {
            var request = new ListObjectsV2Request
            {
                BucketName = _bucketName,
                Prefix = prefix
            };
            
            var keys = new List<string>();
            ListObjectsV2Response response;
            
            do
            {
                response = await _s3Client.ListObjectsV2Async(request);
                keys.AddRange(response.S3Objects.Select(obj => obj.Key));
                request.ContinuationToken = response.NextContinuationToken;
            }
            while (response.IsTruncated);
            
            return keys;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list S3 objects with prefix: {Prefix}", prefix);
            return new List<string>();
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            var request = new GetObjectMetadataRequest
            {
                BucketName = _bucketName,
                Key = key
            };
            
            await _s3Client.GetObjectMetadataAsync(request);
            return true;
        }
        catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if S3 object exists with key: {Key}", key);
            return false;
        }
    }
}

public interface IArchivalService
{
    Task<bool> ArchiveMessageAsync(string messageId, string content, string tenantId, string channel);
    Task<string?> RetrieveArchivedMessageAsync(string messageId);
    Task<bool> DeleteArchivedMessageAsync(string messageId);
    Task<List<string>> GetArchivedMessageIdsAsync(string? tenantId = null, string? channel = null);
    Task ProcessExpiredMessagesAsync();
    Task<ArchivalStats> GetArchivalStatsAsync(string? tenantId = null);
}

public class ArchivalStats
{
    public int TotalArchivedMessages { get; set; }
    public int MessagesArchivedToday { get; set; }
    public long TotalArchivalSizeBytes { get; set; }
    public Dictionary<string, int> ArchivalByChannel { get; set; } = new();
    public DateTime? OldestArchivedMessage { get; set; }
    public DateTime? NewestArchivedMessage { get; set; }
}

public class ArchivalService : IArchivalService
{
    private readonly IStorageAdapterService _storageAdapter;
    private readonly ILogger<ArchivalService> _logger;
    private readonly Dictionary<string, ArchivalConfig> _archivalConfigs = new();
    private readonly Dictionary<string, DateTime> _messageTimestamps = new();

    public ArchivalService(IStorageAdapterService storageAdapter, ILogger<ArchivalService> logger)
    {
        _storageAdapter = storageAdapter;
        _logger = logger;
        
        // Initialize default archival configs
        InitializeDefaultConfigs();
    }

    public async Task<bool> ArchiveMessageAsync(string messageId, string content, string tenantId, string channel)
    {
        try
        {
            var archivalKey = GenerateArchivalKey(messageId, tenantId, channel);
            var success = await _storageAdapter.StoreAsync(archivalKey, content, "application/json");
            
            if (success)
            {
                _messageTimestamps[messageId] = DateTime.UtcNow;
                _logger.LogInformation("Archived message {MessageId} for tenant {TenantId}, channel {Channel}", 
                    messageId, tenantId, channel);
            }
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to archive message {MessageId}", messageId);
            return false;
        }
    }

    public async Task<string?> RetrieveArchivedMessageAsync(string messageId)
    {
        try
        {
            // In a real implementation, we'd need to know the tenant and channel
            // For now, we'll search through possible keys
            var keys = await _storageAdapter.ListKeysAsync($"archived/");
            var matchingKey = keys.FirstOrDefault(k => k.Contains(messageId));
            
            if (matchingKey != null)
            {
                return await _storageAdapter.RetrieveAsync(matchingKey);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve archived message {MessageId}", messageId);
            return null;
        }
    }

    public async Task<bool> DeleteArchivedMessageAsync(string messageId)
    {
        try
        {
            var keys = await _storageAdapter.ListKeysAsync($"archived/");
            var matchingKey = keys.FirstOrDefault(k => k.Contains(messageId));
            
            if (matchingKey != null)
            {
                var success = await _storageAdapter.DeleteAsync(matchingKey);
                if (success)
                {
                    _messageTimestamps.Remove(messageId);
                    _logger.LogInformation("Deleted archived message {MessageId}", messageId);
                }
                return success;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete archived message {MessageId}", messageId);
            return false;
        }
    }

    public async Task<List<string>> GetArchivedMessageIdsAsync(string? tenantId = null, string? channel = null)
    {
        try
        {
            var prefix = "archived/";
            if (!string.IsNullOrEmpty(tenantId))
            {
                prefix += $"{tenantId}/";
                if (!string.IsNullOrEmpty(channel))
                {
                    prefix += $"{channel}/";
                }
            }
            
            var keys = await _storageAdapter.ListKeysAsync(prefix);
            
            // Extract message IDs from keys
            var messageIds = keys.Select(k =>
            {
                var parts = k.Split('/');
                return parts.Length > 0 ? parts.Last().Replace(".json", "") : "";
            }).Where(id => !string.IsNullOrEmpty(id)).ToList();
            
            return messageIds;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get archived message IDs");
            return new List<string>();
        }
    }

    public async Task ProcessExpiredMessagesAsync()
    {
        try
        {
            var expiredMessages = new List<string>();
            var now = DateTime.UtcNow;
            
            foreach (var config in _archivalConfigs.Values.Where(c => c.AutoArchive))
            {
                var cutoffDate = now.AddDays(-config.RetentionDays);
                var expiredInConfig = _messageTimestamps
                    .Where(kvp => kvp.Value < cutoffDate)
                    .Select(kvp => kvp.Key)
                    .ToList();
                
                expiredMessages.AddRange(expiredInConfig);
            }
            
            foreach (var messageId in expiredMessages.Distinct())
            {
                await DeleteArchivedMessageAsync(messageId);
            }
            
            if (expiredMessages.Any())
            {
                _logger.LogInformation("Processed {Count} expired messages", expiredMessages.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process expired messages");
        }
    }

    public async Task<ArchivalStats> GetArchivalStatsAsync(string? tenantId = null)
    {
        try
        {
            var messageIds = await GetArchivedMessageIdsAsync(tenantId);
            var today = DateTime.UtcNow.Date;
            
            var stats = new ArchivalStats
            {
                TotalArchivedMessages = messageIds.Count,
                MessagesArchivedToday = _messageTimestamps.Values.Count(t => t.Date == today),
                TotalArchivalSizeBytes = messageIds.Count * 1024, // Estimate
                ArchivalByChannel = new Dictionary<string, int>
                {
                    ["email"] = messageIds.Count / 3,
                    ["sms"] = messageIds.Count / 3,
                    ["push"] = messageIds.Count / 3
                }
            };
            
            if (_messageTimestamps.Any())
            {
                stats.OldestArchivedMessage = _messageTimestamps.Values.Min();
                stats.NewestArchivedMessage = _messageTimestamps.Values.Max();
            }
            
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get archival stats");
            return new ArchivalStats();
        }
    }

    private string GenerateArchivalKey(string messageId, string tenantId, string channel)
    {
        var date = DateTime.UtcNow.ToString("yyyy/MM/dd");
        return $"archived/{tenantId}/{channel}/{date}/{messageId}.json";
    }

    private void InitializeDefaultConfigs()
    {
        _archivalConfigs["default:email"] = new ArchivalConfig
        {
            TenantId = "default",
            Channel = "email",
            RetentionDays = 90,
            AutoArchive = true,
            ArchivalProvider = "InMemory"
        };
        
        _archivalConfigs["default:sms"] = new ArchivalConfig
        {
            TenantId = "default",
            Channel = "sms",
            RetentionDays = 30,
            AutoArchive = true,
            ArchivalProvider = "InMemory"
        };
        
        _archivalConfigs["default:push"] = new ArchivalConfig
        {
            TenantId = "default",
            Channel = "push",
            RetentionDays = 7,
            AutoArchive = true,
            ArchivalProvider = "InMemory"
        };
    }
}
