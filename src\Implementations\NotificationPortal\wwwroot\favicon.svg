<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="none">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="16" cy="16" r="14" fill="url(#grad1)" filter="url(#shadow)"/>
  
  <!-- Notification Bell -->
  <path d="M16 4c-3.5 0-6.5 2.8-6.5 6.2v3.3c0 1.2-.5 2.3-1.3 3.1l-.7.7c-.4.4-.4 1 0 1.4.2.2.5.3.7.3h15.6c.3 0 .5-.1.7-.3.4-.4.4-1 0-1.4l-.7-.7c-.8-.8-1.3-1.9-1.3-3.1v-3.3C22.5 6.8 19.5 4 16 4z" fill="white"/>
  
  <!-- Bell Clapper -->
  <circle cx="16" cy="12" r="1.5" fill="white" opacity="0.8"/>
  
  <!-- Notification Dot -->
  <circle cx="22" cy="8" r="3" fill="#ef4444"/>
  <circle cx="22" cy="8" r="2" fill="#fca5a5"/>
  
  <!-- Shine Effect -->
  <ellipse cx="13" cy="9" rx="2" ry="3" fill="white" opacity="0.3" transform="rotate(-20 13 9)"/>
</svg>
