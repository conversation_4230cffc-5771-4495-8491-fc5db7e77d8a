{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Plugins/GetPlugins.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1751212229406, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751212250608, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -106,32 +106,46 @@\n     public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)\r\n     {\r\n         try\r\n         {\r\n-            var plugins = await _pluginManager.GetLoadedPluginsAsync();\r\n-            \r\n-            var filteredPlugins = plugins.AsEnumerable();\r\n-            \r\n+            var statuses = await _pluginManager.GetPluginStatusesAsync(ct);\r\n+            var manifests = _pluginManager.GetPluginManifests();\r\n+\r\n+            var filteredStatuses = statuses.AsEnumerable();\r\n+\r\n             if (!string.IsNullOrEmpty(req.Type))\r\n             {\r\n-                filteredPlugins = filteredPlugins.Where(p =>\r\n-                    p.Type.ToString().Equals(req.Type, StringComparison.OrdinalIgnoreCase));\r\n+                filteredStatuses = filteredStatuses.Where(s =>\r\n+                    s.Type.Equals(req.Type, StringComparison.OrdinalIgnoreCase));\r\n             }\r\n-            \r\n+\r\n             if (req.IsEnabled.HasValue)\r\n             {\r\n-                filteredPlugins = filteredPlugins.Where(p => p.IsEnabled == req.IsEnabled.Value);\r\n+                filteredStatuses = filteredStatuses.Where(s => s.IsLoaded == req.IsEnabled.Value);\r\n             }\r\n \r\n-            var pluginSummaries = filteredPlugins.Select(p => new PluginSummary\r\n+            if (!string.IsNullOrEmpty(req.Provider))\r\n             {\r\n-                Name = p.Name ?? \"Unknown\",\r\n-                Version = p.Version ?? \"Unknown\",\r\n-                Type = p.Type.ToString(),\r\n-                Provider = p.Provider ?? \"Unknown\",\r\n-                IsEnabled = p.IsEnabled,\r\n-                Status = p.IsEnabled ? \"Active\" : \"Disabled\",\r\n-                LoadedAt = p.LoadedAt\r\n+                filteredStatuses = filteredStatuses.Where(s =>\r\n+                {\r\n+                    var manifest = manifests.FirstOrDefault(m => m.Name == s.Name);\r\n+                    return manifest?.Provider?.Equals(req.Provider, StringComparison.OrdinalIgnoreCase) == true;\r\n+                });\r\n+            }\r\n+\r\n+            var pluginSummaries = filteredStatuses.Select(status =>\r\n+            {\r\n+                var manifest = manifests.FirstOrDefault(m => m.Name == status.Name);\r\n+                return new PluginSummary\r\n+                {\r\n+                    Name = status.Name,\r\n+                    Version = status.Version,\r\n+                    Type = status.Type,\r\n+                    Provider = manifest?.Provider ?? \"Unknown\",\r\n+                    IsEnabled = status.IsLoaded,\r\n+                    Status = status.Status,\r\n+                    LoadedAt = status.LastChecked.DateTime\r\n+                };\r\n             }).ToList();\r\n \r\n             await SendOkAsync(new GetPluginsResponse\r\n             {\r\n"}, {"date": 1751212648428, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,8 @@\n using FastEndpoints;\r\n-using PluginCore.Interfaces;\r\n-using NotifyMasterApi.Documentation;\r\n-using System.ComponentModel.DataAnnotations;\r\n+using MediatR;\r\n+using NotifyMasterApi.Features.Plugins.Queries;\r\n+using NotifyMasterApi.Models;\r\n \r\n namespace NotifyMasterApi.Features.Plugins;\r\n \r\n /// <summary>\r\n"}, {"date": 1751212667350, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,55 +5,16 @@\n \r\n namespace NotifyMasterApi.Features.Plugins;\r\n \r\n /// <summary>\r\n-/// Request model for retrieving plugins with optional filtering\r\n+/// Simple request for getting plugins\r\n /// </summary>\r\n public class GetPluginsRequest\r\n {\r\n-    /// <summary>\r\n-    /// Filter plugins by type (optional)\r\n-    /// </summary>\r\n-    /// <example>Email, SMS, Push, Messaging</example>\r\n     public string? Type { get; set; }\r\n-\r\n-    /// <summary>\r\n-    /// Filter plugins by enabled status (optional)\r\n-    /// </summary>\r\n-    /// <example>true</example>\r\n-    public bool? IsEnabled { get; set; }\r\n-\r\n-    /// <summary>\r\n-    /// Filter plugins by provider (optional)\r\n-    /// </summary>\r\n-    /// <example>SendGrid, <PERSON><PERSON><PERSON>, FCM</example>\r\n-    public string? Provider { get; set; }\r\n-\r\n-    /// <summary>\r\n-    /// Include detailed plugin information\r\n-    /// </summary>\r\n-    /// <example>false</example>\r\n-    public bool IncludeDetails { get; set; } = false;\r\n+    public bool? IsLoaded { get; set; }\r\n }\r\n \r\n-public class GetPluginsResponse\r\n-{\r\n-    public List<PluginSummary> Plugins { get; set; } = new();\r\n-    public int TotalCount { get; set; }\r\n-    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n-}\r\n-\r\n-public class PluginSummary\r\n-{\r\n-    public string Name { get; set; } = string.Empty;\r\n-    public string Version { get; set; } = string.Empty;\r\n-    public string Type { get; set; } = string.Empty;\r\n-    public string Provider { get; set; } = string.Empty;\r\n-    public bool IsEnabled { get; set; }\r\n-    public string Status { get; set; } = string.Empty;\r\n-    public DateTime? LoadedAt { get; set; }\r\n-}\r\n-\r\n public class GetPluginsEndpoint : Endpoint<GetPluginsRequest, GetPluginsResponse>\r\n {\r\n     private readonly IPluginManager _pluginManager;\r\n     private readonly ILogger<GetPluginsEndpoint> _logger;\r\n"}, {"date": 1751212682080, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,17 +13,15 @@\n     public string? Type { get; set; }\r\n     public bool? IsLoaded { get; set; }\r\n }\r\n \r\n-public class GetPluginsEndpoint : Endpoint<GetPluginsRequest, GetPluginsResponse>\r\n+public class GetPluginsEndpoint : Endpoint<GetPluginsRequest, PluginListResponse>\r\n {\r\n-    private readonly IPluginManager _pluginManager;\r\n-    private readonly ILogger<GetPluginsEndpoint> _logger;\r\n+    private readonly IMediator _mediator;\r\n \r\n-    public GetPluginsEndpoint(IPluginManager pluginManager, ILogger<GetPluginsEndpoint> logger)\r\n+    public GetPluginsEndpoint(IMediator mediator)\r\n     {\r\n-        _pluginManager = pluginManager;\r\n-        _logger = logger;\r\n+        _mediator = mediator;\r\n     }\r\n \r\n     public override void Configure()\r\n     {\r\n"}, {"date": 1751212704521, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,43 +24,16 @@\n     }\r\n \r\n     public override void Configure()\r\n     {\r\n-        this.ConfigurePluginEndpoint(\r\n-            \"GET\",\r\n-            \"/api/plugins\",\r\n-            \"Get All Plugins\",\r\n-            \"Retrieve a comprehensive list of all loaded plugins with their status, configuration, and capabilities.\\n\\n\" +\r\n-            \"## 🔌 Plugin Information\\n\" +\r\n-            \"- **Plugin Details**: Name, version, provider, and type\\n\" +\r\n-            \"- **Status Information**: Enabled/disabled state and health\\n\" +\r\n-            \"- **Configuration**: Current settings and capabilities\\n\" +\r\n-            \"- **Metrics**: Performance and usage statistics\\n\" +\r\n-            \"- **Dependencies**: Required services and connections\\n\\n\" +\r\n-            \"## 📋 Plugin Types\\n\" +\r\n-            \"- **Email Plugins**: SendGrid, Mailgun, SMTP, Amazon SES\\n\" +\r\n-            \"- **SMS Plugins**: <PERSON><PERSON><PERSON>, <PERSON><PERSON>ar, Nexmo, Amazon SNS\\n\" +\r\n-            \"- **Push Plugins**: FCM, APNS, OneSignal, Web Push\\n\" +\r\n-            \"- **Messaging Plugins**: WhatsApp, Slack, Teams, Discord\\n\" +\r\n-            \"- **Utility Plugins**: Templates, Analytics, Webhooks\\n\\n\" +\r\n-            \"## 🔍 Filtering Options\\n\" +\r\n-            \"- Filter by plugin type (Email, SMS, Push, etc.)\\n\" +\r\n-            \"- Filter by enabled/disabled status\\n\" +\r\n-            \"- Filter by specific provider\\n\" +\r\n-            \"- Include/exclude detailed information\\n\\n\" +\r\n-            \"## 📊 Plugin Status\\n\" +\r\n-            \"- **Active**: Plugin is loaded and operational\\n\" +\r\n-            \"- **Inactive**: Plugin is loaded but disabled\\n\" +\r\n-            \"- **Error**: Plugin failed to load or has issues\\n\" +\r\n-            \"- **Loading**: Plugin is currently being initialized\\n\\n\" +\r\n-            \"## 🛠️ Management Operations\\n\" +\r\n-            \"Use this endpoint to:\\n\" +\r\n-            \"- Monitor plugin health and status\\n\" +\r\n-            \"- Identify available messaging providers\\n\" +\r\n-            \"- Check plugin configurations\\n\" +\r\n-            \"- Plan plugin management operations\",\r\n-            new[] { \"System Management\", \"Configuration\" }\r\n-        );\r\n+        Get(\"/api/plugins\");\r\n+        AllowAnonymous();\r\n+        Summary(s =>\r\n+        {\r\n+            s.Summary = \"Get Plugins\";\r\n+            s.Description = \"Get all loaded plugins with optional filtering\";\r\n+            s.Response<PluginListResponse>(200, \"Plugins retrieved successfully\");\r\n+        });\r\n     }\r\n \r\n     public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)\r\n     {\r\n"}, {"date": 1751212769517, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,59 +36,9 @@\n     }\r\n \r\n     public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)\r\n     {\r\n-        try\r\n-        {\r\n-            var statuses = await _pluginManager.GetPluginStatusesAsync(ct);\r\n-            var manifests = _pluginManager.GetPluginManifests();\r\n-\r\n-            var filteredStatuses = statuses.AsEnumerable();\r\n-\r\n-            if (!string.IsNullOrEmpty(req.Type))\r\n-            {\r\n-                filteredStatuses = filteredStatuses.Where(s =>\r\n-                    s.Type.Equals(req.Type, StringComparison.OrdinalIgnoreCase));\r\n-            }\r\n-\r\n-            if (req.IsEnabled.HasValue)\r\n-            {\r\n-                filteredStatuses = filteredStatuses.Where(s => s.IsLoaded == req.IsEnabled.Value);\r\n-            }\r\n-\r\n-            if (!string.IsNullOrEmpty(req.Provider))\r\n-            {\r\n-                filteredStatuses = filteredStatuses.Where(s =>\r\n-                {\r\n-                    var manifest = manifests.FirstOrDefault(m => m.Name == s.Name);\r\n-                    return manifest?.Provider?.Equals(req.Provider, StringComparison.OrdinalIgnoreCase) == true;\r\n-                });\r\n-            }\r\n-\r\n-            var pluginSummaries = filteredStatuses.Select(status =>\r\n-            {\r\n-                var manifest = manifests.FirstOrDefault(m => m.Name == status.Name);\r\n-                return new PluginSummary\r\n-                {\r\n-                    Name = status.Name,\r\n-                    Version = status.Version,\r\n-                    Type = status.Type,\r\n-                    Provider = manifest?.Provider ?? \"Unknown\",\r\n-                    IsEnabled = status.IsLoaded,\r\n-                    Status = status.Status,\r\n-                    LoadedAt = status.LastChecked.DateTime\r\n-                };\r\n-            }).ToList();\r\n-\r\n-            await SendOkAsync(new GetPluginsResponse\r\n-            {\r\n-                Plugins = pluginSummaries,\r\n-                TotalCount = pluginSummaries.Count\r\n-            }, ct);\r\n-        }\r\n-        catch (Exception ex)\r\n-        {\r\n-            _logger.LogError(ex, \"Error retrieving plugins\");\r\n-            await SendErrorsAsync(500, ct);\r\n-        }\r\n+        var query = new GetPluginsQuery(req.Type, req.IsLoaded);\r\n+        var response = await _mediator.Send(query, ct);\r\n+        await SendOkAsync(response, ct);\r\n     }\r\n }\r\n"}], "date": 1751212229406, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing PluginCore.Interfaces;\r\nusing NotifyMasterApi.Documentation;\r\nusing System.ComponentModel.DataAnnotations;\r\n\r\nnamespace NotifyMasterApi.Features.Plugins;\r\n\r\n/// <summary>\r\n/// Request model for retrieving plugins with optional filtering\r\n/// </summary>\r\npublic class GetPluginsRequest\r\n{\r\n    /// <summary>\r\n    /// Filter plugins by type (optional)\r\n    /// </summary>\r\n    /// <example>Email, SMS, Push, Messaging</example>\r\n    public string? Type { get; set; }\r\n\r\n    /// <summary>\r\n    /// Filter plugins by enabled status (optional)\r\n    /// </summary>\r\n    /// <example>true</example>\r\n    public bool? IsEnabled { get; set; }\r\n\r\n    /// <summary>\r\n    /// Filter plugins by provider (optional)\r\n    /// </summary>\r\n    /// <example>SendGrid, Twilio, FCM</example>\r\n    public string? Provider { get; set; }\r\n\r\n    /// <summary>\r\n    /// Include detailed plugin information\r\n    /// </summary>\r\n    /// <example>false</example>\r\n    public bool IncludeDetails { get; set; } = false;\r\n}\r\n\r\npublic class GetPluginsResponse\r\n{\r\n    public List<PluginSummary> Plugins { get; set; } = new();\r\n    public int TotalCount { get; set; }\r\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n}\r\n\r\npublic class PluginSummary\r\n{\r\n    public string Name { get; set; } = string.Empty;\r\n    public string Version { get; set; } = string.Empty;\r\n    public string Type { get; set; } = string.Empty;\r\n    public string Provider { get; set; } = string.Empty;\r\n    public bool IsEnabled { get; set; }\r\n    public string Status { get; set; } = string.Empty;\r\n    public DateTime? LoadedAt { get; set; }\r\n}\r\n\r\npublic class GetPluginsEndpoint : Endpoint<GetPluginsRequest, GetPluginsResponse>\r\n{\r\n    private readonly IPluginManager _pluginManager;\r\n    private readonly ILogger<GetPluginsEndpoint> _logger;\r\n\r\n    public GetPluginsEndpoint(IPluginManager pluginManager, ILogger<GetPluginsEndpoint> logger)\r\n    {\r\n        _pluginManager = pluginManager;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        this.ConfigurePluginEndpoint(\r\n            \"GET\",\r\n            \"/api/plugins\",\r\n            \"Get All Plugins\",\r\n            \"Retrieve a comprehensive list of all loaded plugins with their status, configuration, and capabilities.\\n\\n\" +\r\n            \"## 🔌 Plugin Information\\n\" +\r\n            \"- **Plugin Details**: Name, version, provider, and type\\n\" +\r\n            \"- **Status Information**: Enabled/disabled state and health\\n\" +\r\n            \"- **Configuration**: Current settings and capabilities\\n\" +\r\n            \"- **Metrics**: Performance and usage statistics\\n\" +\r\n            \"- **Dependencies**: Required services and connections\\n\\n\" +\r\n            \"## 📋 Plugin Types\\n\" +\r\n            \"- **Email Plugins**: SendGrid, Mailgun, SMTP, Amazon SES\\n\" +\r\n            \"- **SMS Plugins**: Twilio, Kavenegar, Nexmo, Amazon SNS\\n\" +\r\n            \"- **Push Plugins**: FCM, APNS, OneSignal, Web Push\\n\" +\r\n            \"- **Messaging Plugins**: WhatsApp, Slack, Teams, Discord\\n\" +\r\n            \"- **Utility Plugins**: Templates, Analytics, Webhooks\\n\\n\" +\r\n            \"## 🔍 Filtering Options\\n\" +\r\n            \"- Filter by plugin type (Email, SMS, Push, etc.)\\n\" +\r\n            \"- Filter by enabled/disabled status\\n\" +\r\n            \"- Filter by specific provider\\n\" +\r\n            \"- Include/exclude detailed information\\n\\n\" +\r\n            \"## 📊 Plugin Status\\n\" +\r\n            \"- **Active**: Plugin is loaded and operational\\n\" +\r\n            \"- **Inactive**: Plugin is loaded but disabled\\n\" +\r\n            \"- **Error**: Plugin failed to load or has issues\\n\" +\r\n            \"- **Loading**: Plugin is currently being initialized\\n\\n\" +\r\n            \"## 🛠️ Management Operations\\n\" +\r\n            \"Use this endpoint to:\\n\" +\r\n            \"- Monitor plugin health and status\\n\" +\r\n            \"- Identify available messaging providers\\n\" +\r\n            \"- Check plugin configurations\\n\" +\r\n            \"- Plan plugin management operations\",\r\n            new[] { \"System Management\", \"Configuration\" }\r\n        );\r\n    }\r\n\r\n    public override async Task HandleAsync(GetPluginsRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var plugins = await _pluginManager.GetLoadedPluginsAsync();\r\n            \r\n            var filteredPlugins = plugins.AsEnumerable();\r\n            \r\n            if (!string.IsNullOrEmpty(req.Type))\r\n            {\r\n                filteredPlugins = filteredPlugins.Where(p =>\r\n                    p.Type.ToString().Equals(req.Type, StringComparison.OrdinalIgnoreCase));\r\n            }\r\n            \r\n            if (req.IsEnabled.HasValue)\r\n            {\r\n                filteredPlugins = filteredPlugins.Where(p => p.IsEnabled == req.IsEnabled.Value);\r\n            }\r\n\r\n            var pluginSummaries = filteredPlugins.Select(p => new PluginSummary\r\n            {\r\n                Name = p.Name ?? \"Unknown\",\r\n                Version = p.Version ?? \"Unknown\",\r\n                Type = p.Type.ToString(),\r\n                Provider = p.Provider ?? \"Unknown\",\r\n                IsEnabled = p.IsEnabled,\r\n                Status = p.IsEnabled ? \"Active\" : \"Disabled\",\r\n                LoadedAt = p.LoadedAt\r\n            }).ToList();\r\n\r\n            await SendOkAsync(new GetPluginsResponse\r\n            {\r\n                Plugins = pluginSummaries,\r\n                TotalCount = pluginSummaries.Count\r\n            }, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error retrieving plugins\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n"}]}