{"sourceFile": "src/Libraries/SmsService.Library/Extensions/ServiceCollectionExtensions.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751193642084, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751193642084, "name": "Commit-0", "content": "using SmsService.Library.Interfaces;\r\nusing SmsService.Library.Services;\r\nusing Microsoft.Extensions.DependencyInjection;\r\n\r\nnamespace SmsService.Library.Extensions;\r\n\r\npublic static class ServiceCollectionExtensions\r\n{\r\n    \r\n}\r\n"}]}