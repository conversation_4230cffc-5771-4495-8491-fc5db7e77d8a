using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Base;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using System.Diagnostics;

namespace Plugin.Sms.Twilio;

public class TwilioPlugin : ISmsPlugin
{
    private readonly ILogger<TwilioPlugin> _logger;
    private bool _isInitialized = false;

    public TwilioPlugin(ILogger<TwilioPlugin> logger)
    {
        _logger = logger;
    }

    public PluginInfo PluginInfo => new(
        Name: "Twilio SMS Plugin",
        Version: "1.0.0",
        Description: "SMS plugin for Twilio service",
        Type: PluginContract.Enums.PluginType.Sms,
        Author: "NotificationService Team",
        IsEnabled: true,
        Provider: "Twilio"
    );

    public async Task InitializeAsync(IConfiguration configuration)
    {
        var accountSid = configuration["Twilio:AccountSid"];
        var authToken = configuration["Twilio:AuthToken"];

        if (string.IsNullOrEmpty(accountSid) || string.IsNullOrEmpty(authToken))
        {
            throw new InvalidOperationException("Twilio Account SID and Auth Token are required");
        }

        TwilioClient.Init(accountSid, authToken);

        _isInitialized = true;
        _logger.LogInformation("Twilio plugin initialized successfully");
    }

    public async Task<SmsResponse> SendAsync(SmsMessageRequest request)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var from = GetConfigValue<string>("from");
            var messagingServiceSid = GetConfigValue<string>("messagingServiceSid");

            if (string.IsNullOrEmpty(from) && string.IsNullOrEmpty(messagingServiceSid))
            {
                throw new InvalidOperationException("Either 'from' phone number or 'messagingServiceSid' is required");
            }

            var messageOptions = new CreateMessageOptions(request.PhoneNumber)
            {
                Body = request.Message
            };

            if (!string.IsNullOrEmpty(messagingServiceSid))
            {
                messageOptions.MessagingServiceSid = messagingServiceSid;
            }
            else
            {
                messageOptions.From = from;
            }

            var message = await MessageResource.CreateAsync(messageOptions);

            _logger.LogInformation("SMS sent via Twilio. MessageSid: {MessageSid}", message.Sid);

            return new SmsResponse(true, MessageId: message.Sid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS via Twilio");
            return new SmsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request)
    {
        var results = new List<SmsResponse>();
        
        foreach (var sms in request.Messages)
        {
            var result = await SendAsync(sms);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        return new BulkSmsResponse(
            successCount == results.Count,
            SuccessCount: successCount,
            FailureCount: results.Count - successCount,
            Results: results
        );
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var message = await MessageResource.FetchAsync(messageId);
            return new MessageStatusResponse(true, Status: message.Status.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message status from Twilio");
            return new MessageStatusResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var messages = await MessageResource.ReadAsync(
                to: phoneNumber,
                limit: 100
            );

            var messageList = messages.Select(m => new
            {
                MessageId = m.Sid,
                Body = m.Body,
                Status = m.Status.ToString(),
                DateSent = m.DateSent,
                From = m.From?.ToString(),
                To = m.To
            }).Cast<object>().ToList();

            return new MessageHistoryResponse(true, Messages: messageList);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message history from Twilio");
            return new MessageHistoryResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<SmsResponse> ResendMessageAsync(string messageId)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            // Get original message details
            var originalMessage = await MessageResource.FetchAsync(messageId);
            
            // Create new message with same content
            var request = new SmsMessageRequest
            {
                PhoneNumber = originalMessage.To,
                Message = originalMessage.Body
            };

            return await SendAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resending message via Twilio");
            return new SmsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<bool> ValidateConfigurationAsync()
    {
        try
        {
            var accountSid = GetConfigValue<string>("accountSid");
            var authToken = GetConfigValue<string>("authToken");
            
            if (string.IsNullOrEmpty(accountSid) || string.IsNullOrEmpty(authToken))
                return false;

            // Test API connection by fetching account details
            TwilioClient.Init(accountSid, authToken);
            var account = await Twilio.Rest.Api.V2010.AccountResource.FetchAsync();
            
            return account != null;
        }
        catch
        {
            return false;
        }
    }

    private T? GetConfigValue<T>(string key, T? defaultValue = default)
    {
        if (_configuration.TryGetValue(key, out var configItem))
        {
            if (configItem.DefaultValue != null)
            {
                return (T)Convert.ChangeType(configItem.DefaultValue, typeof(T));
            }
        }
        return defaultValue;
    }

    public void Dispose()
    {
        // Twilio client doesn't require explicit disposal
    }
}
