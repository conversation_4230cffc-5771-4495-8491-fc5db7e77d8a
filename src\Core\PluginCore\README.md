# Gateway Plugin System

## Overview

The Gateway Plugin System is a modern, unified architecture for managing messaging gateways (SMS, Email, Push, WebApp) with plugin-based integrations, administration, metrics, and health monitoring.

## Key Features

- **Unified Gateway Architecture**: All gateways implement the same base interfaces
- **Plugin-Based**: Dynamic loading of gateway implementations
- **Message Scheduling**: Built-in scheduling for gateways that don't support it natively
- **Message Storage & Resending**: Automatic storage and resending capabilities
- **Health Monitoring**: Real-time health checks for all gateways
- **Configuration Management**: Centralized configuration with validation
- **Metrics & Analytics**: Built-in metrics collection and reporting

## Architecture

### Core Interfaces

1. **IPluginType**: Base marker interface for all plugins
2. **IGatewayMessagePluginType**: Core messaging functionality
3. **IGatewayAdminPluginType**: Administrative and configuration functions
4. **IGatewayMetricsPluginType**: Metrics and analytics capabilities

### Gateway Types

- **ISmsPlugin**: SMS messaging gateways
- **IEmailPlugin**: Email messaging gateways  
- **IPushPlugin**: Push notification gateways
- **IWebAppPlugin**: Web application notification gateways

### Base Classes

- **EmailGatewayBase**: Base implementation for email gateways
- **SmsGatewayBase**: Base implementation for SMS gateways
- **PushGatewayBase**: Base implementation for push gateways
- **WebAppGatewayBase**: Base implementation for web app gateways

## Quick Start

### 1. Setup in ASP.NET Core

```csharp
// In Program.cs or Startup.cs
services.AddCompleteGatewayPluginSystem(
    pluginDirectory: "plugins",
    autoLoad: true
);
```

### 2. Setup in Console Application

```csharp
var services = new ServiceCollection();
services.AddLogging(builder => builder.AddConsole());
services.AddCompleteGatewayPluginSystem("plugins", autoLoad: true);

var serviceProvider = services.BuildServiceProvider();
```

### 3. Using Plugins

```csharp
// Get plugin manager
var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();

// Get SMS plugins
var smsPlugins = pluginManager.GetPlugins<ISmsPlugin>();
var smsPlugin = smsPlugins.FirstOrDefault();

// Send a message
var payload = new MessagePayload(
    Recipient: "+**********",
    Content: "Hello World!",
    From: "YourApp"
);

var result = await smsPlugin.SendMessageAsync(payload);
```

### 4. Schedule Messages

```csharp
// Schedule a message for later
var scheduledTime = DateTimeOffset.UtcNow.AddHours(1);
var scheduleResult = await smsPlugin.ScheduleMessageAsync(payload, scheduledTime);

// Cancel if needed
await smsPlugin.CancelScheduledMessageAsync(scheduleResult.ScheduleId);
```

### 5. Resend Messages

```csharp
// Resend a previously sent message
var resendResult = await smsPlugin.ResendMessageAsync(originalMessageId);
```

## Creating a New Gateway Plugin

### 1. Create the Plugin Class

```csharp
public class MyCustomSmsGateway : SmsGatewayBase, ISmsPlugin
{
    public MyCustomSmsGateway(ILogger<MyCustomSmsGateway> logger) : base(logger)
    {
        // Initialize your gateway
    }

    protected override async Task<MessageSendResult> SendSmsInternalAsync(
        MessagePayload payload, 
        CancellationToken cancellationToken)
    {
        // Implement your SMS sending logic
        // Return success/failure result
    }

    // Implement other required methods...
}
```

### 2. Build and Deploy

1. Build your plugin as a .NET library
2. Copy the DLL to the plugins directory
3. The system will automatically discover and load it

## Configuration

### Plugin System Options

```csharp
services.ConfigureGatewayPluginSystem(options =>
{
    options.PluginDirectory = "plugins";
    options.AutoLoadPlugins = true;
    options.EnableHealthMonitoring = true;
    options.EnableMessageScheduling = true;
    options.EnableMessageStorage = true;
    options.HealthCheckInterval = TimeSpan.FromMinutes(5);
});
```

### Gateway Configuration

Each gateway can define its own configuration schema:

```csharp
protected override async Task<IEnumerable<GatewayConfiguration>> GetSmsProviderConfigurationsAsync(
    CancellationToken cancellationToken)
{
    return new[]
    {
        new GatewayConfiguration("ApiKey", "API Key", "", "Your API key", true, true),
        new GatewayConfiguration("BaseUrl", "Base URL", "https://api.example.com", "API base URL", true, false)
    };
}
```

## Built-in Services

### Message Scheduling Service

Provides scheduling capabilities for gateways that don't support it natively:

- **DefaultMessageSchedulingService**: In-memory scheduling
- **ScheduledMessageProcessor**: Background service that processes scheduled messages

### Message Storage Service

Provides storage and resending capabilities:

- **DefaultMessageStorageService**: In-memory storage
- Automatic storage of all sent messages
- Message resending with correlation tracking
- Message statistics and analytics

## Health Monitoring

The system includes built-in health monitoring:

```csharp
// Check plugin health
var statuses = await pluginManager.GetPluginStatusesAsync();

foreach (var status in statuses)
{
    if (!status.IsHealthy)
    {
        logger.LogWarning("Plugin {Name} is unhealthy: {Error}", 
            status.Name, status.ErrorMessage);
    }
}
```

## Plugin Discovery

Discover plugins before loading:

```csharp
// Discover plugin files
var pluginFiles = PluginDiscovery.DiscoverPluginFiles("plugins");

// Validate before loading
foreach (var file in pluginFiles)
{
    var isValid = await PluginLoader.ValidatePluginAsync(serviceProvider, file, logger);
    if (isValid)
    {
        logger.LogInformation("Valid plugin found: {File}", file);
    }
}
```

## Examples

See `PluginCore/Examples/PluginSystemUsage.cs` for comprehensive usage examples including:

- Console application setup
- ASP.NET Core integration
- Sending messages
- Scheduling and cancellation
- Health monitoring
- Plugin discovery

## Migration from Old System

The new system completely replaces the old plugin architecture:

1. **Remove old dependencies**: No more separate contract projects
2. **Use gateway base classes**: Inherit from EmailGatewayBase, SmsGatewayBase, etc.
3. **Implement plugin interfaces**: Add ISmsPlugin, IEmailPlugin, etc.
4. **Update service registration**: Use `AddCompleteGatewayPluginSystem()`

## Extensibility

The system is designed for extensibility:

- **Custom Storage**: Replace DefaultMessageStorageService with database storage
- **Custom Scheduling**: Replace DefaultMessageSchedulingService with advanced scheduling
- **Custom Health Checks**: Implement custom health monitoring logic
- **Custom Metrics**: Add custom metrics collection and reporting

## Best Practices

1. **Always inherit from base classes**: Use EmailGatewayBase, SmsGatewayBase, etc.
2. **Implement all interfaces**: Include the specific plugin interface (ISmsPlugin, etc.)
3. **Handle errors gracefully**: Use try-catch and return appropriate error results
4. **Log important events**: Use the provided logger for debugging and monitoring
5. **Validate configuration**: Implement proper configuration validation
6. **Test thoroughly**: Test all gateway operations including edge cases
