using NotificationPortal.Models;
using Blazored.LocalStorage;
using Microsoft.AspNetCore.SignalR.Client;

namespace NotificationPortal.Services;

public interface IUserService
{
    Task<List<User>> GetUsersAsync(string? tenantId = null);
    Task<User?> GetUserAsync(string userId);
    Task<User?> CreateUserAsync(User user);
    Task<User?> UpdateUserAsync(string userId, User user);
    Task<bool> DeleteUserAsync(string userId);
    Task<bool> InviteUserAsync(string email, List<string> roles, string? tenantId = null);
    Task<List<UserDevice>> GetUserDevicesAsync(string userId);
    Task<bool> RevokeDeviceAsync(string userId, string deviceId);
}

public class UserService : IUserService
{
    private readonly IApiService _apiService;

    public UserService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<User>> GetUsersAsync(string? tenantId = null)
    {
        var endpoint = string.IsNullOrEmpty(tenantId) ? "api/users" : $"api/users?tenantId={tenantId}";
        var response = await _apiService.GetAsync<dynamic>(endpoint);
        
        // Simulate user data
        return new List<User>
        {
            new User
            {
                Id = "user_1",
                Email = "<EMAIL>",
                FirstName = "Admin",
                LastName = "User",
                TenantId = tenantId ?? "default",
                Roles = new List<string> { "Admin" },
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                LastLoginAt = DateTime.UtcNow.AddHours(-2)
            },
            new User
            {
                Id = "user_2",
                Email = "<EMAIL>",
                FirstName = "Regular",
                LastName = "User",
                TenantId = tenantId ?? "default",
                Roles = new List<string> { "User" },
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-15),
                LastLoginAt = DateTime.UtcNow.AddHours(-1)
            }
        };
    }

    public async Task<User?> GetUserAsync(string userId)
    {
        return await _apiService.GetAsync<User>($"api/users/{userId}");
    }

    public async Task<User?> CreateUserAsync(User user)
    {
        return await _apiService.PostAsync<User>("api/users", user);
    }

    public async Task<User?> UpdateUserAsync(string userId, User user)
    {
        return await _apiService.PutAsync<User>($"api/users/{userId}", user);
    }

    public async Task<bool> DeleteUserAsync(string userId)
    {
        return await _apiService.DeleteAsync($"api/users/{userId}");
    }

    public async Task<bool> InviteUserAsync(string email, List<string> roles, string? tenantId = null)
    {
        var invitation = new { Email = email, Roles = roles, TenantId = tenantId };
        var response = await _apiService.PostAsync<dynamic>("api/users/invite", invitation);
        return response != null;
    }

    public async Task<List<UserDevice>> GetUserDevicesAsync(string userId)
    {
        var response = await _apiService.GetAsync<List<UserDevice>>($"api/users/{userId}/devices");
        return response ?? new List<UserDevice>();
    }

    public async Task<bool> RevokeDeviceAsync(string userId, string deviceId)
    {
        return await _apiService.DeleteAsync($"api/users/{userId}/devices/{deviceId}");
    }
}

public interface IRoleService
{
    Task<List<Role>> GetRolesAsync(string? tenantId = null);
    Task<Role?> GetRoleAsync(string roleId);
    Task<Role?> CreateRoleAsync(Role role);
    Task<Role?> UpdateRoleAsync(string roleId, Role role);
    Task<bool> DeleteRoleAsync(string roleId);
    Task<List<PermissionGroup>> GetPermissionGroupsAsync();
    Task<Role?> CloneRoleAsync(string roleId, string newName);
}

public class RoleService : IRoleService
{
    private readonly IApiService _apiService;

    public RoleService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<Role>> GetRolesAsync(string? tenantId = null)
    {
        // Simulate role data
        return new List<Role>
        {
            new Role
            {
                Id = "admin",
                Name = "Administrator",
                Description = "Full system access",
                TenantId = tenantId ?? "default",
                IsSystemRole = true,
                Permissions = new List<string>
                {
                    "tenants.view", "tenants.create", "tenants.edit", "tenants.delete",
                    "users.view", "users.create", "users.edit", "users.delete",
                    "roles.view", "roles.create", "roles.edit", "roles.delete",
                    "plugins.view", "plugins.configure", "settings.view", "settings.edit"
                }
            },
            new Role
            {
                Id = "user",
                Name = "User",
                Description = "Standard user access",
                TenantId = tenantId ?? "default",
                IsSystemRole = true,
                Permissions = new List<string>
                {
                    "dashboard.view", "templates.view", "templates.create", "templates.edit"
                }
            }
        };
    }

    public async Task<Role?> GetRoleAsync(string roleId)
    {
        return await _apiService.GetAsync<Role>($"api/roles/{roleId}");
    }

    public async Task<Role?> CreateRoleAsync(Role role)
    {
        return await _apiService.PostAsync<Role>("api/roles", role);
    }

    public async Task<Role?> UpdateRoleAsync(string roleId, Role role)
    {
        return await _apiService.PutAsync<Role>($"api/roles/{roleId}", role);
    }

    public async Task<bool> DeleteRoleAsync(string roleId)
    {
        return await _apiService.DeleteAsync($"api/roles/{roleId}");
    }

    public async Task<List<PermissionGroup>> GetPermissionGroupsAsync()
    {
        return new List<PermissionGroup>
        {
            new PermissionGroup
            {
                Name = "Tenant Management",
                Description = "Manage tenants and tenant settings",
                Permissions = new List<Permission>
                {
                    new Permission { Key = "tenants.view", Name = "View Tenants", Description = "View tenant list and details" },
                    new Permission { Key = "tenants.create", Name = "Create Tenants", Description = "Create new tenants" },
                    new Permission { Key = "tenants.edit", Name = "Edit Tenants", Description = "Modify tenant settings" },
                    new Permission { Key = "tenants.delete", Name = "Delete Tenants", Description = "Remove tenants" }
                }
            },
            new PermissionGroup
            {
                Name = "User Management",
                Description = "Manage users and their access",
                Permissions = new List<Permission>
                {
                    new Permission { Key = "users.view", Name = "View Users", Description = "View user list and details" },
                    new Permission { Key = "users.create", Name = "Create Users", Description = "Create new users" },
                    new Permission { Key = "users.edit", Name = "Edit Users", Description = "Modify user settings" },
                    new Permission { Key = "users.delete", Name = "Delete Users", Description = "Remove users" }
                }
            }
        };
    }

    public async Task<Role?> CloneRoleAsync(string roleId, string newName)
    {
        var cloneRequest = new { SourceRoleId = roleId, NewName = newName };
        return await _apiService.PostAsync<Role>($"api/roles/{roleId}/clone", cloneRequest);
    }
}

public interface ITemplateService
{
    Task<List<Template>> GetTemplatesAsync(string? channel = null, string? category = null);
    Task<Template?> GetTemplateAsync(string templateId);
    Task<Template?> CreateTemplateAsync(Template template);
    Task<Template?> UpdateTemplateAsync(string templateId, Template template);
    Task<bool> DeleteTemplateAsync(string templateId);
    Task<string> PreviewTemplateAsync(string templateId, Dictionary<string, object> variables);
    Task<bool> TestTemplateAsync(string templateId, Dictionary<string, object> variables, string recipient);
}

public class TemplateService : ITemplateService
{
    private readonly IApiService _apiService;

    public TemplateService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<Template>> GetTemplatesAsync(string? channel = null, string? category = null)
    {
        var queryParams = new List<string>();
        if (!string.IsNullOrEmpty(channel)) queryParams.Add($"channel={channel}");
        if (!string.IsNullOrEmpty(category)) queryParams.Add($"category={category}");
        
        var endpoint = "api/templates";
        if (queryParams.Any()) endpoint += "?" + string.Join("&", queryParams);

        // Simulate template data
        return new List<Template>
        {
            new Template
            {
                Id = "welcome_email",
                Name = "Welcome Email",
                Description = "Welcome email for new users",
                Channel = "Email",
                Subject = "Welcome to {{app_name}}!",
                Body = "Hello {{user_name}}, welcome to our platform!",
                HtmlBody = "<h1>Welcome {{user_name}}!</h1><p>Thank you for joining {{app_name}}.</p>",
                Variables = new List<string> { "user_name", "app_name" },
                Category = "User Onboarding",
                IsActive = true
            }
        };
    }

    public async Task<Template?> GetTemplateAsync(string templateId)
    {
        return await _apiService.GetAsync<Template>($"api/templates/{templateId}");
    }

    public async Task<Template?> CreateTemplateAsync(Template template)
    {
        return await _apiService.PostAsync<Template>("api/templates", template);
    }

    public async Task<Template?> UpdateTemplateAsync(string templateId, Template template)
    {
        return await _apiService.PutAsync<Template>($"api/templates/{templateId}", template);
    }

    public async Task<bool> DeleteTemplateAsync(string templateId)
    {
        return await _apiService.DeleteAsync($"api/templates/{templateId}");
    }

    public async Task<string> PreviewTemplateAsync(string templateId, Dictionary<string, object> variables)
    {
        var request = new { TemplateId = templateId, Variables = variables };
        var response = await _apiService.PostAsync<dynamic>("api/templates/preview", request);
        return response?.preview ?? "";
    }

    public async Task<bool> TestTemplateAsync(string templateId, Dictionary<string, object> variables, string recipient)
    {
        var request = new { TemplateId = templateId, Variables = variables, Recipient = recipient };
        var response = await _apiService.PostAsync<dynamic>("api/templates/test", request);
        return response != null;
    }
}
