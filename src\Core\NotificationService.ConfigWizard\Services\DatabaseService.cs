using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NotificationService.ConfigWizard.Models;
using System.Data.Common;
using System.Diagnostics;

namespace NotificationService.ConfigWizard.Services;

public interface IDatabaseService
{
    Task<DatabaseTestResult> TestConnectionAsync(DatabaseConfiguration config);
    Task<MigrationResult> InitializeSchemaAsync(DatabaseConfiguration config);
    Task<bool> CreateDatabaseIfNotExistsAsync(DatabaseConfiguration config);
    Task<ValidationResult> ValidateConfigurationAsync(DatabaseConfiguration config);
}

public class DatabaseService : IDatabaseService
{
    private readonly ILogger<DatabaseService> _logger;

    public DatabaseService(ILogger<DatabaseService> logger)
    {
        _logger = logger;
    }

    public async Task<DatabaseTestResult> TestConnectionAsync(DatabaseConfiguration config)
    {
        var result = new DatabaseTestResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            using var context = CreateDbContext(config);
            
            // Test basic connectivity
            await context.Database.OpenConnectionAsync();
            result.CanConnect = true;
            
            // Check if database exists
            result.DatabaseExists = await context.Database.CanConnectAsync();
            
            // Check if tables exist
            if (result.DatabaseExists)
            {
                var tableCount = await context.Database.ExecuteScalarAsync<int>(
                    GetTableCountQuery(config.Provider));
                result.HasTables = tableCount > 0;
            }
            
            // Get database version
            result.Version = await GetDatabaseVersionAsync(context, config.Provider);
            
            stopwatch.Stop();
            result.ConnectionTime = stopwatch.Elapsed;
            
            _logger.LogInformation("Database connection test successful for {Provider} at {Host}", 
                config.Provider, config.Host);
        }
        catch (Exception ex)
        {
            result.CanConnect = false;
            result.Error = ex.Message;
            _logger.LogError(ex, "Database connection test failed for {Provider} at {Host}", 
                config.Provider, config.Host);
        }

        return result;
    }

    public async Task<MigrationResult> InitializeSchemaAsync(DatabaseConfiguration config)
    {
        var result = new MigrationResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            using var context = CreateDbContext(config);
            
            // Ensure database is created
            await context.Database.EnsureCreatedAsync();
            
            // Get pending migrations
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            
            if (pendingMigrations.Any())
            {
                _logger.LogInformation("Applying {Count} pending migrations", pendingMigrations.Count());
                
                // Apply migrations
                await context.Database.MigrateAsync();
                
                result.AppliedMigrations.AddRange(pendingMigrations);
            }
            else
            {
                _logger.LogInformation("No pending migrations found");
            }
            
            // Verify schema
            await VerifySchemaAsync(context);
            
            result.Success = true;
            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;
            
            _logger.LogInformation("Schema initialization completed successfully in {Duration}ms", 
                result.Duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Errors.Add(ex.Message);
            _logger.LogError(ex, "Schema initialization failed");
            
            // Attempt rollback if possible
            try
            {
                await RollbackSchemaAsync(config);
            }
            catch (Exception rollbackEx)
            {
                result.Errors.Add($"Rollback failed: {rollbackEx.Message}");
                _logger.LogError(rollbackEx, "Schema rollback failed");
            }
        }

        return result;
    }

    public async Task<bool> CreateDatabaseIfNotExistsAsync(DatabaseConfiguration config)
    {
        try
        {
            using var context = CreateDbContext(config);
            var created = await context.Database.EnsureCreatedAsync();
            
            if (created)
            {
                _logger.LogInformation("Database {DatabaseName} created successfully", config.DatabaseName);
            }
            else
            {
                _logger.LogInformation("Database {DatabaseName} already exists", config.DatabaseName);
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create database {DatabaseName}", config.DatabaseName);
            return false;
        }
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(DatabaseConfiguration config)
    {
        var result = new ValidationResult { IsValid = true };

        // Validate provider
        if (!WizardConstants.SupportedDatabaseProviders.Contains(config.Provider))
        {
            result.AddError($"Unsupported database provider: {config.Provider}");
            result.IsValid = false;
        }

        // Validate connection parameters
        if (config.Provider != "InMemory" && config.Provider != "SQLite")
        {
            if (string.IsNullOrWhiteSpace(config.Host))
            {
                result.AddError("Database host is required");
                result.IsValid = false;
            }

            if (config.Port <= 0 || config.Port > 65535)
            {
                result.AddError("Invalid port number");
                result.IsValid = false;
            }

            if (!config.IntegratedSecurity)
            {
                if (string.IsNullOrWhiteSpace(config.Username))
                {
                    result.AddError("Username is required when not using integrated security");
                    result.IsValid = false;
                }

                if (string.IsNullOrWhiteSpace(config.Password))
                {
                    result.AddWarning("Password is empty - this may cause connection issues");
                }
            }
        }

        if (string.IsNullOrWhiteSpace(config.DatabaseName))
        {
            result.AddError("Database name is required");
            result.IsValid = false;
        }

        return result;
    }

    private DbContext CreateDbContext(DatabaseConfiguration config)
    {
        var optionsBuilder = new DbContextOptionsBuilder<NotificationDbContext>();

        switch (config.Provider.ToLower())
        {
            case "sqlserver":
                optionsBuilder.UseSqlServer(config.ConnectionString);
                break;
            case "postgresql":
                optionsBuilder.UseNpgsql(config.ConnectionString);
                break;
            case "sqlite":
                optionsBuilder.UseSqlite(config.ConnectionString);
                break;
            case "inmemory":
                optionsBuilder.UseInMemoryDatabase(config.DatabaseName);
                break;
            default:
                throw new NotSupportedException($"Database provider '{config.Provider}' is not supported");
        }

        return new NotificationDbContext(optionsBuilder.Options);
    }

    private string GetTableCountQuery(string provider)
    {
        return provider.ToLower() switch
        {
            "sqlserver" => "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'",
            "postgresql" => "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'",
            "sqlite" => "SELECT COUNT(*) FROM sqlite_master WHERE type='table'",
            _ => "SELECT 0"
        };
    }

    private async Task<string?> GetDatabaseVersionAsync(DbContext context, string provider)
    {
        try
        {
            var versionQuery = provider.ToLower() switch
            {
                "sqlserver" => "SELECT @@VERSION",
                "postgresql" => "SELECT version()",
                "sqlite" => "SELECT sqlite_version()",
                _ => null
            };

            if (versionQuery != null)
            {
                var version = await context.Database.ExecuteScalarAsync<string>(versionQuery);
                return version?.Split('\n')[0]; // Return first line only
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get database version");
        }

        return null;
    }

    private async Task VerifySchemaAsync(DbContext context)
    {
        // Verify essential tables exist
        var essentialTables = new[] { "Tenants", "Users", "Roles", "Plugins" };
        
        foreach (var table in essentialTables)
        {
            var exists = await context.Database.ExecuteScalarAsync<int>(
                $"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table}'");
            
            if (exists == 0)
            {
                throw new InvalidOperationException($"Essential table '{table}' was not created");
            }
        }
    }

    private async Task RollbackSchemaAsync(DatabaseConfiguration config)
    {
        try
        {
            using var context = CreateDbContext(config);
            
            // In a real implementation, this would restore from backup or drop created objects
            // For now, we'll just log the attempt
            _logger.LogWarning("Schema rollback attempted - manual cleanup may be required");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Schema rollback failed");
            throw;
        }
    }
}

// Simplified DbContext for the wizard
public class NotificationDbContext : DbContext
{
    public NotificationDbContext(DbContextOptions<NotificationDbContext> options) : base(options)
    {
    }

    // Add your DbSets here
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Plugin> Plugins { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure your entities here
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Domain).IsRequired().HasMaxLength(100);
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(50);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(50);
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
        });

        modelBuilder.Entity<Plugin>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
        });

        base.OnModelCreating(modelBuilder);
    }
}

// Simplified entity models for the wizard
public class Tenant
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Domain { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

public class User
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

public class Role
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public bool IsSystemRole { get; set; } = false;
}

public class Plugin
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
}
