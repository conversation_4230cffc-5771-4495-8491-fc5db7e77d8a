using FastEndpoints;
using PluginCore.Interfaces;
using NotifyMasterApi.Documentation;
using System.ComponentModel.DataAnnotations;

namespace NotifyMasterApi.Features.Plugins;

/// <summary>
/// Load a plugin at runtime
/// </summary>
public class LoadPluginRequest
{
    /// <summary>
    /// Name of the plugin to load
    /// </summary>
    /// <example>SendGrid</example>
    [Required]
    public string PluginName { get; set; } = string.Empty;

    /// <summary>
    /// Path to the plugin file (optional, will search in plugins directory if not provided)
    /// </summary>
    /// <example>plugins/Plugin.Email.SendGrid.dll</example>
    public string? PluginPath { get; set; }
}

/// <summary>
/// Unload a plugin at runtime
/// </summary>
public class UnloadPluginRequest
{
    /// <summary>
    /// Name of the plugin to unload
    /// </summary>
    /// <example>SendGrid</example>
    [Required]
    public string PluginName { get; set; } = string.Empty;
}

/// <summary>
/// Response for plugin management operations
/// </summary>
public class PluginManagementResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? PluginName { get; set; }
    public string? PluginVersion { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Load plugins from directory
/// </summary>
public class LoadPluginsFromDirectoryRequest
{
    /// <summary>
    /// Directory path containing plugins
    /// </summary>
    /// <example>plugins</example>
    [Required]
    public string Directory { get; set; } = "plugins";
}

/// <summary>
/// Endpoint for loading a plugin at runtime
/// </summary>
public class LoadPluginEndpoint : Endpoint<LoadPluginRequest, PluginManagementResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<LoadPluginEndpoint> _logger;

    public LoadPluginEndpoint(IPluginManager pluginManager, ILogger<LoadPluginEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/plugins/load");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Load Plugin at Runtime";
            s.Description = "Dynamically load a plugin by name or path without restarting the application";
            s.Response<PluginManagementResponse>(200, "Plugin loaded successfully");
            s.Response<PluginManagementResponse>(400, "Invalid request or plugin not found");
            s.Response<PluginManagementResponse>(500, "Failed to load plugin");
        });
    }

    public override async Task HandleAsync(LoadPluginRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Loading plugin: {PluginName}", req.PluginName);

            PluginCore.Models.OperationResult result;

            if (!string.IsNullOrEmpty(req.PluginPath))
            {
                // Load from specific path
                result = await _pluginManager.LoadPluginAsync(req.PluginPath, ct);
            }
            else
            {
                // Load by name from plugins directory
                result = await _pluginManager.LoadPluginByNameAsync(req.PluginName, "plugins", ct);
            }

            if (result.IsSuccess)
            {
                var manifest = _pluginManager.GetPluginManifest(req.PluginName);
                
                await SendOkAsync(new PluginManagementResponse
                {
                    Success = true,
                    Message = result.Message ?? "Plugin loaded successfully",
                    PluginName = req.PluginName,
                    PluginVersion = manifest?.Version
                }, ct);
            }
            else
            {
                await SendAsync(new PluginManagementResponse
                {
                    Success = false,
                    Message = result.Message ?? "Failed to load plugin",
                    PluginName = req.PluginName
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading plugin {PluginName}", req.PluginName);
            await SendAsync(new PluginManagementResponse
            {
                Success = false,
                Message = $"Error loading plugin: {ex.Message}",
                PluginName = req.PluginName
            }, 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for unloading a plugin at runtime
/// </summary>
public class UnloadPluginEndpoint : Endpoint<UnloadPluginRequest, PluginManagementResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<UnloadPluginEndpoint> _logger;

    public UnloadPluginEndpoint(IPluginManager pluginManager, ILogger<UnloadPluginEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/plugins/unload");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Unload Plugin at Runtime";
            s.Description = "Dynamically unload a plugin by name without restarting the application";
            s.Response<PluginManagementResponse>(200, "Plugin unloaded successfully");
            s.Response<PluginManagementResponse>(400, "Plugin not found or not loaded");
            s.Response<PluginManagementResponse>(500, "Failed to unload plugin");
        });
    }

    public override async Task HandleAsync(UnloadPluginRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Unloading plugin: {PluginName}", req.PluginName);

            var result = await _pluginManager.UnloadPluginAsync(req.PluginName, ct);

            if (result.IsSuccess)
            {
                await SendOkAsync(new PluginManagementResponse
                {
                    Success = true,
                    Message = result.Message ?? "Plugin unloaded successfully",
                    PluginName = req.PluginName
                }, ct);
            }
            else
            {
                await SendAsync(new PluginManagementResponse
                {
                    Success = false,
                    Message = result.Message ?? "Failed to unload plugin",
                    PluginName = req.PluginName
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unloading plugin {PluginName}", req.PluginName);
            await SendAsync(new PluginManagementResponse
            {
                Success = false,
                Message = $"Error unloading plugin: {ex.Message}",
                PluginName = req.PluginName
            }, 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for loading all plugins from a directory
/// </summary>
public class LoadPluginsFromDirectoryEndpoint : Endpoint<LoadPluginsFromDirectoryRequest, PluginManagementResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<LoadPluginsFromDirectoryEndpoint> _logger;

    public LoadPluginsFromDirectoryEndpoint(IPluginManager pluginManager, ILogger<LoadPluginsFromDirectoryEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/plugins/load-directory");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Load All Plugins from Directory";
            s.Description = "Load all plugins found in the specified directory";
            s.Response<PluginManagementResponse>(200, "Plugins loaded successfully");
            s.Response<PluginManagementResponse>(400, "Directory not found or invalid");
            s.Response<PluginManagementResponse>(500, "Failed to load plugins");
        });
    }

    public override async Task HandleAsync(LoadPluginsFromDirectoryRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Loading plugins from directory: {Directory}", req.Directory);

            var result = await _pluginManager.LoadPluginsAsync(req.Directory, ct);

            if (result.IsSuccess)
            {
                var manifests = _pluginManager.GetPluginManifests();
                
                await SendOkAsync(new PluginManagementResponse
                {
                    Success = true,
                    Message = $"{result.Message}. Loaded {manifests.Count} plugins.",
                }, ct);
            }
            else
            {
                await SendAsync(new PluginManagementResponse
                {
                    Success = false,
                    Message = result.Message ?? "Failed to load plugins from directory"
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading plugins from directory {Directory}", req.Directory);
            await SendAsync(new PluginManagementResponse
            {
                Success = false,
                Message = $"Error loading plugins: {ex.Message}"
            }, 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for getting plugin health status
/// </summary>
public class GetPluginHealthEndpoint : EndpointWithoutRequest<List<PluginCore.Interfaces.PluginStatus>>
{
    private readonly IPluginManager _pluginManager;

    public GetPluginHealthEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Get("/api/plugins/health");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get Plugin Health Status";
            s.Description = "Get detailed health status of all loaded plugins";
            s.Response<List<PluginCore.Interfaces.PluginStatus>>(200, "Plugin health status retrieved successfully");
        });
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var statuses = await _pluginManager.GetPluginStatusesAsync(ct);
        await SendOkAsync(statuses.ToList(), ct);
    }
}
