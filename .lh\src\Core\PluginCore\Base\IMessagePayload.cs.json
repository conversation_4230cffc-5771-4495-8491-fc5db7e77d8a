{"sourceFile": "src/Core/PluginCore/Base/IMessagePayload.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751199048946, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751199048946, "name": "Commit-0", "content": "namespace NotificationService.Core.PluginCore.Base;\r\n\r\npublic interface IMessagePayload\r\n{\r\n    string? MessageId { get; set; }\r\n    string? CorrelationId { get; set; }\r\n    string? UserId { get; set; }\r\n    string? TenantId { get; set; }\r\n    string? Channel { get; set; }\r\n    string? Provider { get; set; }\r\n    string? ProviderMessageId { get; set; }\r\n    string? ProviderResponse { get; set; }\r\n    string? ProviderErrorCode { get; set; }\r\n    string? ProviderErrorMessage { get; set; }\r\n    string? ProviderErrorDetails { get; set; }\r\n    string? ProviderErrorResolution { get; set; }\r\n    string? ProviderErrorDocumentation { get; set; }\r\n    string? ProviderErrorSupport { get; set; }\r\n    string? ProviderErrorContact { get; set; }\r\n    string? ProviderErrorPhone { get; set; }\r\n    string? ProviderErrorEmail { get; set; }\r\n    string? ProviderErrorUrl { get; set; }\r\n    string? ProviderErrorOther { get; set; }\r\n    string? ProviderErrorAdditionalInfo { get; set; }\r\n    string? ProviderErrorTimestamp { get; set; }\r\n    string? TemplateId { get; set; }\r\n    Dictionary<string, string>? TemplateData { get; set; }\r\n    Recipient? Recipient { get; set; }\r\n    string? Subject { get; set; }\r\n    string? Content { get; set; }\r\n    string? MessageType { get; set; }\r\n    string? MessageCategory { get; set; }\r\n    string? MessagePriority { get; set; }\r\n    string? MessageExpiration { get; set; }\r\n    string? MessageDeliveryWindow { get; set; }\r\n    string? MessageDeliveryAttempts { get; set; }\r\n    string? MessageDeliveryRetryDelay { get; set; }\r\n    string? MessageDeliveryRetryAttempts { get; set; }\r\n    string? MessageDeliveryRetryBackoff { get; set; }\r\n    string? MessageDeliveryRetryJitter { get; set; }\r\n}\r\n"}]}