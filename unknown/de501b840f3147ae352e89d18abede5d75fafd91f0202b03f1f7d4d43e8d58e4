{"sourceFile": "src/Implementations/NotifyMasterApi/Infrastructure/HangfireAuthorizationFilter.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751225461346, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751225461346, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\nusing Hangfire.Dashboard;\n\nnamespace NotifyMasterApi.Infrastructure;\n\n/// <summary>\n/// Authorization filter for Hangfire dashboard\n/// </summary>\npublic class HangfireAuthorizationFilter : IDashboardAuthorizationFilter\n{\n    public bool Authorize(DashboardContext context)\n    {\n        var httpContext = context.GetHttpContext();\n        \n        // In development, allow access\n        if (httpContext.RequestServices.GetRequiredService<IWebHostEnvironment>().IsDevelopment())\n        {\n            return true;\n        }\n\n        // Check if user is authenticated and has system admin role\n        if (httpContext.User.Identity?.IsAuthenticated == true)\n        {\n            return httpContext.User.IsInRole(\"SuperAdmin\");\n        }\n\n        return false;\n    }\n}\n"}]}