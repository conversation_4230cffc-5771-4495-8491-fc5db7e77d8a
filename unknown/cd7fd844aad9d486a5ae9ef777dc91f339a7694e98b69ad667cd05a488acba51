# Global Using Statements Structure

This document outlines the global using statements structure implemented across the NotifyMaster solution to clean up and standardize using statements.

## Overview

Global using statements have been implemented in each project to:
- Reduce code duplication
- Standardize commonly used namespaces
- Improve code readability
- Simplify maintenance

## Project Structure

### 1. PluginCore Project (`src/Core/PluginCore/GlobalUsings.cs`)

**Purpose**: Core plugin functionality and interfaces

**Key Namespaces**:
- System fundamentals (Collections, Threading, Tasks, etc.)
- Microsoft Extensions (DI, Logging, Configuration, Hosting)
- ASP.NET Core (Builder, Http)
- JWT and Security (IdentityModel.Tokens, System.IdentityModel.Tokens.Jwt)
- PluginCore namespaces (Base, Interfaces, Models, Services, Utilities)

### 2. NotifyMaster.Core Project (`src/Core/NotifyMaster.Core/GlobalUsings.cs`)

**Purpose**: Core application services (multitenancy, authorization)

**Key Namespaces**:
- System fundamentals
- Microsoft Extensions (DI, Logging, Configuration)
- ASP.NET Core (Authentication, Authorization, Builder, Http)
- JWT Security
- PluginCore references
- NotifyMaster.Core services

### 3. NotifyMaster.Database Project (`src/Core/NotifyMaster.Database/GlobalUsings.cs`)

**Purpose**: Database layer with Entity Framework

**Key Namespaces**:
- System fundamentals
- Microsoft Extensions (Logging)
- Entity Framework Core
- PluginCore references
- NotifyMaster.Database namespace

### 4. NotifyMasterApi Project (`src/Implementations/NotifyMasterApi/GlobalUsings.cs`)

**Purpose**: Main API application

**Key Namespaces**:
- System fundamentals
- Microsoft Extensions (DI, Logging, Configuration, Hosting, Options)
- ASP.NET Core (Authentication, Authorization, Builder, Http, Mvc)
- Entity Framework Core
- FastEndpoints
- Hangfire
- MediatR
- JWT Security
- All project references (PluginCore, NotifyMaster.Core, NotifyMaster.Database, NotifyMasterApi)

## Implementation Benefits

### 1. **Cleaner Code Files**
- Removed repetitive using statements from individual files
- Files now start with `// Using statements are handled by GlobalUsings.cs`
- Only specific, non-global usings are included when needed

### 2. **Consistency**
- Standardized namespace imports across the solution
- Ensures all developers use the same common namespaces

### 3. **Maintainability**
- Single location to manage common using statements per project
- Easy to add new global usings as the project evolves
- Reduces merge conflicts in using statements

### 4. **Performance**
- No runtime performance impact
- Compile-time benefits from reduced parsing

## Usage Guidelines

### Adding New Global Usings
1. Identify commonly used namespaces across multiple files in a project
2. Add to the appropriate `GlobalUsings.cs` file
3. Remove from individual files where it was previously used

### Project-Specific Usings
- Keep project-specific or rarely used namespaces in individual files
- Use specific usings for external libraries that aren't used globally
- Document any special usings that might be confusing

### File-Specific Usings
When a file needs a namespace not in GlobalUsings.cs:
```csharp
// Using statements are handled by GlobalUsings.cs
using SpecificNamespace.ForThisFile;

namespace YourNamespace;
```

## Examples of Cleaned Files

### Before (TenantService.cs):
```csharp
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PluginCore.Interfaces;
using PluginCore.Models;

namespace NotifyMaster.Database.Services;
```

### After (TenantService.cs):
```csharp
// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Database.Services;
```

## Future Considerations

1. **Monitor Usage**: Regularly review which namespaces are commonly used and consider adding them to global usings
2. **Project Growth**: As new projects are added, create corresponding GlobalUsings.cs files
3. **External Dependencies**: Consider adding commonly used external library namespaces to global usings
4. **Team Standards**: Ensure all team members understand and follow the global using conventions

## Related Files

- `src/Core/PluginCore/GlobalUsings.cs`
- `src/Core/NotifyMaster.Core/GlobalUsings.cs`
- `src/Core/NotifyMaster.Database/GlobalUsings.cs`
- `src/Implementations/NotifyMasterApi/GlobalUsings.cs`

This structure provides a clean, maintainable approach to managing using statements across the entire NotifyMaster solution.
