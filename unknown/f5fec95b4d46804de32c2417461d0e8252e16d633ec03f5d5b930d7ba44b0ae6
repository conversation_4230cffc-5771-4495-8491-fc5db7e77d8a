{"sourceFile": "src/Core/NotifyMaster.Database/Services/PermissionService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751225413901, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751225413901, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Database.Services;\n\n/// <summary>\n/// Implementation of permission management service using Entity Framework\n/// </summary>\npublic class PermissionService : IPermissionService\n{\n    private readonly NotifyMasterDbContext _context;\n    private readonly ILogger<PermissionService> _logger;\n\n    public PermissionService(NotifyMasterDbContext context, ILogger<PermissionService> logger)\n    {\n        _context = context;\n        _logger = logger;\n    }\n\n    public async Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Permissions\n                .Include(p => p.Roles).ThenInclude(rp => rp.Role)\n                .Include(p => p.Users).ThenInclude(up => up.User)\n                .FirstOrDefaultAsync(p => p.Id == permissionId, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting permission {PermissionId}\", permissionId);\n            return null;\n        }\n    }\n\n    public async Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Permissions\n                .FirstOrDefaultAsync(p => p.Resource == resource && p.Action == action, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting permission {Resource}:{Action}\", resource, action);\n            return null;\n        }\n    }\n\n    public async Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var query = _context.Permissions.AsQueryable();\n\n            if (!string.IsNullOrEmpty(resource))\n            {\n                query = query.Where(p => p.Resource == resource);\n            }\n\n            return await query\n                .OrderBy(p => p.Resource).ThenBy(p => p.Action)\n                .Skip(skip)\n                .Take(take)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting permissions\");\n            return Array.Empty<Permission>();\n        }\n    }\n\n    public async Task<OperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if permission already exists\n            var existingPermission = await _context.Permissions\n                .FirstOrDefaultAsync(p => p.Resource == request.Resource && p.Action == request.Action, cancellationToken);\n\n            if (existingPermission != null)\n            {\n                return OperationResult<Permission>.Failure(\"A permission with this resource and action already exists\");\n            }\n\n            var permission = new Permission\n            {\n                Id = Guid.NewGuid().ToString(),\n                Name = request.Name,\n                Description = request.Description,\n                Resource = request.Resource,\n                Action = request.Action,\n                IsSystemPermission = request.IsSystemPermission\n            };\n\n            _context.Permissions.Add(permission);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Created permission {PermissionId} - {Resource}:{Action}\", permission.Id, permission.Resource, permission.Action);\n            return OperationResult<Permission>.Success(permission);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error creating permission {Resource}:{Action}\", request.Resource, request.Action);\n            return OperationResult<Permission>.Failure($\"Failed to create permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var permission = await _context.Permissions.FindAsync(permissionId);\n            if (permission == null)\n            {\n                return OperationResult<Permission>.Failure(\"Permission not found\");\n            }\n\n            if (permission.IsSystemPermission)\n            {\n                return OperationResult<Permission>.Failure(\"Cannot update system permissions\");\n            }\n\n            // Update properties if provided\n            if (!string.IsNullOrEmpty(request.Name))\n                permission.Name = request.Name;\n\n            if (!string.IsNullOrEmpty(request.Description))\n                permission.Description = request.Description;\n\n            if (!string.IsNullOrEmpty(request.Resource) || !string.IsNullOrEmpty(request.Action))\n            {\n                var newResource = request.Resource ?? permission.Resource;\n                var newAction = request.Action ?? permission.Action;\n\n                // Check if new resource:action combination already exists\n                var existingPermission = await _context.Permissions\n                    .FirstOrDefaultAsync(p => p.Resource == newResource && p.Action == newAction && p.Id != permissionId, cancellationToken);\n\n                if (existingPermission != null)\n                {\n                    return OperationResult<Permission>.Failure(\"A permission with this resource and action already exists\");\n                }\n\n                permission.Resource = newResource;\n                permission.Action = newAction;\n            }\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Updated permission {PermissionId}\", permissionId);\n            return OperationResult<Permission>.Success(permission);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error updating permission {PermissionId}\", permissionId);\n            return OperationResult<Permission>.Failure($\"Failed to update permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var permission = await _context.Permissions.FindAsync(permissionId);\n            if (permission == null)\n            {\n                return OperationResult.Failure(\"Permission not found\");\n            }\n\n            if (permission.IsSystemPermission)\n            {\n                return OperationResult.Failure(\"Cannot delete system permissions\");\n            }\n\n            _context.Permissions.Remove(permission);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Deleted permission {PermissionId}\", permissionId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error deleting permission {PermissionId}\", permissionId);\n            return OperationResult.Failure($\"Failed to delete permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<IReadOnlyList<Role>> GetPermissionRolesAsync(string permissionId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.RolePermissions\n                .Include(rp => rp.Role)\n                .Where(rp => rp.PermissionId == permissionId)\n                .Select(rp => rp.Role)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting roles for permission {PermissionId}\", permissionId);\n            return Array.Empty<Role>();\n        }\n    }\n\n    public async Task<IReadOnlyList<User>> GetPermissionUsersAsync(string permissionId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var directUsers = await _context.UserPermissions\n                .Include(up => up.User)\n                .Where(up => up.PermissionId == permissionId)\n                .Select(up => up.User)\n                .ToListAsync(cancellationToken);\n\n            var roleUsers = await _context.RolePermissions\n                .Include(rp => rp.Role)\n                .ThenInclude(r => r.Users)\n                .ThenInclude(ur => ur.User)\n                .Where(rp => rp.PermissionId == permissionId)\n                .SelectMany(rp => rp.Role.Users)\n                .Select(ur => ur.User)\n                .ToListAsync(cancellationToken);\n\n            var allUsers = directUsers.Concat(roleUsers).DistinctBy(u => u.Id).ToList();\n            return allUsers;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting users for permission {PermissionId}\", permissionId);\n            return Array.Empty<User>();\n        }\n    }\n\n    public async Task<OperationResult> SeedDefaultPermissionsAsync(CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            _logger.LogInformation(\"🌱 Seeding default permissions and roles...\");\n\n            // Check if permissions already exist\n            var existingPermissionsCount = await _context.Permissions.CountAsync(cancellationToken);\n            if (existingPermissionsCount > 0)\n            {\n                _logger.LogInformation(\"✅ Permissions already seeded ({Count} permissions found)\", existingPermissionsCount);\n                return OperationResult.Success();\n            }\n\n            // Seed permissions\n            var defaultPermissions = SystemPermissions.GetDefaultPermissions();\n            var permissions = new List<Permission>();\n\n            foreach (var (name, description, resource, action) in defaultPermissions)\n            {\n                permissions.Add(new Permission\n                {\n                    Id = Guid.NewGuid().ToString(),\n                    Name = name,\n                    Description = description,\n                    Resource = resource,\n                    Action = action,\n                    IsSystemPermission = true\n                });\n            }\n\n            _context.Permissions.AddRange(permissions);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"✅ Seeded {Count} permissions\", permissions.Count);\n\n            // Seed roles\n            var existingRolesCount = await _context.Roles.CountAsync(cancellationToken);\n            if (existingRolesCount == 0)\n            {\n                var defaultRoles = SystemRoles.GetDefaultRoles();\n                var roles = new List<Role>();\n\n                foreach (var (name, description, scope, permissionNames) in defaultRoles)\n                {\n                    var role = new Role\n                    {\n                        Id = Guid.NewGuid().ToString(),\n                        Name = name,\n                        Description = description,\n                        Scope = scope,\n                        IsSystemRole = true,\n                        CreatedAt = DateTime.UtcNow\n                    };\n\n                    roles.Add(role);\n                }\n\n                _context.Roles.AddRange(roles);\n                await _context.SaveChangesAsync(cancellationToken);\n\n                _logger.LogInformation(\"✅ Seeded {Count} roles\", roles.Count);\n\n                // Assign permissions to roles\n                var rolePermissions = new List<RolePermission>();\n\n                foreach (var (name, description, scope, permissionNames) in defaultRoles)\n                {\n                    var role = roles.First(r => r.Name == name);\n\n                    foreach (var permissionName in permissionNames)\n                    {\n                        var permission = permissions.FirstOrDefault(p => p.Name == permissionName);\n                        if (permission != null)\n                        {\n                            rolePermissions.Add(new RolePermission\n                            {\n                                RoleId = role.Id,\n                                PermissionId = permission.Id,\n                                AssignedAt = DateTime.UtcNow,\n                                AssignedBy = \"System Seed\"\n                            });\n                        }\n                    }\n                }\n\n                _context.RolePermissions.AddRange(rolePermissions);\n                await _context.SaveChangesAsync(cancellationToken);\n\n                _logger.LogInformation(\"✅ Assigned {Count} role permissions\", rolePermissions.Count);\n            }\n\n            _logger.LogInformation(\"🎉 Default permissions and roles seeded successfully!\");\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"❌ Failed to seed default permissions and roles\");\n            return OperationResult.Failure($\"Failed to seed default data: {ex.Message}\");\n        }\n    }\n}\n"}]}