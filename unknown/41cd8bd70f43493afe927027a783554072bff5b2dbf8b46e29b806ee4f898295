// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Service responsible for resolving tenant context from HTTP requests
/// </summary>
public interface ITenantResolutionService
{
    /// <summary>
    /// Resolves tenant from HTTP context
    /// </summary>
    Task<Tenant?> ResolveTenantAsync(HttpContext context, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Resolves tenant by domain
    /// </summary>
    Task<Tenant?> ResolveTenantByDomainAsync(string domain, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Resolves tenant by ID
    /// </summary>
    Task<Tenant?> ResolveTenantByIdAsync(string tenantId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of tenant resolution service
/// </summary>
public class TenantResolutionService : ITenantResolutionService
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<TenantResolutionService> _logger;

    public TenantResolutionService(
        ITenantService tenantService,
        ILogger<TenantResolutionService> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<Tenant?> ResolveTenantAsync(HttpContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Try to resolve from header first
            var tenantFromHeader = await ResolveTenantFromHeaderAsync(context, cancellationToken);
            if (tenantFromHeader != null)
            {
                return tenantFromHeader;
            }

            // Try to resolve from subdomain
            var tenantFromSubdomain = await ResolveTenantFromSubdomainAsync(context, cancellationToken);
            if (tenantFromSubdomain != null)
            {
                return tenantFromSubdomain;
            }

            // Try to resolve from JWT claims
            var tenantFromJwt = await ResolveTenantFromJwtAsync(context, cancellationToken);
            if (tenantFromJwt != null)
            {
                return tenantFromJwt;
            }

            // Fallback to default tenant
            return await ResolveTenantByDomainAsync("default", cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving tenant from HTTP context");
            return null;
        }
    }

    public async Task<Tenant?> ResolveTenantByDomainAsync(string domain, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _tenantService.GetTenantByDomainAsync(domain, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving tenant by domain {Domain}", domain);
            return null;
        }
    }

    public async Task<Tenant?> ResolveTenantByIdAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _tenantService.GetTenantAsync(tenantId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving tenant by ID {TenantId}", tenantId);
            return null;
        }
    }

    private async Task<Tenant?> ResolveTenantFromHeaderAsync(HttpContext context, CancellationToken cancellationToken)
    {
        if (context.Request.Headers.TryGetValue("X-Tenant-Id", out var tenantIdHeader))
        {
            var tenantId = tenantIdHeader.FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantId))
            {
                return await ResolveTenantByIdAsync(tenantId, cancellationToken);
            }
        }

        if (context.Request.Headers.TryGetValue("X-Tenant-Domain", out var tenantDomainHeader))
        {
            var tenantDomain = tenantDomainHeader.FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantDomain))
            {
                return await ResolveTenantByDomainAsync(tenantDomain, cancellationToken);
            }
        }

        return null;
    }

    private async Task<Tenant?> ResolveTenantFromSubdomainAsync(HttpContext context, CancellationToken cancellationToken)
    {
        var host = context.Request.Host.Host;
        if (string.IsNullOrEmpty(host))
        {
            return null;
        }

        // Extract subdomain (e.g., "tenant1.api.example.com" -> "tenant1")
        var parts = host.Split('.');
        if (parts.Length >= 3)
        {
            var subdomain = parts[0];
            if (!string.IsNullOrEmpty(subdomain) && subdomain != "www" && subdomain != "api")
            {
                return await ResolveTenantByDomainAsync(subdomain, cancellationToken);
            }
        }

        return null;
    }

    private async Task<Tenant?> ResolveTenantFromJwtAsync(HttpContext context, CancellationToken cancellationToken)
    {
        if (context.User?.Identity?.IsAuthenticated == true)
        {
            var tenantIdClaim = context.User.FindFirst("tenant_id")?.Value;
            if (!string.IsNullOrEmpty(tenantIdClaim))
            {
                return await ResolveTenantByIdAsync(tenantIdClaim, cancellationToken);
            }

            var tenantDomainClaim = context.User.FindFirst("tenant_domain")?.Value;
            if (!string.IsNullOrEmpty(tenantDomainClaim))
            {
                return await ResolveTenantByDomainAsync(tenantDomainClaim, cancellationToken);
            }
        }

        return null;
    }
}
