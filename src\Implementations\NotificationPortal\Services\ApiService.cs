using NotificationPortal.Models;
using System.Text.Json;
using System.Text;

namespace NotificationPortal.Services;

public interface IApiService
{
    Task<T?> GetAsync<T>(string endpoint);
    Task<T?> PostAsync<T>(string endpoint, object? data = null);
    Task<T?> PutAsync<T>(string endpoint, object? data = null);
    Task<bool> DeleteAsync(string endpoint);
    void SetTenant(string tenantId);
    void SetAuthToken(string token);
}

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ApiService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    private string? _currentTenantId;
    private string? _authToken;

    public ApiService(HttpClient httpClient, ILogger<ApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };

        // Configure base address - this should point to your API
        _httpClient.BaseAddress = new Uri("http://localhost:5120/");
    }

    public void SetTenant(string tenantId)
    {
        _currentTenantId = tenantId;
        UpdateHeaders();
    }

    public void SetAuthToken(string token)
    {
        _authToken = token;
        UpdateHeaders();
    }

    private void UpdateHeaders()
    {
        _httpClient.DefaultRequestHeaders.Clear();
        
        if (!string.IsNullOrEmpty(_authToken))
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_authToken}");
        }
        
        if (!string.IsNullOrEmpty(_currentTenantId))
        {
            _httpClient.DefaultRequestHeaders.Add("X-Tenant-Id", _currentTenantId);
        }
    }

    public async Task<T?> GetAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.GetAsync(endpoint);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(content, _jsonOptions);
            }
            
            _logger.LogWarning("GET {Endpoint} failed with status {StatusCode}", endpoint, response.StatusCode);
            return default;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling GET {Endpoint}", endpoint);
            return default;
        }
    }

    public async Task<T?> PostAsync<T>(string endpoint, object? data = null)
    {
        try
        {
            var content = data != null 
                ? new StringContent(JsonSerializer.Serialize(data, _jsonOptions), Encoding.UTF8, "application/json")
                : null;

            var response = await _httpClient.PostAsync(endpoint, content);
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(responseContent, _jsonOptions);
            }
            
            _logger.LogWarning("POST {Endpoint} failed with status {StatusCode}", endpoint, response.StatusCode);
            return default;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling POST {Endpoint}", endpoint);
            return default;
        }
    }

    public async Task<T?> PutAsync<T>(string endpoint, object? data = null)
    {
        try
        {
            var content = data != null 
                ? new StringContent(JsonSerializer.Serialize(data, _jsonOptions), Encoding.UTF8, "application/json")
                : null;

            var response = await _httpClient.PutAsync(endpoint, content);
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(responseContent, _jsonOptions);
            }
            
            _logger.LogWarning("PUT {Endpoint} failed with status {StatusCode}", endpoint, response.StatusCode);
            return default;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling PUT {Endpoint}", endpoint);
            return default;
        }
    }

    public async Task<bool> DeleteAsync(string endpoint)
    {
        try
        {
            var response = await _httpClient.DeleteAsync(endpoint);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling DELETE {Endpoint}", endpoint);
            return false;
        }
    }
}

public interface ITenantService
{
    Task<List<Tenant>> GetTenantsAsync();
    Task<Tenant?> GetTenantAsync(string tenantId);
    Task<Tenant?> CreateTenantAsync(Tenant tenant);
    Task<Tenant?> UpdateTenantAsync(string tenantId, Tenant tenant);
    Task<bool> DeleteTenantAsync(string tenantId);
}

public class TenantService : ITenantService
{
    private readonly IApiService _apiService;

    public TenantService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<Tenant>> GetTenantsAsync()
    {
        var response = await _apiService.GetAsync<dynamic>("api/tenants");
        // Parse the response and convert to List<Tenant>
        // This is a simplified implementation
        return new List<Tenant>
        {
            new Tenant
            {
                Id = "default",
                Name = "Default Tenant",
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-30)
            }
        };
    }

    public async Task<Tenant?> GetTenantAsync(string tenantId)
    {
        return await _apiService.GetAsync<Tenant>($"api/tenants/{tenantId}");
    }

    public async Task<Tenant?> CreateTenantAsync(Tenant tenant)
    {
        return await _apiService.PostAsync<Tenant>("api/tenants", tenant);
    }

    public async Task<Tenant?> UpdateTenantAsync(string tenantId, Tenant tenant)
    {
        return await _apiService.PutAsync<Tenant>($"api/tenants/{tenantId}", tenant);
    }

    public async Task<bool> DeleteTenantAsync(string tenantId)
    {
        return await _apiService.DeleteAsync($"api/tenants/{tenantId}");
    }
}

public interface IPluginService
{
    Task<List<Plugin>> GetPluginsAsync();
    Task<Plugin?> GetPluginAsync(string pluginName);
    Task<bool> EnablePluginAsync(string pluginName);
    Task<bool> DisablePluginAsync(string pluginName);
    Task<bool> UpdatePluginConfigAsync(string pluginName, Dictionary<string, object> config);
}

public class PluginService : IPluginService
{
    private readonly IApiService _apiService;

    public PluginService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<Plugin>> GetPluginsAsync()
    {
        var response = await _apiService.GetAsync<dynamic>("api/plugins");
        // Parse and convert to List<Plugin>
        // Simplified implementation
        return new List<Plugin>
        {
            new Plugin
            {
                Name = "SendGrid",
                Version = "1.0.0",
                Type = "Email",
                Provider = "SendGrid",
                IsEnabled = true,
                IsLoaded = true,
                LoadedAt = DateTime.UtcNow.AddHours(-2)
            },
            new Plugin
            {
                Name = "Twilio",
                Version = "1.0.0",
                Type = "SMS",
                Provider = "Twilio",
                IsEnabled = true,
                IsLoaded = true,
                LoadedAt = DateTime.UtcNow.AddHours(-2)
            }
        };
    }

    public async Task<Plugin?> GetPluginAsync(string pluginName)
    {
        return await _apiService.GetAsync<Plugin>($"api/plugins/{pluginName}");
    }

    public async Task<bool> EnablePluginAsync(string pluginName)
    {
        var response = await _apiService.PostAsync<dynamic>($"api/plugins/{pluginName}/enable");
        return response != null;
    }

    public async Task<bool> DisablePluginAsync(string pluginName)
    {
        var response = await _apiService.PostAsync<dynamic>($"api/plugins/{pluginName}/disable");
        return response != null;
    }

    public async Task<bool> UpdatePluginConfigAsync(string pluginName, Dictionary<string, object> config)
    {
        var response = await _apiService.PutAsync<dynamic>($"api/plugins/{pluginName}/config", new { configuration = config });
        return response != null;
    }
}
