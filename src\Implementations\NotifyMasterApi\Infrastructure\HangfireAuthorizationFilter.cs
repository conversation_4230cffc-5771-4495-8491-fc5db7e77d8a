using Hangfire.Dashboard;

namespace NotifyMasterApi.Infrastructure;

/// <summary>
/// Authorization filter for Hangfire dashboard
/// </summary>
public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
{
    public bool Authorize(DashboardContext context)
    {
        var httpContext = context.GetHttpContext();
        
        // In development, allow access
        if (httpContext.RequestServices.GetRequiredService<IWebHostEnvironment>().IsDevelopment())
        {
            return true;
        }

        // Check if user is authenticated and has system admin role
        if (httpContext.User.Identity?.IsAuthenticated == true)
        {
            return httpContext.User.IsInRole("SuperAdmin");
        }

        return false;
    }
}
